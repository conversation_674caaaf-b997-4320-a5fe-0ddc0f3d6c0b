user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log;
pid /run/nginx.pid;

# Load dynamic modules. See /usr/share/doc/nginx/README.dynamic.
include /usr/share/nginx/modules/*.conf;

events {
    worker_connections 1024;
}

http {
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Load modular configuration files from the /etc/nginx/conf.d directory.
    include /etc/nginx/conf.d/*.conf;

    server {
        listen 80 default_server;
        listen [::]:80 default_server;
        server_name ************ localhost _;
        
        # 根路径测试
        location / {
            return 200 "Nginx is working! Server: $server_name\n";
            add_header Content-Type text/plain;
        }

        # 代理 /lab 到后端 JupyterLab 服务
        location /lab {
            # 代理到后端服务
            proxy_pass http://127.0.0.1:8810;
            proxy_http_version 1.1;
            
            # 基础代理头
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;
            
            # WebSocket 支持
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            
            # 禁用缓冲，适合实时应用
            proxy_buffering off;
            proxy_cache_bypass $http_upgrade;
            
            # 超时设置
            proxy_connect_timeout 60s;
            proxy_send_timeout 120s;
            proxy_read_timeout 120s;
            
            # 调试头（可选）
            add_header X-Debug-Backend "127.0.0.1:8810" always;
        }

        # 代理 /login 到后端（处理登录重定向）
        location /login {
            proxy_pass http://127.0.0.1:8810;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 代理 /api 到后端（API 请求）
        location /api {
            proxy_pass http://127.0.0.1:8810;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 代理静态资源
        location /static {
            proxy_pass http://127.0.0.1:8810;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
        }

        error_page 404 /404.html;
        location = /404.html {
            return 404 "Page not found\n";
            add_header Content-Type text/plain;
        }

        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            return 500 "Server error\n";
            add_header Content-Type text/plain;
        }
    }
}
