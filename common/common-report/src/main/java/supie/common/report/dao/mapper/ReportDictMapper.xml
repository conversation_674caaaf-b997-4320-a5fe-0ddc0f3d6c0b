<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="supie.common.report.dao.ReportDictMapper">
    <resultMap id="BaseResultMap" type="supie.common.report.model.ReportDict">
        <id column="dict_id" jdbcType="BIGINT" property="dictId"/>
        <result column="app_code" jdbcType="VARCHAR" property="appCode"/>
        <result column="dict_name" jdbcType="VARCHAR" property="dictName"/>
        <result column="dict_type" jdbcType="INTEGER" property="dictType"/>
        <result column="dblink_id" jdbcType="BIGINT" property="dblinkId"/>
        <result column="table_name" jdbcType="VARCHAR" property="tableName"/>
        <result column="dict_code" jdbcType="VARCHAR" property="dictCode"/>
        <result column="key_column_name" jdbcType="VARCHAR" property="keyColumnName"/>
        <result column="parent_key_column_name" jdbcType="VARCHAR" property="parentKeyColumnName"/>
        <result column="value_column_name" jdbcType="VARCHAR" property="valueColumnName"/>
        <result column="deleted_column_name" jdbcType="VARCHAR" property="deletedColumnName"/>
        <result column="tenant_filter_column_name" jdbcType="VARCHAR" property="tenantFilterColumnName"/>
        <result column="tree_flag" jdbcType="BOOLEAN" property="treeFlag"/>
        <result column="dict_list_url" jdbcType="VARCHAR" property="dictListUrl"/>
        <result column="dict_ids_url" jdbcType="VARCHAR" property="dictIdsUrl"/>
        <result column="dict_data_json" jdbcType="LONGVARCHAR" property="dictDataJson"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
    </resultMap>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="supie.common.report.dao.ReportDictMapper.inputFilterRef"/>
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="reportDictFilter != null">
            <if test="reportDictFilter.appCode == null">
                AND zz_report_dict.app_code IS NULL
            </if>
            <if test="reportDictFilter.appCode != null">
                AND zz_report_dict.app_code = #{reportDictFilter.appCode}
            </if>
            <if test="reportDictFilter.dictType != null">
                AND zz_report_dict.dict_type = #{reportDictFilter.dictType}
            </if>
            <if test="reportDictFilter.dblinkId != null">
                AND zz_report_dict.dblink_id = #{reportDictFilter.dblinkId}
            </if>
            <if test="reportDictFilter.dictCode != null and reportDictFilter.dictCode != ''">
                AND zz_report_dict.dict_code = #{reportDictFilter.dictCode}
            </if>
            <if test="reportDictFilter.createUserId != null">
                AND zz_report_dict.create_user_id = #{reportDictFilter.createUserId}
            </if>
        </if>
    </sql>

    <select id="getReportDictList" resultMap="BaseResultMap" parameterType="supie.common.report.model.ReportDict">
        SELECT * FROM zz_report_dict
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
