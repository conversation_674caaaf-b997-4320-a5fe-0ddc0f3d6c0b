package supie.common.report.dao;

import supie.common.core.base.dao.BaseDaoMapper;
import supie.common.report.model.ReportDatasetColumn;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据集字段数据操作访问接口。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
public interface ReportDatasetColumnMapper extends BaseDaoMapper<ReportDatasetColumn> {

    /**
     * 批量插入对象列表。
     *
     * @param reportDatasetColumnList 新增对象列表。
     */
    void insertList(List<ReportDatasetColumn> reportDatasetColumnList);

    /**
     * 获取过滤后的对象列表。
     *
     * @param reportDatasetColumnFilter 主表过滤对象。
     * @return 对象列表。
     */
    List<ReportDatasetColumn> getReportDatasetColumnList(
            @Param("reportDatasetColumnFilter") ReportDatasetColumn reportDatasetColumnFilter);
}
