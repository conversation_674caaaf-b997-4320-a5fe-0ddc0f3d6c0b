<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="supie.common.report.dao.ReportDatasetRelationMapper">
    <resultMap id="BaseResultMap" type="supie.common.report.model.ReportDatasetRelation">
        <id column="relation_id" jdbcType="BIGINT" property="relationId"/>
        <result column="app_code" jdbcType="VARCHAR" property="appCode"/>
        <result column="variable_name" jdbcType="VARCHAR" property="variableName"/>
        <result column="master_dataset_id" jdbcType="BIGINT" property="masterDatasetId"/>
        <result column="master_column_id" jdbcType="BIGINT" property="masterColumnId"/>
        <result column="slave_dataset_id" jdbcType="BIGINT" property="slaveDatasetId"/>
        <result column="slave_column_id" jdbcType="BIGINT" property="slaveColumnId"/>
        <result column="relation_type" jdbcType="INTEGER" property="relationType"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
    </resultMap>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="supie.common.report.dao.ReportDatasetRelationMapper.inputFilterRef"/>
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="reportDatasetRelationFilter != null">
            <if test="reportDatasetRelationFilter.appCode == null">
                AND zz_report_dataset_relation.app_code IS NULL
            </if>
            <if test="reportDatasetRelationFilter.appCode != null">
                AND zz_report_dataset_relation.app_code = #{reportDatasetRelationFilter.appCode}
            </if>
            <if test="reportDatasetRelationFilter.masterDatasetId != null">
                AND zz_report_dataset_relation.master_dataset_id = #{reportDatasetRelationFilter.masterDatasetId}
            </if>
            <if test="reportDatasetRelationFilter.slaveDatasetId != null">
                AND zz_report_dataset_relation.slave_dataset_id = #{reportDatasetRelationFilter.slaveDatasetId}
            </if>
        </if>
    </sql>

    <select id="getReportDatasetRelationList" resultMap="BaseResultMap" parameterType="supie.common.report.model.ReportDatasetRelation">
        SELECT * FROM zz_report_dataset_relation
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
