package supie.common.report.object;

import cn.hutool.core.collection.CollUtil;
import supie.common.dbutil.object.GenericResultSet;
import supie.common.dbutil.object.SqlResultSet;
import supie.common.report.model.ReportDatasetColumn;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 查询结果集对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
public class ReportResultSet<T> extends GenericResultSet<ReportDatasetColumn, T> {

    public ReportResultSet(List<ReportDatasetColumn> columnMetaList, List<T> dataList) {
        super(columnMetaList, dataList);
    }

    public static <D> ReportResultSet<D> fromSqlResultSet(
            SqlResultSet<D> source, List<ReportDatasetColumn> columnList) {
        ReportResultSet<D> resultSet = new ReportResultSet<>();
        if (source != null) {
            resultSet.setDataList(source.getDataList());
            resultSet.setTotalCount(source.getTotalCount());
            resultSet.setColumnMetaList(columnList);
        }
        return resultSet;
    }

    public static <D> boolean isEmpty(ReportResultSet<D> rs) {
        return rs == null || CollUtil.isEmpty(rs.getDataList());
    }
}
