package supie.common.report.dao;

import supie.common.core.base.dao.BaseDaoMapper;
import supie.common.report.model.ReportPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 报表页面数据操作访问接口。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
public interface ReportPageMapper extends BaseDaoMapper<ReportPage> {

    /**
     * 获取过滤后的对象列表。
     *
     * @param reportPageFilter 主表过滤对象。
     * @param orderBy 排序字符串，order by从句的参数。
     * @return 对象列表。
     */
    List<ReportPage> getReportPageList(
            @Param("reportPageFilter") ReportPage reportPageFilter, @Param("orderBy") String orderBy);
}
