package supie.common.flow.object;

import lombok.Data;

/**
 * 流程任务的业务配置数据。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Data
public class FlowBusinessOperationConfig {

    /**
     * 执行类型为SQL。
     */
    public static final String ACTION_TYPE_SQL = "SQL";
    /**
     * 执行类型为HTTP。
     */
    public static final String ACTION_TYPE_HTTP = "HTTP";
    /**
     * 执行类型为EL表达式。
     */
    public static final String ACTION_TYPE_EXPRESSION = "EXPRESSION";

    /**
     * 操作类型。enter_task/agree/refuse/reject/revoke/multi_sign
     */
    private String operationType;
    /**
     * 是否为在线表单工作流。
     */
    private Boolean forOnlineTable;
    /**
     * 在线表单所在数据库的Id。
     */
    private Long dblinkId;
    /**
     * 执行类型。SQL/HTTP/EXPRESSION。
     */
    private String actionType;
    /**
     * SQL更新语句。
     */
    private String sql;
    /**
     * EL表达式。
     */
    private String expression;
    /**
     * HTTP请求信息。
     */
    private HttpRequestInfo httpRequestInfo;
    /**
     * HTTP应答数据。
     */
    private HttpResponseData httpResponnseData;
    /**
     * 流程定义标识。
     */
    private String processDefinitionKey;
}
