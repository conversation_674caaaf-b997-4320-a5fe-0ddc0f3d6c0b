<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="supie.common.flow.dao.FlowApiRequestLogMapper">
    <resultMap id="BaseResultMap" type="supie.common.flow.model.FlowApiRequestLog">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="request_id" jdbcType="VARCHAR" property="requestId"/>
        <result column="process_instance_id" jdbcType="VARCHAR" property="processInstanceId"/>
        <result column="task_id" jdbcType="VARCHAR" property="taskId"/>
        <result column="task_key" jdbcType="VARCHAR" property="taskKey"/>
        <result column="business_key" jdbcType="VARCHAR" property="businessKey"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
    </resultMap>
</mapper>
