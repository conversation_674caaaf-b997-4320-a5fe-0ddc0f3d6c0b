package supie.common.flow.listener;

import supie.common.core.util.ApplicationContextHolder;
import supie.common.flow.model.FlowTaskComment;
import supie.common.flow.service.FlowTaskCommentService;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.TaskService;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;

/**
 * 将其他任务提交者赋值给当前任务审批者的监听器。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Slf4j
public class FlowOtherTaskSubmitterListener implements TaskListener {

    private final transient TaskService taskService =
            ApplicationContextHolder.getBean(TaskService.class);
    private final transient FlowTaskCommentService flowTaskCommentService =
            ApplicationContextHolder.getBean(FlowTaskCommentService.class);

    @Override
    public void notify(DelegateTask t) {
        FlowTaskComment comment =
                flowTaskCommentService.getLatestFlowTaskComment(t.getProcessInstanceId(), t.getAssignee());
        if (comment != null) {
            taskService.setAssignee(t.getId(), comment.getCreateLoginName());
        }
    }
}
