<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>common</artifactId>
        <groupId>supie</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>common-online</artifactId>
    <version>1.0.0</version>
    <name>common-online</name>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>supie</groupId>
            <artifactId>common-satoken</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>supie</groupId>
            <artifactId>common-dbutil</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>supie</groupId>
            <artifactId>common-dict</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>supie</groupId>
            <artifactId>common-datafilter</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>supie</groupId>
            <artifactId>common-redis</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>supie</groupId>
            <artifactId>common-sequence</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>supie</groupId>
            <artifactId>common-log</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>supie</groupId>
            <artifactId>common-minio</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>supie</groupId>
            <artifactId>common-swagger</artifactId>
            <version>1.0.0</version>
        </dependency>
    </dependencies>
</project>