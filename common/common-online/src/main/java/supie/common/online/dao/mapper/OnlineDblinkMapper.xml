<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="supie.common.online.dao.OnlineDblinkMapper">
    <resultMap id="BaseResultMap" type="supie.common.online.model.OnlineDblink">
        <id column="dblink_id" jdbcType="BIGINT" property="dblinkId"/>
        <result column="app_code" jdbcType="VARCHAR" property="appCode"/>
        <result column="dblink_name" jdbcType="VARCHAR" property="dblinkName"/>
        <result column="dblink_description" jdbcType="VARCHAR" property="dblinkDescription"/>
        <result column="configuration" jdbcType="VARCHAR" property="configuration"/>
        <result column="dblink_type" jdbcType="INTEGER" property="dblinkType"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
    </resultMap>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="supie.common.online.dao.OnlineDblinkMapper.inputFilterRef"/>
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="onlineDblinkFilter != null">
            <if test="onlineDblinkFilter.appCode == null">
                AND zz_online_dblink.app_code IS NULL
            </if>
            <if test="onlineDblinkFilter.appCode != null">
                AND zz_online_dblink.app_code = #{onlineDblinkFilter.appCode}
            </if>
            <if test="onlineDblinkFilter.dblinkType != null">
                AND zz_online_dblink.dblink_type = #{onlineDblinkFilter.dblinkType}
            </if>
        </if>
    </sql>

    <select id="getOnlineDblinkList" resultMap="BaseResultMap" parameterType="supie.common.online.model.OnlineDblink">
        SELECT * FROM zz_online_dblink
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
