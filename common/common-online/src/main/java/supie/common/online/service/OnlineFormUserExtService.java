package supie.common.online.service;

import supie.common.core.base.service.IBaseService;
import supie.common.online.model.OnlineFormUserExt;

/**
 * 在线表单用户扩展数据操作服务接口。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
public interface OnlineFormUserExtService extends IBaseService<OnlineFormUserExt, Long> {

    /**
     * 保存新增或者更新对象。
     *
     * @param data 数据对象。
     * @return 返回对象。
     */
    OnlineFormUserExt saveNewOrUpdate(OnlineFormUserExt data);

    /**
     * 获取指定在线表单的关联用户扩展数据。
     *
     * @param formId 在线表单Id。
     * @return 返回关联用户扩展数据。
     */
    OnlineFormUserExt getByFormId(Long formId);
}
