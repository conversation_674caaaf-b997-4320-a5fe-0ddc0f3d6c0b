user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log;
pid /run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    server {
        listen 80;
        server_name ************;

        # 测试根路径
        location / {
            return 200 "Nginx is working!\n";
            add_header Content-Type text/plain;
        }

        # 代理 /lab 到后端服务
        location /lab {
            proxy_pass http://127.0.0.1:8810;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 添加调试头
            add_header X-Debug-Proxy "Proxied to 8810" always;
        }

        # 测试路径
        location /test {
            return 200 "Test endpoint working!\n";
            add_header Content-Type text/plain;
        }
    }
}
