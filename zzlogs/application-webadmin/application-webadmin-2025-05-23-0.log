[INFO ] [2025-05-23 15:09:09] T:[] S:[] U:[] [background-preinit] ==> HV000001: Hibernate Validator 8.0.1.Final
[INFO ] [2025-05-23 15:09:10] T:[] S:[] U:[] [main] ==> Starting WebAdminApplication using Java 17.0.4 with PID 16108 (D:\CompanySupie\gy_sch_be\application-webadmin\target\classes started by Superhero in D:\CompanySupie\gy_sch_be)
[INFO ] [2025-05-23 15:09:10] T:[] S:[] U:[] [main] ==> The following 1 profile is active: "dev"
[INFO ] [2025-05-23 15:09:18] T:[] S:[] U:[] [main] ==> Multiple Spring Data modules found, entering strict repository configuration mode
[INFO ] [2025-05-23 15:09:18] T:[] S:[] U:[] [main] ==> Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[INFO ] [2025-05-23 15:09:18] T:[] S:[] U:[] [main] ==> Finished Spring Data repository scanning in 101 ms. Found 0 Redis repository interfaces.
[INFO ] [2025-05-23 15:09:19] T:[] S:[] U:[] [main] ==> Bean 'commonWebMvcConfig' of type [supie.common.core.config.CommonWebMvcConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO ] [2025-05-23 15:09:22] T:[] S:[] U:[] [main] ==> Tomcat initialized with port(s): 8082 (http)
[INFO ] [2025-05-23 15:09:22] T:[] S:[] U:[] [main] ==> Initializing ProtocolHandler ["http-nio-8082"]
[INFO ] [2025-05-23 15:09:22] T:[] S:[] U:[] [main] ==> Starting service [Tomcat]
[INFO ] [2025-05-23 15:09:22] T:[] S:[] U:[] [main] ==> Starting Servlet engine: [Apache Tomcat/10.1.16]
[INFO ] [2025-05-23 15:09:23] T:[] S:[] U:[] [main] ==> Initializing Spring embedded WebApplicationContext
[INFO ] [2025-05-23 15:09:23] T:[] S:[] U:[] [main] ==> Root WebApplicationContext: initialization completed in 11687 ms
[ERROR] [2025-05-23 15:09:23] T:[] S:[] U:[] [main] ==> For security constraints with URL pattern [/*] only the HTTP methods [TRACE HEAD DELETE SEARCH PROPFIND COPY PUT PATCH] are covered. All other methods are uncovered.
[INFO ] [2025-05-23 15:09:24] T:[] S:[] U:[] [main] ==> Redisson 3.15.4
[WARN ] [2025-05-23 15:09:25] T:[] S:[] U:[] [main] ==> Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'schComputeDeviceController': Unsatisfied dependency expressed through field 'schComputeDeviceService': Error creating bean with name 'schComputeDeviceService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'schComputeDeviceMapper' defined in file [D:\CompanySupie\gy_sch_be\application-webadmin\target\classes\supie\webadmin\app\dao\SchComputeDeviceMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration': Failed to instantiate [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration]: Constructor threw exception
[INFO ] [2025-05-23 15:09:25] T:[] S:[] U:[] [main] ==> Stopping service [Tomcat]
[INFO ] [2025-05-23 15:09:26] T:[] S:[] U:[] [main] ==> 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
[ERROR] [2025-05-23 15:09:26] T:[] S:[] U:[] [main] ==> Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'schComputeDeviceController': Unsatisfied dependency expressed through field 'schComputeDeviceService': Error creating bean with name 'schComputeDeviceService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'schComputeDeviceMapper' defined in file [D:\CompanySupie\gy_sch_be\application-webadmin\target\classes\supie\webadmin\app\dao\SchComputeDeviceMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration': Failed to instantiate [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration]: Constructor threw exception
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:771)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:751)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:492)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1416)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:597)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:520)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:973)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:950)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:616)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:738)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:440)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at supie.webadmin.WebAdminApplication.main(WebAdminApplication.java:21)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'schComputeDeviceService': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'schComputeDeviceMapper' defined in file [D:\CompanySupie\gy_sch_be\application-webadmin\target\classes\supie\webadmin\app\dao\SchComputeDeviceMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration': Failed to instantiate [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration]: Constructor threw exception
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:771)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:751)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:492)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1416)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:597)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:520)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1417)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1337)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:768)
	... 20 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'schComputeDeviceMapper' defined in file [D:\CompanySupie\gy_sch_be\application-webadmin\target\classes\supie\webadmin\app\dao\SchComputeDeviceMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration': Failed to instantiate [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration]: Constructor threw exception
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1513)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1407)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:597)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:520)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1417)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1337)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:768)
	... 34 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration': Failed to instantiate [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration]: Constructor threw exception
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:321)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:309)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1189)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:560)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:520)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:412)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1332)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1162)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:560)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:520)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1417)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1337)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1498)
	... 45 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration]: Constructor threw exception
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:225)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:110)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:318)
	... 67 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'mybatisDataFilterInterceptor': Unsatisfied dependency expressed through field 'redissonClient': Error creating bean with name 'redissonClient' defined in class path resource [supie/common/redis/config/RedissonConfig.class]: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redissonClient' threw exception with message: Unable to connect to Redis server: /*************:30051
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:771)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:751)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:492)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1416)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:597)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:520)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1633)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1597)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1465)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.getIfAvailable(DefaultListableBeanFactory.java:2056)
	at com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.<init>(MybatisPlusAutoConfiguration.java:145)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:212)
	... 69 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redissonClient' defined in class path resource [supie/common/redis/config/RedissonConfig.class]: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redissonClient' threw exception with message: Unable to connect to Redis server: /*************:30051
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:654)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:488)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1332)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1162)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:560)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:520)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1417)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1337)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:768)
	... 92 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redissonClient' threw exception with message: Unable to connect to Redis server: /*************:30051
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:171)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:650)
	... 105 common frames omitted
Caused by: org.redisson.client.RedisConnectionException: Unable to connect to Redis server: /*************:30051
	at org.redisson.connection.pool.ConnectionPool$1.lambda$run$0(ConnectionPool.java:158)
	at org.redisson.misc.RedissonPromise.lambda$onComplete$0(RedissonPromise.java:183)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at org.redisson.misc.RedissonPromise.tryFailure(RedissonPromise.java:96)
	at org.redisson.connection.pool.ConnectionPool.promiseFailure(ConnectionPool.java:328)
	at org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:294)
	at org.redisson.misc.RedissonPromise.lambda$onComplete$0(RedissonPromise.java:183)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at org.redisson.misc.RedissonPromise.tryFailure(RedissonPromise.java:96)
	at org.redisson.client.RedisClient$2$1.run(RedisClient.java:241)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: org.redisson.client.RedisAuthRequiredException: NOAUTH Authentication required.. channel: [id: 0x58716eea, L:/192.168.0.20:13748 - R:/*************:30051] data: CommandData [promise=RedissonPromise [promise=ImmediateEventExecutor$ImmediatePromise@28ffad8c(incomplete)], command=(SELECT), params=[3], codec=null]
	at org.redisson.client.handler.CommandDecoder.decode(CommandDecoder.java:335)
	at org.redisson.client.handler.CommandDecoder.decodeCommand(CommandDecoder.java:177)
	at org.redisson.client.handler.CommandDecoder.decode(CommandDecoder.java:116)
	at org.redisson.client.handler.CommandDecoder.decode(CommandDecoder.java:101)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:529)
	at io.netty.handler.codec.ReplayingDecoder.callDecode(ReplayingDecoder.java:366)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	... 4 common frames omitted
[INFO ] [2025-05-23 15:11:49] T:[] S:[] U:[] [background-preinit] ==> HV000001: Hibernate Validator 8.0.1.Final
[INFO ] [2025-05-23 15:11:49] T:[] S:[] U:[] [main] ==> Starting WebAdminApplication using Java 17.0.4 with PID 23700 (D:\CompanySupie\gy_sch_be\application-webadmin\target\classes started by Superhero in D:\CompanySupie\gy_sch_be)
[INFO ] [2025-05-23 15:11:49] T:[] S:[] U:[] [main] ==> The following 1 profile is active: "dev"
[INFO ] [2025-05-23 15:11:57] T:[] S:[] U:[] [main] ==> Multiple Spring Data modules found, entering strict repository configuration mode
[INFO ] [2025-05-23 15:11:57] T:[] S:[] U:[] [main] ==> Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[INFO ] [2025-05-23 15:11:57] T:[] S:[] U:[] [main] ==> Finished Spring Data repository scanning in 112 ms. Found 0 Redis repository interfaces.
[INFO ] [2025-05-23 15:11:58] T:[] S:[] U:[] [main] ==> Bean 'commonWebMvcConfig' of type [supie.common.core.config.CommonWebMvcConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO ] [2025-05-23 15:12:01] T:[] S:[] U:[] [main] ==> Tomcat initialized with port(s): 8082 (http)
[INFO ] [2025-05-23 15:12:01] T:[] S:[] U:[] [main] ==> Initializing ProtocolHandler ["http-nio-8082"]
[INFO ] [2025-05-23 15:12:01] T:[] S:[] U:[] [main] ==> Starting service [Tomcat]
[INFO ] [2025-05-23 15:12:01] T:[] S:[] U:[] [main] ==> Starting Servlet engine: [Apache Tomcat/10.1.16]
[INFO ] [2025-05-23 15:12:02] T:[] S:[] U:[] [main] ==> Initializing Spring embedded WebApplicationContext
[INFO ] [2025-05-23 15:12:02] T:[] S:[] U:[] [main] ==> Root WebApplicationContext: initialization completed in 11757 ms
[ERROR] [2025-05-23 15:12:03] T:[] S:[] U:[] [main] ==> For security constraints with URL pattern [/*] only the HTTP methods [TRACE HEAD DELETE SEARCH PROPFIND COPY PUT PATCH] are covered. All other methods are uncovered.
[INFO ] [2025-05-23 15:12:03] T:[] S:[] U:[] [main] ==> Redisson 3.15.4
[INFO ] [2025-05-23 15:12:04] T:[] S:[] U:[] [redisson-netty-2-13] ==> 1 connections initialized for /*************:30051
[INFO ] [2025-05-23 15:12:04] T:[] S:[] U:[] [redisson-netty-2-8] ==> 5 connections initialized for /*************:30051
[INFO ] [2025-05-23 15:12:11] T:[] S:[] U:[] [main] ==> {dataSource-1,application-webadmin} inited
[INFO ] [2025-05-23 15:12:17] T:[] S:[] U:[] [main] ==> {dataSource-2,application-webadmin} inited
[INFO ] [2025-05-23 15:12:23] T:[] S:[] U:[] [main] ==> {dataSource-3,application-webadmin} inited
[INFO ] [2025-05-23 15:12:28] T:[] S:[] U:[] [main] ==> {dataSource-4,application-webadmin} inited
[INFO ] [2025-05-23 15:12:34] T:[] S:[] U:[] [main] ==> {dataSource-5,application-webadmin} inited
[WARN ] [2025-05-23 15:12:36] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysDataPermDept".
[WARN ] [2025-05-23 15:12:36] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysDataPermDept ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:12:36] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysDataPermMenu".
[WARN ] [2025-05-23 15:12:36] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysDataPermMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:12:36] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysDataPermUser".
[WARN ] [2025-05-23 15:12:36] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysDataPermUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:12:37] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysDeptRelation".
[WARN ] [2025-05-23 15:12:37] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysDeptRelation ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:12:37] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysRoleMenu".
[WARN ] [2025-05-23 15:12:37] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysRoleMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:12:37] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysUserPost".
[WARN ] [2025-05-23 15:12:37] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysUserPost ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:12:37] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysUserRole".
[WARN ] [2025-05-23 15:12:37] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:12:37] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.mobile.model.MobileEntryDataPerm".
[WARN ] [2025-05-23 15:12:37] T:[] S:[] U:[] [main] ==> class supie.common.mobile.model.MobileEntryDataPerm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:12:37] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.mobile.model.MobileEntryRole".
[WARN ] [2025-05-23 15:12:37] T:[] S:[] U:[] [main] ==> class supie.common.mobile.model.MobileEntryRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:12:37] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.online.model.OnlineColumnRule".
[WARN ] [2025-05-23 15:12:37] T:[] S:[] U:[] [main] ==> class supie.common.online.model.OnlineColumnRule ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:12:39] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.flow.model.FlowTaskExt".
[WARN ] [2025-05-23 15:12:39] T:[] S:[] U:[] [main] ==> class supie.common.flow.model.FlowTaskExt ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:12:40] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.report.model.ReportTenantDataset".
[WARN ] [2025-05-23 15:12:40] T:[] S:[] U:[] [main] ==> class supie.common.report.model.ReportTenantDataset ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[INFO ] [2025-05-23 15:12:54] T:[] S:[] U:[] [main] ==> No deployment resources were found for autodeployment
[INFO ] [2025-05-23 15:12:54] T:[] S:[] U:[] [main] ==> No deployment resources were found for autodeployment
[INFO ] [2025-05-23 15:12:56] T:[] S:[] U:[] [main] ==> Found 1 auto-discoverable Process Engine Configurator
[INFO ] [2025-05-23 15:12:56] T:[] S:[] U:[] [main] ==> Found 3 Engine Configurators in total:
[INFO ] [2025-05-23 15:12:56] T:[] S:[] U:[] [main] ==> class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-05-23 15:12:56] T:[] S:[] U:[] [main] ==> class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
[INFO ] [2025-05-23 15:12:56] T:[] S:[] U:[] [main] ==> class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
[INFO ] [2025-05-23 15:12:56] T:[] S:[] U:[] [main] ==> Executing beforeInit() of class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-05-23 15:12:56] T:[] S:[] U:[] [main] ==> Executing beforeInit() of class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
[INFO ] [2025-05-23 15:12:56] T:[] S:[] U:[] [main] ==> Executing beforeInit() of class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
[INFO ] [2025-05-23 15:12:58] T:[] S:[] U:[] [main] ==> Executing configure() of class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-05-23 15:12:58] T:[] S:[] U:[] [main] ==> Executing configure() of class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
[INFO ] [2025-05-23 15:12:58] T:[] S:[] U:[] [main] ==> Found 1 auto-discoverable Process Engine Configurator
[INFO ] [2025-05-23 15:12:58] T:[] S:[] U:[] [main] ==> Found 1 Engine Configurators in total:
[INFO ] [2025-05-23 15:12:58] T:[] S:[] U:[] [main] ==> class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-05-23 15:12:58] T:[] S:[] U:[] [main] ==> Executing beforeInit() of class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-05-23 15:12:58] T:[] S:[] U:[] [main] ==> Executing configure() of class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-05-23 15:12:59] T:[] S:[] U:[] [main] ==> INFO: An older version of the XSD is specified in one or more changelog's <databaseChangeLog> header. This can lead to unexpected outcomes. If a specific XSD is not required, please replace all XSD version references with "-latest". Learn more at https://docs.liquibase.com
[INFO ] [2025-05-23 15:13:00] T:[] S:[] U:[] [main] ==> Reading from gy_sch_dev.FLW_EV_DATABASECHANGELOG
[INFO ] [2025-05-23 15:13:00] T:[] S:[] U:[] [main] ==> Changelog query completed.
[INFO ] [2025-05-23 15:13:00] T:[] S:[] U:[] [main] ==> EventRegistryEngine default created
[INFO ] [2025-05-23 15:13:00] T:[] S:[] U:[] [main] ==> Executing configure() of class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
[INFO ] [2025-05-23 15:13:00] T:[] S:[] U:[] [main] ==> IdmEngine default created
[INFO ] [2025-05-23 15:13:01] T:[] S:[] U:[] [main] ==> ProcessEngine default created
[INFO ] [2025-05-23 15:13:01] T:[] S:[] U:[] [main] ==> Total of v5 deployments found: 0
[WARN ] [2025-05-23 15:13:05] T:[] S:[] U:[] [main] ==> Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'loginController': Unsatisfied dependency expressed through field 'flowOnlineOperationService': Error creating bean with name 'flowOnlineOperationService': Unsatisfied dependency expressed through field 'flowApiService': Error creating bean with name 'flowApiService': Unsatisfied dependency expressed through field 'flowEntryService': Error creating bean with name 'flowEntryService': Unsatisfied dependency expressed through field 'flowTaskExtService': Error creating bean with name 'flowTaskExtService': Unsatisfied dependency expressed through field 'flowBusinessHelper': Error creating bean with name 'flowBusinessHelper': Unsatisfied dependency expressed through field 'rocketMqTemplate': No qualifying bean of type 'org.apache.rocketmq.spring.core.RocketMQTemplate' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@org.springframework.beans.factory.annotation.Autowired(required=true)}
[INFO ] [2025-05-23 15:13:06] T:[] S:[] U:[] [main] ==> {dataSource-5} closing ...
[INFO ] [2025-05-23 15:13:06] T:[] S:[] U:[] [main] ==> {dataSource-5} closed
[INFO ] [2025-05-23 15:13:06] T:[] S:[] U:[] [main] ==> {dataSource-4} closing ...
[ERROR] [2025-05-23 15:13:06] T:[] S:[] U:[] [main] ==> unregister mbean error
javax.management.InstanceNotFoundException: com.alibaba.druid:type=DruidDataSource,id=application-webadmin
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.getMBean(DefaultMBeanServerInterceptor.java:1088)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.exclusiveUnregisterMBean(DefaultMBeanServerInterceptor.java:423)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.unregisterMBean(DefaultMBeanServerInterceptor.java:411)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.unregisterMBean(JmxMBeanServer.java:547)
	at com.alibaba.druid.stat.DruidDataSourceStatManager.removeDataSource(DruidDataSourceStatManager.java:191)
	at com.alibaba.druid.pool.DruidDataSource$2.run(DruidDataSource.java:2237)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:318)
	at com.alibaba.druid.pool.DruidDataSource.unregisterMbean(DruidDataSource.java:2234)
	at com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2191)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:221)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1189)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1182)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1118)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:629)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:738)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:440)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at supie.webadmin.WebAdminApplication.main(WebAdminApplication.java:21)
[INFO ] [2025-05-23 15:13:06] T:[] S:[] U:[] [main] ==> {dataSource-4} closed
[INFO ] [2025-05-23 15:13:06] T:[] S:[] U:[] [main] ==> {dataSource-3} closing ...
[ERROR] [2025-05-23 15:13:06] T:[] S:[] U:[] [main] ==> unregister mbean error
javax.management.InstanceNotFoundException: com.alibaba.druid:type=DruidDataSource,id=application-webadmin
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.getMBean(DefaultMBeanServerInterceptor.java:1088)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.exclusiveUnregisterMBean(DefaultMBeanServerInterceptor.java:423)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.unregisterMBean(DefaultMBeanServerInterceptor.java:411)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.unregisterMBean(JmxMBeanServer.java:547)
	at com.alibaba.druid.stat.DruidDataSourceStatManager.removeDataSource(DruidDataSourceStatManager.java:191)
	at com.alibaba.druid.pool.DruidDataSource$2.run(DruidDataSource.java:2237)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:318)
	at com.alibaba.druid.pool.DruidDataSource.unregisterMbean(DruidDataSource.java:2234)
	at com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2191)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:221)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1189)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1182)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1118)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:629)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:738)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:440)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at supie.webadmin.WebAdminApplication.main(WebAdminApplication.java:21)
[INFO ] [2025-05-23 15:13:06] T:[] S:[] U:[] [main] ==> {dataSource-3} closed
[INFO ] [2025-05-23 15:13:06] T:[] S:[] U:[] [main] ==> {dataSource-2} closing ...
[ERROR] [2025-05-23 15:13:06] T:[] S:[] U:[] [main] ==> unregister mbean error
javax.management.InstanceNotFoundException: com.alibaba.druid:type=DruidDataSource,id=application-webadmin
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.getMBean(DefaultMBeanServerInterceptor.java:1088)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.exclusiveUnregisterMBean(DefaultMBeanServerInterceptor.java:423)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.unregisterMBean(DefaultMBeanServerInterceptor.java:411)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.unregisterMBean(JmxMBeanServer.java:547)
	at com.alibaba.druid.stat.DruidDataSourceStatManager.removeDataSource(DruidDataSourceStatManager.java:191)
	at com.alibaba.druid.pool.DruidDataSource$2.run(DruidDataSource.java:2237)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:318)
	at com.alibaba.druid.pool.DruidDataSource.unregisterMbean(DruidDataSource.java:2234)
	at com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2191)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:221)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1189)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1182)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1118)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:629)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:738)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:440)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at supie.webadmin.WebAdminApplication.main(WebAdminApplication.java:21)
[INFO ] [2025-05-23 15:13:06] T:[] S:[] U:[] [main] ==> {dataSource-2} closed
[INFO ] [2025-05-23 15:13:06] T:[] S:[] U:[] [main] ==> {dataSource-1} closing ...
[ERROR] [2025-05-23 15:13:06] T:[] S:[] U:[] [main] ==> unregister mbean error
javax.management.InstanceNotFoundException: com.alibaba.druid:type=DruidDataSource,id=application-webadmin
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.getMBean(DefaultMBeanServerInterceptor.java:1088)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.exclusiveUnregisterMBean(DefaultMBeanServerInterceptor.java:423)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.unregisterMBean(DefaultMBeanServerInterceptor.java:411)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.unregisterMBean(JmxMBeanServer.java:547)
	at com.alibaba.druid.stat.DruidDataSourceStatManager.removeDataSource(DruidDataSourceStatManager.java:191)
	at com.alibaba.druid.pool.DruidDataSource$2.run(DruidDataSource.java:2237)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:318)
	at com.alibaba.druid.pool.DruidDataSource.unregisterMbean(DruidDataSource.java:2234)
	at com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2191)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:221)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1189)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1182)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1118)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:629)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:738)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:440)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at supie.webadmin.WebAdminApplication.main(WebAdminApplication.java:21)
[INFO ] [2025-05-23 15:13:06] T:[] S:[] U:[] [main] ==> {dataSource-1} closed
[INFO ] [2025-05-23 15:13:06] T:[] S:[] U:[] [main] ==> Stopping service [Tomcat]
[INFO ] [2025-05-23 15:13:06] T:[] S:[] U:[] [main] ==> 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
[ERROR] [2025-05-23 15:13:07] T:[] S:[] U:[] [main] ==> 

***************************
APPLICATION FAILED TO START
***************************

Description:

Field rocketMqTemplate in supie.common.flow.util.FlowBusinessHelper required a bean of type 'org.apache.rocketmq.spring.core.RocketMQTemplate' that could not be found.

The injection point has the following annotations:
	- @org.springframework.beans.factory.annotation.Autowired(required=true)


Action:

Consider defining a bean of type 'org.apache.rocketmq.spring.core.RocketMQTemplate' in your configuration.

[INFO ] [2025-05-23 15:23:47] T:[] S:[] U:[] [background-preinit] ==> HV000001: Hibernate Validator 8.0.1.Final
[INFO ] [2025-05-23 15:23:48] T:[] S:[] U:[] [main] ==> Starting WebAdminApplication using Java 17.0.4 with PID 8732 (D:\CompanySupie\gy_sch_be\application-webadmin\target\classes started by Superhero in D:\CompanySupie\gy_sch_be)
[INFO ] [2025-05-23 15:23:48] T:[] S:[] U:[] [main] ==> The following 1 profile is active: "dev"
[INFO ] [2025-05-23 15:23:54] T:[] S:[] U:[] [main] ==> Multiple Spring Data modules found, entering strict repository configuration mode
[INFO ] [2025-05-23 15:23:54] T:[] S:[] U:[] [main] ==> Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[INFO ] [2025-05-23 15:23:54] T:[] S:[] U:[] [main] ==> Finished Spring Data repository scanning in 70 ms. Found 0 Redis repository interfaces.
[INFO ] [2025-05-23 15:23:55] T:[] S:[] U:[] [main] ==> Bean 'commonWebMvcConfig' of type [supie.common.core.config.CommonWebMvcConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO ] [2025-05-23 15:23:58] T:[] S:[] U:[] [main] ==> Tomcat initialized with port(s): 8082 (http)
[INFO ] [2025-05-23 15:23:58] T:[] S:[] U:[] [main] ==> Initializing ProtocolHandler ["http-nio-8082"]
[INFO ] [2025-05-23 15:23:58] T:[] S:[] U:[] [main] ==> Starting service [Tomcat]
[INFO ] [2025-05-23 15:23:58] T:[] S:[] U:[] [main] ==> Starting Servlet engine: [Apache Tomcat/10.1.16]
[INFO ] [2025-05-23 15:23:58] T:[] S:[] U:[] [main] ==> Initializing Spring embedded WebApplicationContext
[INFO ] [2025-05-23 15:23:58] T:[] S:[] U:[] [main] ==> Root WebApplicationContext: initialization completed in 9654 ms
[ERROR] [2025-05-23 15:23:59] T:[] S:[] U:[] [main] ==> For security constraints with URL pattern [/*] only the HTTP methods [TRACE HEAD DELETE SEARCH PROPFIND COPY PUT PATCH] are covered. All other methods are uncovered.
[INFO ] [2025-05-23 15:23:59] T:[] S:[] U:[] [main] ==> Redisson 3.15.4
[INFO ] [2025-05-23 15:24:00] T:[] S:[] U:[] [redisson-netty-2-13] ==> 1 connections initialized for /*************:30051
[INFO ] [2025-05-23 15:24:00] T:[] S:[] U:[] [redisson-netty-2-10] ==> 5 connections initialized for /*************:30051
[INFO ] [2025-05-23 15:24:06] T:[] S:[] U:[] [main] ==> {dataSource-1,application-webadmin} inited
[INFO ] [2025-05-23 15:24:11] T:[] S:[] U:[] [main] ==> {dataSource-2,application-webadmin} inited
[INFO ] [2025-05-23 15:24:16] T:[] S:[] U:[] [main] ==> {dataSource-3,application-webadmin} inited
[INFO ] [2025-05-23 15:24:21] T:[] S:[] U:[] [main] ==> {dataSource-4,application-webadmin} inited
[INFO ] [2025-05-23 15:24:26] T:[] S:[] U:[] [main] ==> {dataSource-5,application-webadmin} inited
[WARN ] [2025-05-23 15:24:28] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysDataPermDept".
[WARN ] [2025-05-23 15:24:28] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysDataPermDept ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:24:28] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysDataPermMenu".
[WARN ] [2025-05-23 15:24:28] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysDataPermMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:24:28] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysDataPermUser".
[WARN ] [2025-05-23 15:24:28] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysDataPermUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:24:28] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysDeptRelation".
[WARN ] [2025-05-23 15:24:28] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysDeptRelation ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:24:28] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysRoleMenu".
[WARN ] [2025-05-23 15:24:28] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysRoleMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:24:28] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysUserPost".
[WARN ] [2025-05-23 15:24:28] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysUserPost ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:24:28] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysUserRole".
[WARN ] [2025-05-23 15:24:28] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:24:28] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.mobile.model.MobileEntryDataPerm".
[WARN ] [2025-05-23 15:24:28] T:[] S:[] U:[] [main] ==> class supie.common.mobile.model.MobileEntryDataPerm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:24:29] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.mobile.model.MobileEntryRole".
[WARN ] [2025-05-23 15:24:29] T:[] S:[] U:[] [main] ==> class supie.common.mobile.model.MobileEntryRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:24:29] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.online.model.OnlineColumnRule".
[WARN ] [2025-05-23 15:24:29] T:[] S:[] U:[] [main] ==> class supie.common.online.model.OnlineColumnRule ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:24:30] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.flow.model.FlowTaskExt".
[WARN ] [2025-05-23 15:24:30] T:[] S:[] U:[] [main] ==> class supie.common.flow.model.FlowTaskExt ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:24:31] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.report.model.ReportTenantDataset".
[WARN ] [2025-05-23 15:24:31] T:[] S:[] U:[] [main] ==> class supie.common.report.model.ReportTenantDataset ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[INFO ] [2025-05-23 15:24:42] T:[] S:[] U:[] [main] ==> No deployment resources were found for autodeployment
[INFO ] [2025-05-23 15:24:43] T:[] S:[] U:[] [main] ==> No deployment resources were found for autodeployment
[INFO ] [2025-05-23 15:24:44] T:[] S:[] U:[] [main] ==> Found 1 auto-discoverable Process Engine Configurator
[INFO ] [2025-05-23 15:24:44] T:[] S:[] U:[] [main] ==> Found 3 Engine Configurators in total:
[INFO ] [2025-05-23 15:24:44] T:[] S:[] U:[] [main] ==> class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-05-23 15:24:44] T:[] S:[] U:[] [main] ==> class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
[INFO ] [2025-05-23 15:24:44] T:[] S:[] U:[] [main] ==> class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
[INFO ] [2025-05-23 15:24:44] T:[] S:[] U:[] [main] ==> Executing beforeInit() of class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-05-23 15:24:44] T:[] S:[] U:[] [main] ==> Executing beforeInit() of class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
[INFO ] [2025-05-23 15:24:44] T:[] S:[] U:[] [main] ==> Executing beforeInit() of class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
[INFO ] [2025-05-23 15:24:46] T:[] S:[] U:[] [main] ==> Executing configure() of class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-05-23 15:24:46] T:[] S:[] U:[] [main] ==> Executing configure() of class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
[INFO ] [2025-05-23 15:24:46] T:[] S:[] U:[] [main] ==> Found 1 auto-discoverable Process Engine Configurator
[INFO ] [2025-05-23 15:24:46] T:[] S:[] U:[] [main] ==> Found 1 Engine Configurators in total:
[INFO ] [2025-05-23 15:24:46] T:[] S:[] U:[] [main] ==> class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-05-23 15:24:46] T:[] S:[] U:[] [main] ==> Executing beforeInit() of class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-05-23 15:24:46] T:[] S:[] U:[] [main] ==> Executing configure() of class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-05-23 15:24:47] T:[] S:[] U:[] [main] ==> INFO: An older version of the XSD is specified in one or more changelog's <databaseChangeLog> header. This can lead to unexpected outcomes. If a specific XSD is not required, please replace all XSD version references with "-latest". Learn more at https://docs.liquibase.com
[INFO ] [2025-05-23 15:24:48] T:[] S:[] U:[] [main] ==> Reading from gy_sch_dev.FLW_EV_DATABASECHANGELOG
[INFO ] [2025-05-23 15:24:48] T:[] S:[] U:[] [main] ==> Changelog query completed.
[INFO ] [2025-05-23 15:24:48] T:[] S:[] U:[] [main] ==> EventRegistryEngine default created
[INFO ] [2025-05-23 15:24:48] T:[] S:[] U:[] [main] ==> Executing configure() of class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
[INFO ] [2025-05-23 15:24:49] T:[] S:[] U:[] [main] ==> IdmEngine default created
[INFO ] [2025-05-23 15:24:49] T:[] S:[] U:[] [main] ==> ProcessEngine default created
[INFO ] [2025-05-23 15:24:49] T:[] S:[] U:[] [main] ==> Total of v5 deployments found: 0
[WARN ] [2025-05-23 15:25:05] T:[] S:[] U:[] [main] ==> Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'minioUpDownloader': Unsatisfied dependency expressed through field 'minioTemplate': Error creating bean with name 'minioTemplate' defined in class path resource [supie/common/minio/config/MinioAutoConfiguration.class]: Unsatisfied dependency expressed through method 'minioTemplate' parameter 1: Error creating bean with name 'minioClient' defined in class path resource [supie/common/minio/config/MinioAutoConfiguration.class]: Failed to instantiate [io.minio.MinioClient]: Factory method 'minioClient' threw exception with message: java.lang.IllegalArgumentException: gy_sch_be : bucket name does not follow Amazon S3 standards. For more information refer http://docs.aws.amazon.com/AmazonS3/latest/dev/BucketRestrictions.html
[INFO ] [2025-05-23 15:25:05] T:[] S:[] U:[] [main] ==> {dataSource-5} closing ...
[INFO ] [2025-05-23 15:25:05] T:[] S:[] U:[] [main] ==> {dataSource-5} closed
[INFO ] [2025-05-23 15:25:05] T:[] S:[] U:[] [main] ==> {dataSource-4} closing ...
[ERROR] [2025-05-23 15:25:05] T:[] S:[] U:[] [main] ==> unregister mbean error
javax.management.InstanceNotFoundException: com.alibaba.druid:type=DruidDataSource,id=application-webadmin
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.getMBean(DefaultMBeanServerInterceptor.java:1088)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.exclusiveUnregisterMBean(DefaultMBeanServerInterceptor.java:423)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.unregisterMBean(DefaultMBeanServerInterceptor.java:411)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.unregisterMBean(JmxMBeanServer.java:547)
	at com.alibaba.druid.stat.DruidDataSourceStatManager.removeDataSource(DruidDataSourceStatManager.java:191)
	at com.alibaba.druid.pool.DruidDataSource$2.run(DruidDataSource.java:2237)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:318)
	at com.alibaba.druid.pool.DruidDataSource.unregisterMbean(DruidDataSource.java:2234)
	at com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2191)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:221)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1189)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1182)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1118)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:629)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:738)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:440)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at supie.webadmin.WebAdminApplication.main(WebAdminApplication.java:21)
[INFO ] [2025-05-23 15:25:05] T:[] S:[] U:[] [main] ==> {dataSource-4} closed
[INFO ] [2025-05-23 15:25:05] T:[] S:[] U:[] [main] ==> {dataSource-3} closing ...
[ERROR] [2025-05-23 15:25:05] T:[] S:[] U:[] [main] ==> unregister mbean error
javax.management.InstanceNotFoundException: com.alibaba.druid:type=DruidDataSource,id=application-webadmin
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.getMBean(DefaultMBeanServerInterceptor.java:1088)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.exclusiveUnregisterMBean(DefaultMBeanServerInterceptor.java:423)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.unregisterMBean(DefaultMBeanServerInterceptor.java:411)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.unregisterMBean(JmxMBeanServer.java:547)
	at com.alibaba.druid.stat.DruidDataSourceStatManager.removeDataSource(DruidDataSourceStatManager.java:191)
	at com.alibaba.druid.pool.DruidDataSource$2.run(DruidDataSource.java:2237)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:318)
	at com.alibaba.druid.pool.DruidDataSource.unregisterMbean(DruidDataSource.java:2234)
	at com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2191)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:221)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1189)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1182)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1118)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:629)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:738)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:440)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at supie.webadmin.WebAdminApplication.main(WebAdminApplication.java:21)
[INFO ] [2025-05-23 15:25:05] T:[] S:[] U:[] [main] ==> {dataSource-3} closed
[INFO ] [2025-05-23 15:25:05] T:[] S:[] U:[] [main] ==> {dataSource-2} closing ...
[ERROR] [2025-05-23 15:25:05] T:[] S:[] U:[] [main] ==> unregister mbean error
javax.management.InstanceNotFoundException: com.alibaba.druid:type=DruidDataSource,id=application-webadmin
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.getMBean(DefaultMBeanServerInterceptor.java:1088)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.exclusiveUnregisterMBean(DefaultMBeanServerInterceptor.java:423)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.unregisterMBean(DefaultMBeanServerInterceptor.java:411)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.unregisterMBean(JmxMBeanServer.java:547)
	at com.alibaba.druid.stat.DruidDataSourceStatManager.removeDataSource(DruidDataSourceStatManager.java:191)
	at com.alibaba.druid.pool.DruidDataSource$2.run(DruidDataSource.java:2237)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:318)
	at com.alibaba.druid.pool.DruidDataSource.unregisterMbean(DruidDataSource.java:2234)
	at com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2191)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:221)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1189)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1182)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1118)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:629)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:738)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:440)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at supie.webadmin.WebAdminApplication.main(WebAdminApplication.java:21)
[INFO ] [2025-05-23 15:25:05] T:[] S:[] U:[] [main] ==> {dataSource-2} closed
[INFO ] [2025-05-23 15:25:05] T:[] S:[] U:[] [main] ==> {dataSource-1} closing ...
[ERROR] [2025-05-23 15:25:05] T:[] S:[] U:[] [main] ==> unregister mbean error
javax.management.InstanceNotFoundException: com.alibaba.druid:type=DruidDataSource,id=application-webadmin
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.getMBean(DefaultMBeanServerInterceptor.java:1088)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.exclusiveUnregisterMBean(DefaultMBeanServerInterceptor.java:423)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.unregisterMBean(DefaultMBeanServerInterceptor.java:411)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.unregisterMBean(JmxMBeanServer.java:547)
	at com.alibaba.druid.stat.DruidDataSourceStatManager.removeDataSource(DruidDataSourceStatManager.java:191)
	at com.alibaba.druid.pool.DruidDataSource$2.run(DruidDataSource.java:2237)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:318)
	at com.alibaba.druid.pool.DruidDataSource.unregisterMbean(DruidDataSource.java:2234)
	at com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2191)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:221)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1189)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1182)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1118)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:629)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:738)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:440)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at supie.webadmin.WebAdminApplication.main(WebAdminApplication.java:21)
[INFO ] [2025-05-23 15:25:05] T:[] S:[] U:[] [main] ==> {dataSource-1} closed
[INFO ] [2025-05-23 15:25:05] T:[] S:[] U:[] [main] ==> Stopping service [Tomcat]
[INFO ] [2025-05-23 15:25:05] T:[] S:[] U:[] [main] ==> 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
[ERROR] [2025-05-23 15:25:05] T:[] S:[] U:[] [main] ==> Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'minioUpDownloader': Unsatisfied dependency expressed through field 'minioTemplate': Error creating bean with name 'minioTemplate' defined in class path resource [supie/common/minio/config/MinioAutoConfiguration.class]: Unsatisfied dependency expressed through method 'minioTemplate' parameter 1: Error creating bean with name 'minioClient' defined in class path resource [supie/common/minio/config/MinioAutoConfiguration.class]: Failed to instantiate [io.minio.MinioClient]: Factory method 'minioClient' threw exception with message: java.lang.IllegalArgumentException: gy_sch_be : bucket name does not follow Amazon S3 standards. For more information refer http://docs.aws.amazon.com/AmazonS3/latest/dev/BucketRestrictions.html
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:771)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:751)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:492)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1416)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:597)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:520)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:973)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:950)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:616)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:738)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:440)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at supie.webadmin.WebAdminApplication.main(WebAdminApplication.java:21)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'minioTemplate' defined in class path resource [supie/common/minio/config/MinioAutoConfiguration.class]: Unsatisfied dependency expressed through method 'minioTemplate' parameter 1: Error creating bean with name 'minioClient' defined in class path resource [supie/common/minio/config/MinioAutoConfiguration.class]: Failed to instantiate [io.minio.MinioClient]: Factory method 'minioClient' threw exception with message: java.lang.IllegalArgumentException: gy_sch_be : bucket name does not follow Amazon S3 standards. For more information refer http://docs.aws.amazon.com/AmazonS3/latest/dev/BucketRestrictions.html
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:801)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:545)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1332)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1162)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:560)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:520)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1417)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1337)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:768)
	... 20 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'minioClient' defined in class path resource [supie/common/minio/config/MinioAutoConfiguration.class]: Failed to instantiate [io.minio.MinioClient]: Factory method 'minioClient' threw exception with message: java.lang.IllegalArgumentException: gy_sch_be : bucket name does not follow Amazon S3 standards. For more information refer http://docs.aws.amazon.com/AmazonS3/latest/dev/BucketRestrictions.html
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:654)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:642)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1332)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1162)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:560)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:520)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1417)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1337)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:910)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:788)
	... 33 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [io.minio.MinioClient]: Factory method 'minioClient' threw exception with message: java.lang.IllegalArgumentException: gy_sch_be : bucket name does not follow Amazon S3 standards. For more information refer http://docs.aws.amazon.com/AmazonS3/latest/dev/BucketRestrictions.html
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:171)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:650)
	... 47 common frames omitted
Caused by: supie.common.core.exception.MyRuntimeException: java.lang.IllegalArgumentException: gy_sch_be : bucket name does not follow Amazon S3 standards. For more information refer http://docs.aws.amazon.com/AmazonS3/latest/dev/BucketRestrictions.html
	at supie.common.minio.config.MinioAutoConfiguration.minioClient(MinioAutoConfiguration.java:40)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:139)
	... 48 common frames omitted
Caused by: java.lang.IllegalArgumentException: gy_sch_be : bucket name does not follow Amazon S3 standards. For more information refer http://docs.aws.amazon.com/AmazonS3/latest/dev/BucketRestrictions.html
	at io.minio.BucketArgs$Builder.validateBucketName(BucketArgs.java:57)
	at io.minio.BucketArgs$Builder.bucket(BucketArgs.java:72)
	at supie.common.minio.config.MinioAutoConfiguration.minioClient(MinioAutoConfiguration.java:35)
	... 53 common frames omitted
[INFO ] [2025-05-23 15:34:54] T:[] S:[] U:[] [background-preinit] ==> HV000001: Hibernate Validator 8.0.1.Final
[INFO ] [2025-05-23 15:34:54] T:[] S:[] U:[] [main] ==> Starting WebAdminApplication using Java 17.0.4 with PID 2532 (D:\CompanySupie\gy_sch_be\application-webadmin\target\classes started by Superhero in D:\CompanySupie\gy_sch_be)
[INFO ] [2025-05-23 15:34:54] T:[] S:[] U:[] [main] ==> The following 1 profile is active: "dev"
[INFO ] [2025-05-23 15:35:00] T:[] S:[] U:[] [main] ==> Multiple Spring Data modules found, entering strict repository configuration mode
[INFO ] [2025-05-23 15:35:00] T:[] S:[] U:[] [main] ==> Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[INFO ] [2025-05-23 15:35:00] T:[] S:[] U:[] [main] ==> Finished Spring Data repository scanning in 72 ms. Found 0 Redis repository interfaces.
[INFO ] [2025-05-23 15:35:01] T:[] S:[] U:[] [main] ==> Bean 'commonWebMvcConfig' of type [supie.common.core.config.CommonWebMvcConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO ] [2025-05-23 15:35:04] T:[] S:[] U:[] [main] ==> Tomcat initialized with port(s): 8082 (http)
[INFO ] [2025-05-23 15:35:04] T:[] S:[] U:[] [main] ==> Initializing ProtocolHandler ["http-nio-8082"]
[INFO ] [2025-05-23 15:35:04] T:[] S:[] U:[] [main] ==> Starting service [Tomcat]
[INFO ] [2025-05-23 15:35:04] T:[] S:[] U:[] [main] ==> Starting Servlet engine: [Apache Tomcat/10.1.16]
[INFO ] [2025-05-23 15:35:04] T:[] S:[] U:[] [main] ==> Initializing Spring embedded WebApplicationContext
[INFO ] [2025-05-23 15:35:04] T:[] S:[] U:[] [main] ==> Root WebApplicationContext: initialization completed in 9302 ms
[ERROR] [2025-05-23 15:35:05] T:[] S:[] U:[] [main] ==> For security constraints with URL pattern [/*] only the HTTP methods [TRACE HEAD DELETE SEARCH PROPFIND COPY PUT PATCH] are covered. All other methods are uncovered.
[INFO ] [2025-05-23 15:35:06] T:[] S:[] U:[] [main] ==> Redisson 3.15.4
[INFO ] [2025-05-23 15:35:07] T:[] S:[] U:[] [redisson-netty-2-11] ==> 5 connections initialized for /*************:30051
[INFO ] [2025-05-23 15:35:07] T:[] S:[] U:[] [redisson-netty-2-13] ==> 1 connections initialized for /*************:30051
[INFO ] [2025-05-23 15:35:14] T:[] S:[] U:[] [main] ==> {dataSource-1,application-webadmin} inited
[INFO ] [2025-05-23 15:35:20] T:[] S:[] U:[] [main] ==> {dataSource-2,application-webadmin} inited
[INFO ] [2025-05-23 15:35:26] T:[] S:[] U:[] [main] ==> {dataSource-3,application-webadmin} inited
[INFO ] [2025-05-23 15:35:32] T:[] S:[] U:[] [main] ==> {dataSource-4,application-webadmin} inited
[INFO ] [2025-05-23 15:35:38] T:[] S:[] U:[] [main] ==> {dataSource-5,application-webadmin} inited
[WARN ] [2025-05-23 15:35:40] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysDataPermDept".
[WARN ] [2025-05-23 15:35:40] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysDataPermDept ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:35:40] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysDataPermMenu".
[WARN ] [2025-05-23 15:35:40] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysDataPermMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:35:40] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysDataPermUser".
[WARN ] [2025-05-23 15:35:40] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysDataPermUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:35:40] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysDeptRelation".
[WARN ] [2025-05-23 15:35:40] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysDeptRelation ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:35:40] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysRoleMenu".
[WARN ] [2025-05-23 15:35:40] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysRoleMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:35:40] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysUserPost".
[WARN ] [2025-05-23 15:35:40] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysUserPost ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:35:40] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysUserRole".
[WARN ] [2025-05-23 15:35:40] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:35:40] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.mobile.model.MobileEntryDataPerm".
[WARN ] [2025-05-23 15:35:40] T:[] S:[] U:[] [main] ==> class supie.common.mobile.model.MobileEntryDataPerm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:35:40] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.mobile.model.MobileEntryRole".
[WARN ] [2025-05-23 15:35:40] T:[] S:[] U:[] [main] ==> class supie.common.mobile.model.MobileEntryRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:35:41] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.online.model.OnlineColumnRule".
[WARN ] [2025-05-23 15:35:41] T:[] S:[] U:[] [main] ==> class supie.common.online.model.OnlineColumnRule ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:35:42] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.flow.model.FlowTaskExt".
[WARN ] [2025-05-23 15:35:42] T:[] S:[] U:[] [main] ==> class supie.common.flow.model.FlowTaskExt ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:35:43] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.report.model.ReportTenantDataset".
[WARN ] [2025-05-23 15:35:43] T:[] S:[] U:[] [main] ==> class supie.common.report.model.ReportTenantDataset ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[INFO ] [2025-05-23 15:35:55] T:[] S:[] U:[] [main] ==> No deployment resources were found for autodeployment
[INFO ] [2025-05-23 15:35:56] T:[] S:[] U:[] [main] ==> No deployment resources were found for autodeployment
[INFO ] [2025-05-23 15:35:57] T:[] S:[] U:[] [main] ==> Found 1 auto-discoverable Process Engine Configurator
[INFO ] [2025-05-23 15:35:57] T:[] S:[] U:[] [main] ==> Found 3 Engine Configurators in total:
[INFO ] [2025-05-23 15:35:57] T:[] S:[] U:[] [main] ==> class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-05-23 15:35:57] T:[] S:[] U:[] [main] ==> class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
[INFO ] [2025-05-23 15:35:57] T:[] S:[] U:[] [main] ==> class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
[INFO ] [2025-05-23 15:35:57] T:[] S:[] U:[] [main] ==> Executing beforeInit() of class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-05-23 15:35:57] T:[] S:[] U:[] [main] ==> Executing beforeInit() of class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
[INFO ] [2025-05-23 15:35:57] T:[] S:[] U:[] [main] ==> Executing beforeInit() of class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
[INFO ] [2025-05-23 15:35:58] T:[] S:[] U:[] [main] ==> Executing configure() of class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-05-23 15:35:58] T:[] S:[] U:[] [main] ==> Executing configure() of class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
[INFO ] [2025-05-23 15:35:58] T:[] S:[] U:[] [main] ==> Found 1 auto-discoverable Process Engine Configurator
[INFO ] [2025-05-23 15:35:58] T:[] S:[] U:[] [main] ==> Found 1 Engine Configurators in total:
[INFO ] [2025-05-23 15:35:58] T:[] S:[] U:[] [main] ==> class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-05-23 15:35:58] T:[] S:[] U:[] [main] ==> Executing beforeInit() of class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-05-23 15:35:58] T:[] S:[] U:[] [main] ==> Executing configure() of class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-05-23 15:35:59] T:[] S:[] U:[] [main] ==> INFO: An older version of the XSD is specified in one or more changelog's <databaseChangeLog> header. This can lead to unexpected outcomes. If a specific XSD is not required, please replace all XSD version references with "-latest". Learn more at https://docs.liquibase.com
[INFO ] [2025-05-23 15:36:00] T:[] S:[] U:[] [main] ==> Reading from gy_sch_dev.FLW_EV_DATABASECHANGELOG
[INFO ] [2025-05-23 15:36:01] T:[] S:[] U:[] [main] ==> Changelog query completed.
[INFO ] [2025-05-23 15:36:01] T:[] S:[] U:[] [main] ==> EventRegistryEngine default created
[INFO ] [2025-05-23 15:36:01] T:[] S:[] U:[] [main] ==> Executing configure() of class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
[INFO ] [2025-05-23 15:36:01] T:[] S:[] U:[] [main] ==> IdmEngine default created
[INFO ] [2025-05-23 15:36:02] T:[] S:[] U:[] [main] ==> ProcessEngine default created
[INFO ] [2025-05-23 15:36:02] T:[] S:[] U:[] [main] ==> Total of v5 deployments found: 0
[INFO ] [2025-05-23 15:36:22] T:[] S:[] U:[] [main] ==> Exposing 14 endpoint(s) beneath base path '/actuator'
[INFO ] [2025-05-23 15:36:23] T:[] S:[] U:[] [main] ==> Starting ProtocolHandler ["http-nio-8082"]
[INFO ] [2025-05-23 15:36:23] T:[] S:[] U:[] [main] ==> Tomcat started on port(s): 8082 (http) with context path ''
[INFO ] [2025-05-23 15:36:23] T:[] S:[] U:[] [main] ==> Started WebAdminApplication in 90.466 seconds (process running for 93.632)
[ERROR] [2025-05-23 15:36:24] T:[] S:[] U:[] [main] ==> Application run failed
java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @543788f3
	at java.base/java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:354)
	at java.base/java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:297)
	at java.base/java.lang.reflect.Field.checkCanSetAccessible(Field.java:178)
	at java.base/java.lang.reflect.Field.setAccessible(Field.java:172)
	at cn.hutool.core.util.ReflectUtil.setAccessible(ReflectUtil.java:1122)
	at cn.hutool.core.util.ReflectUtil.getFieldValue(ReflectUtil.java:271)
	at cn.hutool.core.util.ReflectUtil.getFieldValue(ReflectUtil.java:239)
	at supie.common.datafilter.interceptor.MybatisDataFilterInterceptor.loadInfoWithDataFilter(MybatisDataFilterInterceptor.java:94)
	at supie.common.datafilter.listener.LoadDataFilterInfoListener.onApplicationEvent(LoadDataFilterInfoListener.java:23)
	at supie.common.datafilter.listener.LoadDataFilterInfoListener.onApplicationEvent(LoadDataFilterInfoListener.java:16)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:174)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:167)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:445)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:378)
	at org.springframework.boot.context.event.EventPublishingRunListener.ready(EventPublishingRunListener.java:109)
	at org.springframework.boot.SpringApplicationRunListeners.lambda$ready$6(SpringApplicationRunListeners.java:80)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:118)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:112)
	at org.springframework.boot.SpringApplicationRunListeners.ready(SpringApplicationRunListeners.java:80)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at supie.webadmin.WebAdminApplication.main(WebAdminApplication.java:21)
[INFO ] [2025-05-23 15:36:24] T:[] S:[] U:[] [main] ==> Pausing ProtocolHandler ["http-nio-8082"]
[INFO ] [2025-05-23 15:36:24] T:[] S:[] U:[] [main] ==> Stopping service [Tomcat]
[INFO ] [2025-05-23 15:36:24] T:[] S:[] U:[] [main] ==> Stopping ProtocolHandler ["http-nio-8082"]
[INFO ] [2025-05-23 15:36:24] T:[] S:[] U:[] [main] ==> Destroying ProtocolHandler ["http-nio-8082"]
[INFO ] [2025-05-23 15:36:24] T:[] S:[] U:[] [main] ==> {dataSource-5} closing ...
[INFO ] [2025-05-23 15:36:24] T:[] S:[] U:[] [main] ==> {dataSource-5} closed
[INFO ] [2025-05-23 15:36:24] T:[] S:[] U:[] [main] ==> {dataSource-4} closing ...
[ERROR] [2025-05-23 15:36:24] T:[] S:[] U:[] [main] ==> unregister mbean error
javax.management.InstanceNotFoundException: com.alibaba.druid:type=DruidDataSource,id=application-webadmin
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.getMBean(DefaultMBeanServerInterceptor.java:1088)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.exclusiveUnregisterMBean(DefaultMBeanServerInterceptor.java:423)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.unregisterMBean(DefaultMBeanServerInterceptor.java:411)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.unregisterMBean(JmxMBeanServer.java:547)
	at com.alibaba.druid.stat.DruidDataSourceStatManager.removeDataSource(DruidDataSourceStatManager.java:191)
	at com.alibaba.druid.pool.DruidDataSource$2.run(DruidDataSource.java:2237)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:318)
	at com.alibaba.druid.pool.DruidDataSource.unregisterMbean(DruidDataSource.java:2234)
	at com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2191)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:221)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1189)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1182)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1118)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1084)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:173)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1035)
	at org.springframework.boot.SpringApplication.handleRunFailure(SpringApplication.java:790)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:342)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at supie.webadmin.WebAdminApplication.main(WebAdminApplication.java:21)
[INFO ] [2025-05-23 15:36:24] T:[] S:[] U:[] [main] ==> {dataSource-4} closed
[INFO ] [2025-05-23 15:36:24] T:[] S:[] U:[] [main] ==> {dataSource-3} closing ...
[ERROR] [2025-05-23 15:36:24] T:[] S:[] U:[] [main] ==> unregister mbean error
javax.management.InstanceNotFoundException: com.alibaba.druid:type=DruidDataSource,id=application-webadmin
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.getMBean(DefaultMBeanServerInterceptor.java:1088)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.exclusiveUnregisterMBean(DefaultMBeanServerInterceptor.java:423)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.unregisterMBean(DefaultMBeanServerInterceptor.java:411)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.unregisterMBean(JmxMBeanServer.java:547)
	at com.alibaba.druid.stat.DruidDataSourceStatManager.removeDataSource(DruidDataSourceStatManager.java:191)
	at com.alibaba.druid.pool.DruidDataSource$2.run(DruidDataSource.java:2237)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:318)
	at com.alibaba.druid.pool.DruidDataSource.unregisterMbean(DruidDataSource.java:2234)
	at com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2191)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:221)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1189)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1182)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1118)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1084)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:173)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1035)
	at org.springframework.boot.SpringApplication.handleRunFailure(SpringApplication.java:790)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:342)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at supie.webadmin.WebAdminApplication.main(WebAdminApplication.java:21)
[INFO ] [2025-05-23 15:36:24] T:[] S:[] U:[] [main] ==> {dataSource-3} closed
[INFO ] [2025-05-23 15:36:24] T:[] S:[] U:[] [main] ==> {dataSource-2} closing ...
[ERROR] [2025-05-23 15:36:24] T:[] S:[] U:[] [main] ==> unregister mbean error
javax.management.InstanceNotFoundException: com.alibaba.druid:type=DruidDataSource,id=application-webadmin
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.getMBean(DefaultMBeanServerInterceptor.java:1088)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.exclusiveUnregisterMBean(DefaultMBeanServerInterceptor.java:423)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.unregisterMBean(DefaultMBeanServerInterceptor.java:411)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.unregisterMBean(JmxMBeanServer.java:547)
	at com.alibaba.druid.stat.DruidDataSourceStatManager.removeDataSource(DruidDataSourceStatManager.java:191)
	at com.alibaba.druid.pool.DruidDataSource$2.run(DruidDataSource.java:2237)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:318)
	at com.alibaba.druid.pool.DruidDataSource.unregisterMbean(DruidDataSource.java:2234)
	at com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2191)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:221)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1189)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1182)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1118)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1084)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:173)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1035)
	at org.springframework.boot.SpringApplication.handleRunFailure(SpringApplication.java:790)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:342)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at supie.webadmin.WebAdminApplication.main(WebAdminApplication.java:21)
[INFO ] [2025-05-23 15:36:24] T:[] S:[] U:[] [main] ==> {dataSource-2} closed
[INFO ] [2025-05-23 15:36:24] T:[] S:[] U:[] [main] ==> {dataSource-1} closing ...
[ERROR] [2025-05-23 15:36:24] T:[] S:[] U:[] [main] ==> unregister mbean error
javax.management.InstanceNotFoundException: com.alibaba.druid:type=DruidDataSource,id=application-webadmin
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.getMBean(DefaultMBeanServerInterceptor.java:1088)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.exclusiveUnregisterMBean(DefaultMBeanServerInterceptor.java:423)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.unregisterMBean(DefaultMBeanServerInterceptor.java:411)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.unregisterMBean(JmxMBeanServer.java:547)
	at com.alibaba.druid.stat.DruidDataSourceStatManager.removeDataSource(DruidDataSourceStatManager.java:191)
	at com.alibaba.druid.pool.DruidDataSource$2.run(DruidDataSource.java:2237)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:318)
	at com.alibaba.druid.pool.DruidDataSource.unregisterMbean(DruidDataSource.java:2234)
	at com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2191)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:221)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1189)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1182)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1118)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1084)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:173)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1035)
	at org.springframework.boot.SpringApplication.handleRunFailure(SpringApplication.java:790)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:342)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at supie.webadmin.WebAdminApplication.main(WebAdminApplication.java:21)
[INFO ] [2025-05-23 15:36:24] T:[] S:[] U:[] [main] ==> {dataSource-1} closed
[INFO ] [2025-05-23 15:41:56] T:[] S:[] U:[] [background-preinit] ==> HV000001: Hibernate Validator 8.0.1.Final
[INFO ] [2025-05-23 15:41:56] T:[] S:[] U:[] [main] ==> Starting WebAdminApplication using Java 17.0.4 with PID 27432 (D:\CompanySupie\gy_sch_be\application-webadmin\target\classes started by Superhero in D:\CompanySupie\gy_sch_be)
[INFO ] [2025-05-23 15:41:56] T:[] S:[] U:[] [main] ==> The following 1 profile is active: "dev"
[INFO ] [2025-05-23 15:42:01] T:[] S:[] U:[] [main] ==> Multiple Spring Data modules found, entering strict repository configuration mode
[INFO ] [2025-05-23 15:42:01] T:[] S:[] U:[] [main] ==> Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[INFO ] [2025-05-23 15:42:01] T:[] S:[] U:[] [main] ==> Finished Spring Data repository scanning in 53 ms. Found 0 Redis repository interfaces.
[INFO ] [2025-05-23 15:42:02] T:[] S:[] U:[] [main] ==> Bean 'commonWebMvcConfig' of type [supie.common.core.config.CommonWebMvcConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO ] [2025-05-23 15:42:04] T:[] S:[] U:[] [main] ==> Tomcat initialized with port(s): 8082 (http)
[INFO ] [2025-05-23 15:42:04] T:[] S:[] U:[] [main] ==> Initializing ProtocolHandler ["http-nio-8082"]
[INFO ] [2025-05-23 15:42:04] T:[] S:[] U:[] [main] ==> Starting service [Tomcat]
[INFO ] [2025-05-23 15:42:04] T:[] S:[] U:[] [main] ==> Starting Servlet engine: [Apache Tomcat/10.1.16]
[INFO ] [2025-05-23 15:42:04] T:[] S:[] U:[] [main] ==> Initializing Spring embedded WebApplicationContext
[INFO ] [2025-05-23 15:42:04] T:[] S:[] U:[] [main] ==> Root WebApplicationContext: initialization completed in 7710 ms
[ERROR] [2025-05-23 15:42:05] T:[] S:[] U:[] [main] ==> For security constraints with URL pattern [/*] only the HTTP methods [TRACE HEAD DELETE SEARCH PROPFIND COPY PUT PATCH] are covered. All other methods are uncovered.
[INFO ] [2025-05-23 15:42:05] T:[] S:[] U:[] [main] ==> Redisson 3.15.4
[INFO ] [2025-05-23 15:42:06] T:[] S:[] U:[] [redisson-netty-2-9] ==> 1 connections initialized for /*************:30051
[INFO ] [2025-05-23 15:42:06] T:[] S:[] U:[] [redisson-netty-2-13] ==> 5 connections initialized for /*************:30051
[INFO ] [2025-05-23 15:42:12] T:[] S:[] U:[] [main] ==> {dataSource-1,application-webadmin} inited
[INFO ] [2025-05-23 15:42:17] T:[] S:[] U:[] [main] ==> {dataSource-2,application-webadmin} inited
[INFO ] [2025-05-23 15:42:23] T:[] S:[] U:[] [main] ==> {dataSource-3,application-webadmin} inited
[INFO ] [2025-05-23 15:42:27] T:[] S:[] U:[] [main] ==> {dataSource-4,application-webadmin} inited
[INFO ] [2025-05-23 15:42:33] T:[] S:[] U:[] [main] ==> {dataSource-5,application-webadmin} inited
[WARN ] [2025-05-23 15:42:34] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysDataPermDept".
[WARN ] [2025-05-23 15:42:34] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysDataPermDept ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:42:34] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysDataPermMenu".
[WARN ] [2025-05-23 15:42:34] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysDataPermMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:42:34] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysDataPermUser".
[WARN ] [2025-05-23 15:42:34] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysDataPermUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:42:34] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysDeptRelation".
[WARN ] [2025-05-23 15:42:34] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysDeptRelation ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:42:35] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysRoleMenu".
[WARN ] [2025-05-23 15:42:35] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysRoleMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:42:35] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysUserPost".
[WARN ] [2025-05-23 15:42:35] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysUserPost ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:42:35] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysUserRole".
[WARN ] [2025-05-23 15:42:35] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:42:35] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.mobile.model.MobileEntryDataPerm".
[WARN ] [2025-05-23 15:42:35] T:[] S:[] U:[] [main] ==> class supie.common.mobile.model.MobileEntryDataPerm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:42:35] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.mobile.model.MobileEntryRole".
[WARN ] [2025-05-23 15:42:35] T:[] S:[] U:[] [main] ==> class supie.common.mobile.model.MobileEntryRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:42:35] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.online.model.OnlineColumnRule".
[WARN ] [2025-05-23 15:42:35] T:[] S:[] U:[] [main] ==> class supie.common.online.model.OnlineColumnRule ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:42:36] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.flow.model.FlowTaskExt".
[WARN ] [2025-05-23 15:42:36] T:[] S:[] U:[] [main] ==> class supie.common.flow.model.FlowTaskExt ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-05-23 15:42:37] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.report.model.ReportTenantDataset".
[WARN ] [2025-05-23 15:42:37] T:[] S:[] U:[] [main] ==> class supie.common.report.model.ReportTenantDataset ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[INFO ] [2025-05-23 15:42:51] T:[] S:[] U:[] [main] ==> No deployment resources were found for autodeployment
[INFO ] [2025-05-23 15:42:52] T:[] S:[] U:[] [main] ==> No deployment resources were found for autodeployment
[INFO ] [2025-05-23 15:42:53] T:[] S:[] U:[] [main] ==> Found 1 auto-discoverable Process Engine Configurator
[INFO ] [2025-05-23 15:42:53] T:[] S:[] U:[] [main] ==> Found 3 Engine Configurators in total:
[INFO ] [2025-05-23 15:42:53] T:[] S:[] U:[] [main] ==> class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-05-23 15:42:53] T:[] S:[] U:[] [main] ==> class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
[INFO ] [2025-05-23 15:42:53] T:[] S:[] U:[] [main] ==> class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
[INFO ] [2025-05-23 15:42:53] T:[] S:[] U:[] [main] ==> Executing beforeInit() of class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-05-23 15:42:53] T:[] S:[] U:[] [main] ==> Executing beforeInit() of class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
[INFO ] [2025-05-23 15:42:53] T:[] S:[] U:[] [main] ==> Executing beforeInit() of class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
[INFO ] [2025-05-23 15:42:56] T:[] S:[] U:[] [main] ==> Executing configure() of class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-05-23 15:42:56] T:[] S:[] U:[] [main] ==> Executing configure() of class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
[INFO ] [2025-05-23 15:42:56] T:[] S:[] U:[] [main] ==> Found 1 auto-discoverable Process Engine Configurator
[INFO ] [2025-05-23 15:42:56] T:[] S:[] U:[] [main] ==> Found 1 Engine Configurators in total:
[INFO ] [2025-05-23 15:42:56] T:[] S:[] U:[] [main] ==> class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-05-23 15:42:56] T:[] S:[] U:[] [main] ==> Executing beforeInit() of class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-05-23 15:42:56] T:[] S:[] U:[] [main] ==> Executing configure() of class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-05-23 15:42:56] T:[] S:[] U:[] [main] ==> INFO: An older version of the XSD is specified in one or more changelog's <databaseChangeLog> header. This can lead to unexpected outcomes. If a specific XSD is not required, please replace all XSD version references with "-latest". Learn more at https://docs.liquibase.com
[INFO ] [2025-05-23 15:42:57] T:[] S:[] U:[] [main] ==> Reading from gy_sch_dev.FLW_EV_DATABASECHANGELOG
[INFO ] [2025-05-23 15:42:58] T:[] S:[] U:[] [main] ==> Changelog query completed.
[INFO ] [2025-05-23 15:42:58] T:[] S:[] U:[] [main] ==> EventRegistryEngine default created
[INFO ] [2025-05-23 15:42:58] T:[] S:[] U:[] [main] ==> Executing configure() of class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
[INFO ] [2025-05-23 15:42:58] T:[] S:[] U:[] [main] ==> IdmEngine default created
[INFO ] [2025-05-23 15:42:58] T:[] S:[] U:[] [main] ==> ProcessEngine default created
[INFO ] [2025-05-23 15:42:59] T:[] S:[] U:[] [main] ==> Total of v5 deployments found: 0
[INFO ] [2025-05-23 15:43:19] T:[] S:[] U:[] [main] ==> Exposing 14 endpoint(s) beneath base path '/actuator'
[INFO ] [2025-05-23 15:43:20] T:[] S:[] U:[] [main] ==> Starting ProtocolHandler ["http-nio-8082"]
[INFO ] [2025-05-23 15:43:20] T:[] S:[] U:[] [main] ==> Tomcat started on port(s): 8082 (http) with context path ''
[INFO ] [2025-05-23 15:43:20] T:[] S:[] U:[] [main] ==> Started WebAdminApplication in 85.279 seconds (process running for 87.395)
[DEBUG] [2025-05-23 15:45:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-05-23 15:45:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-05-23 15:45:00.061(Timestamp)
[DEBUG] [2025-05-23 15:45:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[DEBUG] [2025-05-23 15:50:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-05-23 15:50:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-05-23 15:50:00.048(Timestamp)
[DEBUG] [2025-05-23 15:50:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[DEBUG] [2025-05-23 15:55:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-05-23 15:55:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-05-23 15:55:00.049(Timestamp)
[DEBUG] [2025-05-23 15:55:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[DEBUG] [2025-05-23 16:00:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-05-23 16:00:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-05-23 16:00:00.057(Timestamp)
[DEBUG] [2025-05-23 16:00:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[DEBUG] [2025-05-23 16:05:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-05-23 16:05:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-05-23 16:05:00.054(Timestamp)
[DEBUG] [2025-05-23 16:05:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[DEBUG] [2025-05-23 16:10:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-05-23 16:10:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-05-23 16:10:00.032(Timestamp)
[DEBUG] [2025-05-23 16:10:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[DEBUG] [2025-05-23 16:15:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-05-23 16:15:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-05-23 16:15:00.035(Timestamp)
[DEBUG] [2025-05-23 16:15:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[DEBUG] [2025-05-23 16:20:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-05-23 16:20:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-05-23 16:20:00.141(Timestamp)
[DEBUG] [2025-05-23 16:20:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[DEBUG] [2025-05-23 16:25:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-05-23 16:25:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-05-23 16:25:00.078(Timestamp)
[DEBUG] [2025-05-23 16:25:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[DEBUG] [2025-05-23 16:30:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-05-23 16:30:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-05-23 16:30:00.097(Timestamp)
[DEBUG] [2025-05-23 16:30:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[DEBUG] [2025-05-23 16:35:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-05-23 16:35:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-05-23 16:35:00.08(Timestamp)
[DEBUG] [2025-05-23 16:35:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[DEBUG] [2025-05-23 16:40:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-05-23 16:40:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-05-23 16:40:00.068(Timestamp)
[DEBUG] [2025-05-23 16:40:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[DEBUG] [2025-05-23 16:45:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-05-23 16:45:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-05-23 16:45:00.029(Timestamp)
[DEBUG] [2025-05-23 16:45:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[DEBUG] [2025-05-23 16:50:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-05-23 16:50:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-05-23 16:50:00.04(Timestamp)
[DEBUG] [2025-05-23 16:50:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[DEBUG] [2025-05-23 16:55:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-05-23 16:55:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-05-23 16:55:00.089(Timestamp)
[DEBUG] [2025-05-23 16:55:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[DEBUG] [2025-05-23 17:00:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-05-23 17:00:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-05-23 17:00:00.121(Timestamp)
[DEBUG] [2025-05-23 17:00:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[DEBUG] [2025-05-23 17:05:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-05-23 17:05:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-05-23 17:05:00.087(Timestamp)
[DEBUG] [2025-05-23 17:05:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[DEBUG] [2025-05-23 17:10:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-05-23 17:10:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-05-23 17:10:00.111(Timestamp)
[DEBUG] [2025-05-23 17:10:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[DEBUG] [2025-05-23 17:15:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-05-23 17:15:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-05-23 17:15:00.096(Timestamp)
[DEBUG] [2025-05-23 17:15:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[DEBUG] [2025-05-23 17:20:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-05-23 17:20:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-05-23 17:20:00.037(Timestamp)
[DEBUG] [2025-05-23 17:20:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[DEBUG] [2025-05-23 17:25:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-05-23 17:25:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-05-23 17:25:00.037(Timestamp)
[DEBUG] [2025-05-23 17:25:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[DEBUG] [2025-05-23 17:30:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-05-23 17:30:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-05-23 17:30:00.076(Timestamp)
[DEBUG] [2025-05-23 17:30:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[DEBUG] [2025-05-23 17:35:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-05-23 17:35:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-05-23 17:35:00.204(Timestamp)
[DEBUG] [2025-05-23 17:35:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
