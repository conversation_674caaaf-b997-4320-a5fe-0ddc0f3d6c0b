[DEBUG] [2025-06-20 00:00:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-06-20 00:00:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-06-20 00:00:00.031(Timestamp)
[DEBUG] [2025-06-20 00:00:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[DEBUG] [2025-06-20 00:05:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-06-20 00:05:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-06-20 00:05:00.029(Timestamp)
[DEBUG] [2025-06-20 00:05:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[DEBUG] [2025-06-20 00:10:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-06-20 00:10:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-06-20 00:10:00.022(Timestamp)
[DEBUG] [2025-06-20 00:10:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[DEBUG] [2025-06-20 00:15:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-06-20 00:15:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-06-20 00:15:00.018(Timestamp)
[DEBUG] [2025-06-20 00:15:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[DEBUG] [2025-06-20 00:20:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-06-20 00:20:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-06-20 00:20:00.043(Timestamp)
[DEBUG] [2025-06-20 00:20:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[INFO ] [2025-06-20 00:24:26] T:[f9c7ef22c98242cdbbaf0ad9a5f3feac] S:[] U:[] [http-nio-8084-exec-2] ==> 开始请求，url=/admin/upms/login/doLogin, reqData={"password":"pI9AmEXh8nrdHlucJbD2s0kL5pZArt0xNBuqyN9%2BATp7BjfLGUBwYPrkA2GA4KiD0lmXdb1PxOIcbjgsVjhENuh2uQ9qJIcgvxuS1qBJCWvA6og%2F0jqwudvMG9V%2FioJaW%2Fz3poqboPsaY0QVDgaWYoWCaC%2F9%2FiRZQp3YELYfi28%3D","loginName":"admin"}
[DEBUG] [2025-06-20 00:24:26] T:[f9c7ef22c98242cdbbaf0ad9a5f3feac] S:[] U:[] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT user_id,login_name,password,dept_id,show_name,user_type,head_image_url,workbench_id,user_status,email,mobile,user_auth_info,create_user_id,update_user_id,create_time,update_time,deleted_flag FROM sys_user WHERE deleted_flag=1 AND login_name=?
[DEBUG] [2025-06-20 00:24:26] T:[f9c7ef22c98242cdbbaf0ad9a5f3feac] S:[] U:[] [http-nio-8084-exec-2] ==> ==> Parameters: admin(String)
[DEBUG] [2025-06-20 00:24:27] T:[f9c7ef22c98242cdbbaf0ad9a5f3feac] S:[] U:[] [http-nio-8084-exec-2] ==> <==      Total: 1
[ERROR] [2025-06-20 00:24:46] T:[f9c7ef22c98242cdbbaf0ad9a5f3feac] S:[] U:[] [http-nio-8084-exec-2] ==> 请求报错，url=/admin/upms/login/doLogin, reqData={"password":"pI9AmEXh8nrdHlucJbD2s0kL5pZArt0xNBuqyN9%2BATp7BjfLGUBwYPrkA2GA4KiD0lmXdb1PxOIcbjgsVjhENuh2uQ9qJIcgvxuS1qBJCWvA6og%2F0jqwudvMG9V%2FioJaW%2Fz3poqboPsaY0QVDgaWYoWCaC%2F9%2FiRZQp3YELYfi28%3D","loginName":"admin"}, error=Redis exception
[ERROR] [2025-06-20 00:24:46] T:[] S:[] U:[] [http-nio-8084-exec-2] ==> DataAccessException exception from URL [/admin/upms/login/doLogin]
org.springframework.data.redis.RedisSystemException: Redis exception
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:72)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:38)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:256)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:969)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.lambda$doInvoke$4(LettuceConnection.java:826)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$Synchronizer.invoke(LettuceInvoker.java:665)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker.just(LettuceInvoker.java:94)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:54)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.get(DefaultedRedisConnection.java:284)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.get(DefaultStringRedisConnection.java:382)
	at org.springframework.data.redis.core.DefaultValueOperations$1.inRedis(DefaultValueOperations.java:54)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:61)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:406)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:373)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:97)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:50)
	at cn.dev33.satoken.dao.SaTokenDaoRedisFastjson.get(SaTokenDaoRedisFastjson.java:101)
	at cn.dev33.satoken.stp.StpLogic.getLoginIdNotHandle(StpLogic.java:1094)
	at cn.dev33.satoken.stp.StpLogic.lambda$distUsableToken$1(StpLogic.java:549)
	at cn.dev33.satoken.strategy.SaStrategy.lambda$new$8(SaStrategy.java:315)
	at cn.dev33.satoken.stp.StpLogic.distUsableToken(StpLogic.java:542)
	at cn.dev33.satoken.stp.StpLogic.createLoginSession(StpLogic.java:471)
	at cn.dev33.satoken.stp.StpLogic.login(StpLogic.java:438)
	at cn.dev33.satoken.stp.StpLogic.login(StpLogic.java:407)
	at cn.dev33.satoken.stp.StpUtil.login(StpUtil.java:174)
	at supie.webadmin.upms.controller.LoginController.loginAndCreateToken(LoginController.java:603)
	at supie.webadmin.upms.controller.LoginController.buildLoginDataAndLogin(LoginController.java:500)
	at supie.webadmin.upms.controller.LoginController.doLogin(LoginController.java:290)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:343)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at supie.common.datafilter.aop.DisableDataFilterAspect.around(DisableDataFilterAspect.java:37)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:627)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at supie.common.log.aop.OperationLogAspect.around(OperationLogAspect.java:116)
	at jdk.internal.reflect.GeneratedMethodAccessor395.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:627)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703)
	at supie.webadmin.upms.controller.LoginController$$SpringCGLIB$$0.doLogin(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:884)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1081)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:974)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: io.lettuce.core.RedisException: java.net.SocketException: Connection reset
	at io.lettuce.core.internal.Exceptions.bubble(Exceptions.java:83)
	at io.lettuce.core.internal.Futures.awaitOrCancel(Futures.java:250)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:967)
	... 123 common frames omitted
Caused by: java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:254)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
[DEBUG] [2025-06-20 00:24:46] T:[] S:[] U:[] [flowable-task-Executor-5] ==> ==>  Preparing: INSERT INTO zz_sys_operation_log ( log_id, description, operation_type, service_name, api_class, api_method, trace_id, elapse, request_method, request_url, request_arguments, request_ip, success, error_msg, operation_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[DEBUG] [2025-06-20 00:24:46] T:[] S:[] U:[] [flowable-task-Executor-5] ==> ==> Parameters: 1935735436110467072(Long), (String), 0(Integer), application-webadmin(String), supie.webadmin.upms.controller.LoginController(String), supie.webadmin.upms.controller.LoginController.doLogin(String), f9c7ef22c98242cdbbaf0ad9a5f3feac(String), 19587(Long), POST(String), /admin/upms/login/doLogin(String), {"password":"pI9AmEXh8nrdHlucJbD2s0kL5pZArt0xNBuqyN9%2BATp7BjfLGUBwYPrkA2GA4KiD0lmXdb1PxOIcbjgsVjhENuh2uQ9qJIcgvxuS1qBJCWvA6og%2F0jqwudvMG9V%2FioJaW%2Fz3poqboPsaY0QVDgaWYoWCaC%2F9%2FiRZQp3YELYfi28%3D","loginName":"admin"}(String), ************(String), false(Boolean), Redis exception(String), 2025-06-20 00:24:26.852(Timestamp)
[DEBUG] [2025-06-20 00:24:46] T:[] S:[] U:[] [flowable-task-Executor-5] ==> <==    Updates: 1
[DEBUG] [2025-06-20 00:25:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-06-20 00:25:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-06-20 00:25:00.018(Timestamp)
[DEBUG] [2025-06-20 00:25:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[INFO ] [2025-06-20 00:25:01] T:[49841e1848b74cae8095e0ba7322e00e] S:[] U:[] [http-nio-8084-exec-3] ==> 开始请求，url=/admin/upms/login/doLogin, reqData={"password":"fN8ziAYSvUGZoE19wTTNyKAGQTqehty74UnH%2BbGp639Awh6CN%2BNr7W1qJnqpnhSK5AZwI67lJus0NT%2Bgg2gEnJYdiY3%2Fj91xadFV8iISiE0oYfh6ANPSz9c%2BAMh46aY1tF2BFkPkrM3AJ83%2FOeHrdb3QrNejKUcr7jfo%2FAgzuZ8%3D","loginName":"admin"}
[DEBUG] [2025-06-20 00:25:01] T:[49841e1848b74cae8095e0ba7322e00e] S:[] U:[] [http-nio-8084-exec-3] ==> ==>  Preparing: SELECT user_id,login_name,password,dept_id,show_name,user_type,head_image_url,workbench_id,user_status,email,mobile,user_auth_info,create_user_id,update_user_id,create_time,update_time,deleted_flag FROM sys_user WHERE deleted_flag=1 AND login_name=?
[DEBUG] [2025-06-20 00:25:01] T:[49841e1848b74cae8095e0ba7322e00e] S:[] U:[] [http-nio-8084-exec-3] ==> ==> Parameters: admin(String)
[DEBUG] [2025-06-20 00:25:01] T:[49841e1848b74cae8095e0ba7322e00e] S:[] U:[] [http-nio-8084-exec-3] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:01] T:[49841e1848b74cae8095e0ba7322e00e] S:[] U:[] [http-nio-8084-exec-3] ==> ==>  Preparing: SELECT user_id,dept_post_id,post_id FROM sys_user_post WHERE user_id=?
[DEBUG] [2025-06-20 00:25:01] T:[49841e1848b74cae8095e0ba7322e00e] S:[] U:[] [http-nio-8084-exec-3] ==> ==> Parameters: 1921773426524033024(Long)
[DEBUG] [2025-06-20 00:25:01] T:[49841e1848b74cae8095e0ba7322e00e] S:[] U:[] [http-nio-8084-exec-3] ==> <==      Total: 0
[DEBUG] [2025-06-20 00:25:01] T:[49841e1848b74cae8095e0ba7322e00e] S:[] U:[] [http-nio-8084-exec-3] ==> ==>  Preparing: SELECT user_id,role_id FROM sys_user_role WHERE user_id=?
[DEBUG] [2025-06-20 00:25:01] T:[49841e1848b74cae8095e0ba7322e00e] S:[] U:[] [http-nio-8084-exec-3] ==> ==> Parameters: 1921773426524033024(Long)
[DEBUG] [2025-06-20 00:25:01] T:[49841e1848b74cae8095e0ba7322e00e] S:[] U:[] [http-nio-8084-exec-3] ==> <==      Total: 0
[DEBUG] [2025-06-20 00:25:02] T:[49841e1848b74cae8095e0ba7322e00e] S:[] U:[] [http-nio-8084-exec-3] ==> ==>  Preparing: SELECT dept_id,dept_name,show_order,parent_id,deleted_flag,create_user_id,update_user_id,create_time,update_time FROM sys_dept WHERE dept_id=? AND deleted_flag=1
[DEBUG] [2025-06-20 00:25:02] T:[49841e1848b74cae8095e0ba7322e00e] S:[] U:[] [http-nio-8084-exec-3] ==> ==> Parameters: 1923195822141345795(Long)
[DEBUG] [2025-06-20 00:25:02] T:[49841e1848b74cae8095e0ba7322e00e] S:[] U:[] [http-nio-8084-exec-3] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:02] T:[49841e1848b74cae8095e0ba7322e00e] S:[] U:[] [http-nio-8084-exec-3] ==> ==>  Preparing: SELECT menu_id,data_belong_type,deleted_flag,parent_id,menu_name,menu_type,form_router_name,online_form_id,online_menu_perm_type,report_page_id,online_flow_entry_id,show_order,icon,extra_data,create_user_id,create_time,update_user_id,update_time FROM sys_menu ORDER BY show_order ASC
[DEBUG] [2025-06-20 00:25:02] T:[49841e1848b74cae8095e0ba7322e00e] S:[] U:[] [http-nio-8084-exec-3] ==> ==> Parameters: 
[DEBUG] [2025-06-20 00:25:02] T:[49841e1848b74cae8095e0ba7322e00e] S:[] U:[] [http-nio-8084-exec-3] ==> <==      Total: 26
[INFO ] [2025-06-20 00:25:02] T:[49841e1848b74cae8095e0ba7322e00e] S:[] U:[] [http-nio-8084-exec-3] ==> 请求完成, url=/admin/upms/login/doLogin，elapse=1252ms, respData={"data":{"deptName":"公司总部","menuList":[{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"formRouterName\":\"index\",\"permCodeList\":[]}","extraObject":{"bindType":0,"formRouterName":"index","permCodeList":[]},"formRouterName":"index","menuId":1867406109141110785,"menuName":"业务系统","menuType":0,"parentId":1001,"showOrder":1,"updateTime":1748341360000,"updateUserId":1742014705053995008},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","extraObject":{"bindType":0,"permCodeList":[]},"formRouterName":"task-monitor","menuId":1932002347219685377,"menuName":"任务运行监控","menuType":1,"parentId":1932002347219685378,"showOrder":1,"updateTime":1749460230000,"updateUserId":1923195822141345792},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","extraObject":{"bindType":0,"permCodeList":[]},"formRouterName":"formSysUser","menuId":1867406109346631682,"menuName":"用户管理","menuType":1,"parentId":1867406109774450688,"showOrder":1,"updateTime":1734059286000,"updateUserId":1742014705053995008},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","extraObject":{"bindType":0,"permCodeList":[]},"formRouterName":"overview","menuId":1932002344703102980,"menuName":"总览","menuType":1,"parentId":1867406109141110785,"showOrder":1,"updateTime":1749460230000,"updateUserId":1923195822141345792},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","extraObject":{"bindType":0,"permCodeList":[]},"formRouterName":"resource-pool-index","menuId":1932002344703102978,"menuName":"资源池管理","menuType":1,"parentId":1932002344703102979,"showOrder":1,"updateTime":1749460230000,"updateUserId":1923195822141345792},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","extraObject":{"bindType":0,"permCodeList":[]},"formRouterName":"service-resource-index","menuId":1932002344703102977,"menuName":"服务资源管理","menuType":1,"parentId":1932002344703102976,"showOrder":1,"updateTime":1749460230000,"updateUserId":1923195822141345792},{"createTime":1749460218000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","extraObject":{"bindType":0,"permCodeList":[]},"formRouterName":"overview","menuId":1932002300935540736,"menuName":"总览","menuType":1,"parentId":1867406109141110785,"showOrder":1,"updateTime":1749460219000,"updateUserId":1923195822141345792},{"createTime":1749460218000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","extraObject":{"bindType":0,"permCodeList":[]},"formRouterName":"service-resource-index","menuId":1932002301136867329,"menuName":"服务资源管理","menuType":1,"parentId":1932002301208170496,"showOrder":1,"updateTime":1749460219000,"updateUserId":1923195822141345792},{"createTime":1749460218000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","extraObject":{"bindType":0,"permCodeList":[]},"formRouterName":"resource-pool-index","menuId":1932002301136867330,"menuName":"资源池管理","menuType":1,"parentId":1932002301136867328,"showOrder":1,"updateTime":1749460220000,"updateUserId":1923195822141345792},{"createTime":1749460219000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","extraObject":{"bindType":0,"permCodeList":[]},"formRouterName":"task-monitor","menuId":1932002304458756097,"menuName":"任务运行监控","menuType":1,"parentId":1932002304458756098,"showOrder":1,"updateTime":1749460220000,"updateUserId":1923195822141345792},{"createTime":1720599610000,"createUserId":1742014705053995008,"dataBelongType":"","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","extraObject":{"bindType":0,"permCodeList":[]},"menuId":1001,"menuName":"业务功能菜单","menuType":0,"showOrder":1,"updateTime":1720599610000,"updateUserId":1742014705053995008},{"createTime":1749460219000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","extraObject":{"bindType":0,"permCodeList":[]},"formRouterName":"task-template","menuId":1932002304152571904,"menuName":"任务模板管理","menuType":1,"parentId":1932002304458756098,"showOrder":2,"updateTime":1749460219000,"updateUserId":1923195822141345792},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","extraObject":{"bindType":0,"permCodeList":[]},"formRouterName":"task-template","menuId":1932002347219685376,"menuName":"任务模板管理","menuType":1,"parentId":1932002347219685378,"showOrder":2,"updateTime":1749460230000,"updateUserId":1923195822141345792},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"formRouterName\":\"service-resource\",\"permCodeList\":[]}","extraObject":{"bindType":0,"formRouterName":"service-resource","permCodeList":[]},"formRouterName":"service-resource","menuId":1932002344703102976,"menuName":"服务资源管理","menuType":0,"parentId":1867406109141110785,"showOrder":2,"updateTime":1749696050000,"updateUserId":1923195822141345792},{"createTime":1749460218000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"formRouterName\":\"service-resource\",\"permCodeList\":[]}","extraObject":{"bindType":0,"formRouterName":"service-resource","permCodeList":[]},"formRouterName":"service-resource","menuId":1932002301208170496,"menuName":"服务资源管理","menuType":0,"parentId":1867406109141110785,"showOrder":2,"updateTime":1749696050000,"updateUserId":1923195822141345792},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","extraObject":{"bindType":0,"permCodeList":[]},"formRouterName":"systemManage","menuId":1867406109774450688,"menuName":"系统管理","menuType":0,"parentId":1001,"showOrder":2,"updateTime":1749460219000,"updateUserId":1923195822141345792},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","extraObject":{"bindType":0,"permCodeList":[]},"formRouterName":"formSysDept","menuId":1867406109346631683,"menuName":"部门管理","menuType":1,"parentId":1867406109774450688,"showOrder":2,"updateTime":1734059286000,"updateUserId":1742014705053995008},{"createTime":1749460219000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","extraObject":{"bindType":0,"permCodeList":[]},"formRouterName":"image-management","menuId":1932002304458756096,"menuName":"镜像管理","menuType":1,"parentId":1932002304458756098,"showOrder":3,"updateTime":1749460219000,"updateUserId":1923195822141345792},{"createTime":1749460218000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"formRouterName\":\"resource-pool\",\"permCodeList\":[]}","extraObject":{"bindType":0,"formRouterName":"resource-pool","permCodeList":[]},"formRouterName":"resource-pool","menuId":1932002301136867328,"menuName":"资源池管理","menuType":0,"parentId":1867406109141110785,"showOrder":3,"updateTime":1749696050000,"updateUserId":1923195822141345792},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"formRouterName\":\"resource-pool\",\"permCodeList\":[]}","extraObject":{"bindType":0,"formRouterName":"resource-pool","permCodeList":[]},"formRouterName":"resource-pool","menuId":1932002344703102979,"menuName":"资源池管理","menuType":0,"parentId":1867406109141110785,"showOrder":3,"updateTime":1749696050000,"updateUserId":1923195822141345792},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","extraObject":{"bindType":0,"permCodeList":[]},"formRouterName":"formSysRole","menuId":1867406109346631680,"menuName":"角色管理","menuType":1,"parentId":1867406109774450688,"showOrder":3,"updateTime":1734059286000,"updateUserId":1742014705053995008},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","extraObject":{"bindType":0,"permCodeList":[]},"formRouterName":"image-management","menuId":1932002347223879680,"menuName":"镜像管理","menuType":1,"parentId":1932002347219685378,"showOrder":3,"updateTime":1749460230000,"updateUserId":1923195822141345792},{"createTime":1749460219000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","extraObject":{"bindType":0,"permCodeList":[]},"formRouterName":"task-scheduling","menuId":1932002304458756098,"menuName":"任务调度","menuType":0,"parentId":1867406109141110785,"showOrder":4,"updateTime":1749460219000,"updateUserId":1923195822141345792},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","extraObject":{"bindType":0,"permCodeList":[]},"formRouterName":"formSysMenu","menuId":1867406109371797504,"menuName":"菜单管理","menuType":1,"parentId":1867406109774450688,"showOrder":4,"updateTime":1734059286000,"updateUserId":1742014705053995008},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","extraObject":{"bindType":0,"permCodeList":[]},"formRouterName":"task-scheduling","menuId":1932002347219685378,"menuName":"任务调度","menuType":0,"parentId":1867406109141110785,"showOrder":4,"updateTime":1749460230000,"updateUserId":1923195822141345792},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","extraObject":{"bindType":0,"permCodeList":[]},"formRouterName":"formSysDataPerm","menuId":1867406109346631681,"menuName":"数据权限管理","menuType":1,"parentId":1867406109774450688,"showOrder":5,"updateTime":1734059286000,"updateUserId":1742014705053995008}],"showName":"管理员","tokenData":"d134552f-b98e-4ecd-ab0a-1cc64b737e39","loginName":"admin","deptId":1923195822141345795,"isAdmin":true,"userId":1921773426524033024,"permCodeList":[]},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[DEBUG] [2025-06-20 00:25:02] T:[] S:[] U:[] [flowable-task-Executor-6] ==> ==>  Preparing: INSERT INTO zz_sys_operation_log ( log_id, description, operation_type, service_name, api_class, api_method, session_id, trace_id, elapse, request_method, request_url, request_arguments, request_ip, success, operator_id, operator_name, operation_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[DEBUG] [2025-06-20 00:25:02] T:[] S:[] U:[] [flowable-task-Executor-6] ==> ==> Parameters: 1935735579836682240(Long), (String), 0(Integer), application-webadmin(String), supie.webadmin.upms.controller.LoginController(String), supie.webadmin.upms.controller.LoginController.doLogin(String), Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39(String), 49841e1848b74cae8095e0ba7322e00e(String), 1312(Long), POST(String), /admin/upms/login/doLogin(String), {"password":"fN8ziAYSvUGZoE19wTTNyKAGQTqehty74UnH%2BbGp639Awh6CN%2BNr7W1qJnqpnhSK5AZwI67lJus0NT%2Bgg2gEnJYdiY3%2Fj91xadFV8iISiE0oYfh6ANPSz9c%2BAMh46aY1tF2BFkPkrM3AJ83%2FOeHrdb3QrNejKUcr7jfo%2FAgzuZ8%3D","loginName":"admin"}(String), ************(String), true(Boolean), 1921773426524033024(Long), admin(String), 2025-06-20 00:25:01.118(Timestamp)
[DEBUG] [2025-06-20 00:25:02] T:[] S:[] U:[] [flowable-task-Executor-6] ==> <==    Updates: 1
[INFO ] [2025-06-20 00:25:03] T:[915f44018e814e688d929637559690a6] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> 开始请求，url=/admin/app/schResourceInfo/getResourceStatistics, reqData={}
[INFO ] [2025-06-20 00:25:03] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> 开始请求，url=/admin/app/schResourceInfo/list, reqData={}
[INFO ] [2025-06-20 00:25:03] T:[4581a3f8550a45dba959db200ca8c50c] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> 开始请求，url=/admin/app/schNodeBasicMetrics/resourceSort, reqData={}
[INFO ] [2025-06-20 00:25:03] T:[4d12f1b75dce456f849f524bd1b5274d] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-3] ==> 开始请求，url=/admin/app/schResourceInfo/hostSampling, reqData={}
[INFO ] [2025-06-20 00:25:03] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> 开始请求，url=/admin/app/schTaskInfo/taskOccupyResource, reqData={}
[DEBUG] [2025-06-20 00:25:03] T:[915f44018e814e688d929637559690a6] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT COALESCE(COUNT(*), 0) AS 'resourceTotal', COALESCE(SUM( CASE WHEN cpu_core_count != 'Unknown' AND cpu_core_count REGEXP '^[0-9]+(\\.[0-9]+)?$' THEN CAST(cpu_core_count AS DECIMAL(20,2)) ELSE 0 END ), 0) AS 'cpuCoreTotal', COALESCE(SUM( CASE WHEN memory_capacity != 'Unknown' AND memory_capacity REGEXP '^[0-9]+(\\.[0-9]+)?$' THEN CAST(memory_capacity AS DECIMAL(20,2)) ELSE 0 END ), 0) AS 'memoryTotal', COALESCE(SUM( CASE WHEN graphics_memory REGEXP '^[0-9]+(\\.[0-9]+)?$' THEN CAST(graphics_memory AS DECIMAL(20,2)) ELSE 0 END ), 0) AS 'gpuMemoryTotal', COALESCE(SUM(CASE WHEN status = 'offline' THEN 1 ELSE 0 END), 0) AS 'offlineCount', COALESCE(SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END), 0) AS 'activeCount', COALESCE(SUM(CASE WHEN status = 'maintenance' THEN 1 ELSE 0 END), 0) AS 'maintenanceCount', COALESCE(SUM(CASE WHEN status = 'error' THEN 1 ELSE 0 END), 0) AS 'errorCount' FROM sch_resource_info WHERE is_delete = 1;
[DEBUG] [2025-06-20 00:25:03] T:[915f44018e814e688d929637559690a6] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: 
[DEBUG] [2025-06-20 00:25:03] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==>  Preparing: SELECT * FROM sch_resource_info WHERE sch_resource_info.is_delete = 1 ORDER BY sch_resource_info.id DESC
[DEBUG] [2025-06-20 00:25:03] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==> Parameters: 
[DEBUG] [2025-06-20 00:25:03] T:[915f44018e814e688d929637559690a6] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:03] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE is_delete=1
[DEBUG] [2025-06-20 00:25:03] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 
[DEBUG] [2025-06-20 00:25:03] T:[915f44018e814e688d929637559690a6] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT COUNT(*) AS total_count FROM sch_compute_device d WHERE EXISTS ( SELECT 1 FROM sch_virtual_compute_card_situation s WHERE s.compute_device_id = d.id AND s.is_delete = 1 ) AND d.is_delete = 1
[DEBUG] [2025-06-20 00:25:03] T:[915f44018e814e688d929637559690a6] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: 
[DEBUG] [2025-06-20 00:25:03] T:[4d12f1b75dce456f849f524bd1b5274d] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-3] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE is_delete=1
[DEBUG] [2025-06-20 00:25:03] T:[4d12f1b75dce456f849f524bd1b5274d] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-3] ==> ==> Parameters: 
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> <==      Total: 16
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 16
[DEBUG] [2025-06-20 00:25:04] T:[915f44018e814e688d929637559690a6] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:04] T:[915f44018e814e688d929637559690a6] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT COUNT(DISTINCT resource_id) AS count FROM sch_task_info WHERE is_delete = 1 and status = 'running'
[DEBUG] [2025-06-20 00:25:04] T:[915f44018e814e688d929637559690a6] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: 
[DEBUG] [2025-06-20 00:25:04] T:[4d12f1b75dce456f849f524bd1b5274d] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-3] ==> <==      Total: 16
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT id,exit_code,fail_reason,scale_plan_id,dict_id,container_name,container_status,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,task_name,status,task_priority,graphic_needed_mb,memory_needed_mb,cpu_need,pool_id,resource_id,task_image_id,compute_device_id,partition_id,run_command,env_config,release_policy,start_time,end_tiem,estimat_time,scheduling_policies,approve_state,allow_preemption FROM sch_task_info WHERE is_delete=1 AND (status = ? AND resource_id IN (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?))
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: running(String), 1932331930129272832(Long), 1932336532371279872(Long), 1932339656674775040(Long), 1932700819912658944(Long), 1932704139536502784(Long), 1932705890410958848(Long), 1932709585647308800(Long), 1932717669144858624(Long), 1932722934955118592(Long), 1932725193193558016(Long), 1932729802632990720(Long), 1932729975991963648(Long), 1932730175653416960(Long), 1933048812436197376(Long), 1933063901780381696(Long), 1933097753617895424(Long)
[DEBUG] [2025-06-20 00:25:04] T:[4581a3f8550a45dba959db200ca8c50c] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==>  Preparing: SELECT t1.* FROM sch_node_basic_metrics t1 JOIN ( SELECT resource_id, MAX(id) AS max_id FROM sch_node_basic_metrics WHERE is_delete = 1 GROUP BY resource_id ) t2 ON t1.resource_id = t2.resource_id AND t1.id = t2.max_id WHERE t1.is_delete = 1 ORDER BY t1.overall_cpu_usage DESC, t1.memory_utilization DESC;
[DEBUG] [2025-06-20 00:25:04] T:[915f44018e814e688d929637559690a6] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id IN ( ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? ) AND ts >= now() - INTERVAL 60 MINUTE; ORDER BY ts DESC, resource_id DESC
[DEBUG] [2025-06-20 00:25:04] T:[4581a3f8550a45dba959db200ca8c50c] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==> Parameters: 
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==> Parameters: 1933097753617895424(Long), 1933063901780381696(Long), 1933048812436197376(Long), 1932730175653416960(Long), 1932729975991963648(Long), 1932729802632990720(Long), 1932725193193558016(Long), 1932722934955118592(Long), 1932717669144858624(Long), 1932709585647308800(Long), 1932705890410958848(Long), 1932704139536502784(Long), 1932700819912658944(Long), 1932339656674775040(Long), 1932336532371279872(Long), 1932331930129272832(Long)
[DEBUG] [2025-06-20 00:25:04] T:[915f44018e814e688d929637559690a6] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT compute_device_id FROM sch_task_info WHERE is_delete=1 AND (is_delete = ? AND compute_device_id IS NOT NULL) GROUP BY compute_device_id
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 19
[DEBUG] [2025-06-20 00:25:04] T:[915f44018e814e688d929637559690a6] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: 1(Integer)
[DEBUG] [2025-06-20 00:25:04] T:[915f44018e814e688d929637559690a6] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:04] T:[4d12f1b75dce456f849f524bd1b5274d] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-3] ==> ==>  Preparing: SELECT ts AS tsLocal,* FROM sch_node_basic_metrics WHERE resource_id IN ? AND ts >= ? AND ts <= ? ORDER BY ts Asc
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> <==      Total: 0
[DEBUG] [2025-06-20 00:25:04] T:[915f44018e814e688d929637559690a6] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT compute_device_id FROM sch_virtual_compute_card_situation WHERE is_delete=1 AND (is_delete = ? AND compute_device_id IS NOT NULL) GROUP BY compute_device_id
[DEBUG] [2025-06-20 00:25:04] T:[915f44018e814e688d929637559690a6] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: 1(Integer)
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==>  Preparing: SELECT t.* FROM sch_card_monitor t JOIN ( SELECT resource_id, max(ts) AS latest_ts FROM sch_card_monitor WHERE resource_id IN ( ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? ) GROUP BY resource_id ) m ON t.resource_id = m.resource_id AND t.ts = m.latest_ts;
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==> Parameters: 1933097753617895424(Long), 1933063901780381696(Long), 1933048812436197376(Long), 1932730175653416960(Long), 1932729975991963648(Long), 1932729802632990720(Long), 1932725193193558016(Long), 1932722934955118592(Long), 1932717669144858624(Long), 1932709585647308800(Long), 1932705890410958848(Long), 1932704139536502784(Long), 1932700819912658944(Long), 1932339656674775040(Long), 1932336532371279872(Long), 1932331930129272832(Long)
[DEBUG] [2025-06-20 00:25:04] T:[4d12f1b75dce456f849f524bd1b5274d] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-3] ==> ==> Parameters: [1932331930129272832, 1932336532371279872, 1932339656674775040, 1932700819912658944, 1932704139536502784, 1932705890410958848, 1932709585647308800, 1932717669144858624, 1932722934955118592, 1932725193193558016, 1932729802632990720, 1932729975991963648, 1932730175653416960, 1933048812436197376, 1933063901780381696, 1933097753617895424](ListN), 2025-06-19T00:25:04.023277500(String), 2025-06-20T00:25:04.025334500(String)
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? ORDER BY ts DESC LiMIT 1;
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932730175653416960(Long)
[DEBUG] [2025-06-20 00:25:04] T:[4d12f1b75dce456f849f524bd1b5274d] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-3] ==> <==      Total: 0
[DEBUG] [2025-06-20 00:25:04] T:[915f44018e814e688d929637559690a6] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 14
[DEBUG] [2025-06-20 00:25:04] T:[4d12f1b75dce456f849f524bd1b5274d] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-3] ==> ==>  Preparing: SELECT ts AS tsLocal,* FROM sch_card_monitor WHERE resource_id in ? AND ts >= ? AND ts <= ? ORDER BY ts Asc
[DEBUG] [2025-06-20 00:25:04] T:[915f44018e814e688d929637559690a6] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (is_delete = ? AND id IN (?,?,?,?,?,?,?,?,?,?,?,?,?,?))
[DEBUG] [2025-06-20 00:25:04] T:[915f44018e814e688d929637559690a6] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: 1(Integer), 1932725194112110592(Long), 1932007868370784256(Long), 1932717670214406144(Long), 1932730176622301186(Long), 1932709586645553155(Long), 1933063902724100096(Long), 1932705891413397506(Long), 1933048813337972736(Long), 1932705891413397507(Long), 1932705891413397504(Long), 1933097754570002433(Long), 1932330840201629697(Long), 1932339658595766273(Long), 1932704140606050304(Long)
[DEBUG] [2025-06-20 00:25:04] T:[4581a3f8550a45dba959db200ca8c50c] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> <==      Total: 34
[DEBUG] [2025-06-20 00:25:04] T:[4581a3f8550a45dba959db200ca8c50c] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE is_delete=1
[DEBUG] [2025-06-20 00:25:04] T:[4581a3f8550a45dba959db200ca8c50c] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==> Parameters: 
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:04] T:[4d12f1b75dce456f849f524bd1b5274d] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-3] ==> ==> Parameters: [1932331930129272832, 1932336532371279872, 1932339656674775040, 1932700819912658944, 1932704139536502784, 1932705890410958848, 1932709585647308800, 1932717669144858624, 1932722934955118592, 1932725193193558016, 1932729802632990720, 1932729975991963648, 1932730175653416960, 1933048812436197376, 1933063901780381696, 1933097753617895424](ListN), 2025-06-19T00:25:04.023277500(String), 2025-06-20T00:25:04.025334500(String)
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? AND ts = ? ORDER BY ts DESC
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932730175653416960(Long), 2025-06-16 11:41:20.198(Timestamp)
[DEBUG] [2025-06-20 00:25:04] T:[915f44018e814e688d929637559690a6] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 12
[DEBUG] [2025-06-20 00:25:04] T:[915f44018e814e688d929637559690a6] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT s.status, COALESCE(t.count, 0) AS count FROM ( SELECT 'running' AS status UNION ALL SELECT 'queued' UNION ALL SELECT 'starting' UNION ALL SELECT 'pending' UNION ALL SELECT 'stop' UNION ALL SELECT 'finished' UNION ALL SELECT 'failed' ) s LEFT JOIN ( SELECT status, COUNT(*) AS count FROM sch_task_info WHERE is_delete = 1 AND status IN ('pending', 'queued', 'starting', 'running', 'stop', 'finished', 'failed') GROUP BY status ) t ON s.status = t.status ORDER BY FIELD(s.status, 'running', 'queued', 'starting', 'pending', 'stop', 'finished', 'failed' )
[DEBUG] [2025-06-20 00:25:04] T:[915f44018e814e688d929637559690a6] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: 
[DEBUG] [2025-06-20 00:25:04] T:[4581a3f8550a45dba959db200ca8c50c] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> <==      Total: 16
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? ORDER BY ts DESC LiMIT 1;
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932331930129272832(Long)
[INFO ] [2025-06-20 00:25:04] T:[4581a3f8550a45dba959db200ca8c50c] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> 请求完成, url=/admin/app/schNodeBasicMetrics/resourceSort，elapse=333ms, respData={"data":[{"cpuUsage":3.238029,"memoryUtil":12.253810,"resourceId":1933097753617895424,"resourceName":"001","videoUtil":0.000000,"timestamp":"2025-06-14T18:10:00.164"},{"cpuUsage":3.235570,"memoryUtil":13.178408,"resourceId":1932729802632990720,"resourceName":"test005","videoUtil":0.000000,"timestamp":"2025-06-16T11:43:00.258"},{"cpuUsage":3.235553,"memoryUtil":12.989904,"resourceId":1932331930129272832,"resourceName":"test","videoUtil":0.000000,"timestamp":"2025-06-16T11:46:00.171"},{"cpuUsage":3.235552,"memoryUtil":12.987550,"resourceId":1932336532371279872,"resourceName":"test6","videoUtil":0.000000,"timestamp":"2025-06-16T11:46:00.171"},{"cpuUsage":3.235552,"memoryUtil":12.986413,"resourceId":1932339656674775040,"resourceName":"test333","videoUtil":0.000000,"timestamp":"2025-06-16T11:46:00.171"},{"cpuUsage":3.235552,"memoryUtil":12.984105,"resourceId":1932709585647308800,"resourceName":"test002","videoUtil":0.000000,"timestamp":"2025-06-16T11:46:00.171"},{"cpuUsage":3.235552,"memoryUtil":12.983728,"resourceId":1932717669144858624,"resourceName":"test003","videoUtil":0.000000,"timestamp":"2025-06-16T11:46:00.171"},{"cpuUsage":3.235551,"memoryUtil":12.981803,"resourceId":1932722934955118592,"resourceName":"222","videoUtil":0.000000,"timestamp":"2025-06-16T11:46:00.171"},{"cpuUsage":3.235550,"memoryUtil":12.980103,"resourceId":1932704139536502784,"resourceName":"test001","videoUtil":0.000000,"timestamp":"2025-06-16T11:46:00.171"},{"cpuUsage":3.235550,"memoryUtil":12.976910,"resourceId":1932725193193558016,"resourceName":"testHH2","videoUtil":0.000000,"timestamp":"2025-06-16T11:46:00.171"},{"cpuUsage":3.235550,"memoryUtil":12.976068,"resourceId":1932700819912658944,"resourceName":"test","videoUtil":0.000000,"timestamp":"2025-06-16T11:46:00.171"},{"cpuUsage":3.235549,"memoryUtil":12.984004,"resourceId":1932705890410958848,"resourceName":"testHH","videoUtil":0.000000,"timestamp":"2025-06-16T11:46:00.171"},{"cpuUsage":3.235465,"memoryUtil":13.166035,"resourceId":1932730175653416960,"resourceName":"test007","videoUtil":0.000000,"timestamp":"2025-06-16T11:41:20.198"},{"cpuUsage":3.235459,"memoryUtil":13.137604,"resourceId":1932729975991963648,"resourceName":"test006","videoUtil":0.000000,"timestamp":"2025-06-16T11:41:20.198"},{"cpuUsage":3.235454,"memoryUtil":13.180117,"resourceId":1933063901780381696,"resourceName":"test","videoUtil":0.000000,"timestamp":"2025-06-16T11:41:00.273"},{"cpuUsage":3.235454,"memoryUtil":13.179398,"resourceId":1933048812436197376,"resourceName":"资源1001","videoUtil":0.000000,"timestamp":"2025-06-16T11:41:00.273"}],"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":[{"$ref":"$.data[0]"},{"$ref":"$.data[1]"},{"$ref":"$.data[2]"},{"$ref":"$.data[3]"},{"$ref":"$.data[4]"},{"$ref":"$.data[5]"},{"$ref":"$.data[6]"},{"$ref":"$.data[7]"},{"$ref":"$.data[8]"},{"$ref":"$.data[9]"},{"$ref":"$.data[10]"},{"$ref":"$.data[11]"},{"$ref":"$.data[12]"},{"$ref":"$.data[13]"},{"$ref":"$.data[14]"},{"$ref":"$.data[15]"}],"success":true}
[DEBUG] [2025-06-20 00:25:04] T:[915f44018e814e688d929637559690a6] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 7
[DEBUG] [2025-06-20 00:25:04] T:[915f44018e814e688d929637559690a6] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT COUNT( * ) AS total FROM sch_resource_pool WHERE is_delete=1
[DEBUG] [2025-06-20 00:25:04] T:[915f44018e814e688d929637559690a6] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: 
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? AND ts = ? ORDER BY ts DESC
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932331930129272832(Long), 2025-06-16 11:46:00.171(Timestamp)
[DEBUG] [2025-06-20 00:25:04] T:[915f44018e814e688d929637559690a6] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 1
[INFO ] [2025-06-20 00:25:04] T:[915f44018e814e688d929637559690a6] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> 请求完成, url=/admin/app/schResourceInfo/getResourceStatistics，elapse=399ms, respData={"data":{"resourceTotal":16,"activeCount":13,"resourceRateUsage":62.5,"offlineCount":1,"totalPool":8,"memoryTotal":12371528.00,"taskStatusCount":[{"count":20,"status":"running"},{"count":0,"status":"queued"},{"count":0,"status":"starting"},{"count":0,"status":"pending"},{"count":2,"status":"stop"},{"count":2,"status":"finished"},{"count":17,"status":"failed"}],"gpuMemoryTotal":4194304.00,"cpuCoreTotal":3072.00,"virtualResoureCount":12,"maintenanceCount":1,"errorCount":1},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? ORDER BY ts DESC LiMIT 1;
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932705890410958848(Long)
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? AND ts = ? ORDER BY ts DESC
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932705890410958848(Long), 2025-06-16 11:46:00.171(Timestamp)
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? ORDER BY ts DESC LiMIT 1;
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932705890410958848(Long)
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? AND ts = ? ORDER BY ts DESC
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932705890410958848(Long), 2025-06-16 11:46:00.171(Timestamp)
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? ORDER BY ts DESC LiMIT 1;
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932729975991963648(Long)
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? AND ts = ? ORDER BY ts DESC
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932729975991963648(Long), 2025-06-16 11:41:20.198(Timestamp)
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> <==      Total: 128
[DEBUG] [2025-06-20 00:25:04] T:[4d12f1b75dce456f849f524bd1b5274d] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-3] ==> <==      Total: 0
[INFO ] [2025-06-20 00:25:04] T:[4d12f1b75dce456f849f524bd1b5274d] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-3] ==> 请求完成, url=/admin/app/schResourceInfo/hostSampling，elapse=634ms, respData={"data":{"NPU":{},"CPU":[]},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==> Parameters: 1933097753617895424(Long)
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? ORDER BY ts DESC LiMIT 1;
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932729975991963648(Long)
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? AND ts = ? ORDER BY ts DESC
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932729975991963648(Long), 2025-06-16 11:41:20.198(Timestamp)
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==> Parameters: 1933063901780381696(Long)
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? ORDER BY ts DESC LiMIT 1;
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932709585647308800(Long)
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==> Parameters: 1933048812436197376(Long)
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? AND ts = ? ORDER BY ts DESC
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932709585647308800(Long), 2025-06-16 11:46:00.171(Timestamp)
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? ORDER BY ts DESC LiMIT 1;
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932729975991963648(Long)
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==> Parameters: 1932730175653416960(Long)
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? AND ts = ? ORDER BY ts DESC
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932729975991963648(Long), 2025-06-16 11:41:20.198(Timestamp)
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? ORDER BY ts DESC LiMIT 1;
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932729802632990720(Long)
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==> Parameters: 1932729975991963648(Long)
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? AND ts = ? ORDER BY ts DESC
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932729802632990720(Long), 2025-06-16 11:43:00.258(Timestamp)
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==> Parameters: 1932729802632990720(Long)
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? ORDER BY ts DESC LiMIT 1;
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932704139536502784(Long)
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==> Parameters: 1932725193193558016(Long)
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? AND ts = ? ORDER BY ts DESC
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932704139536502784(Long), 2025-06-16 11:46:00.171(Timestamp)
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==> Parameters: 1932722934955118592(Long)
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? ORDER BY ts DESC LiMIT 1;
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1933063901780381696(Long)
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==> Parameters: 1932717669144858624(Long)
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? AND ts = ? ORDER BY ts DESC
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1933063901780381696(Long), 2025-06-16 11:41:00.273(Timestamp)
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? ORDER BY ts DESC LiMIT 1;
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932705890410958848(Long)
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:04] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==> Parameters: 1932709585647308800(Long)
[DEBUG] [2025-06-20 00:25:04] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:05] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_card_monitor WHERE compute_device_id =? AND ts = ? ORDER BY ts DESC
[DEBUG] [2025-06-20 00:25:05] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932705891413397506(Long), 2025-06-16 11:46:00.171(Timestamp)
[DEBUG] [2025-06-20 00:25:05] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:05] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:05] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==> Parameters: 1932705890410958848(Long)
[DEBUG] [2025-06-20 00:25:05] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:05] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:05] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==> Parameters: 1932704139536502784(Long)
[DEBUG] [2025-06-20 00:25:05] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:05] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:05] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==> Parameters: 1932700819912658944(Long)
[DEBUG] [2025-06-20 00:25:05] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:05] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:05] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==> Parameters: 1932339656674775040(Long)
[DEBUG] [2025-06-20 00:25:05] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:05] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? AND ts = ? ORDER BY ts DESC
[DEBUG] [2025-06-20 00:25:05] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932705890410958848(Long), 2025-06-16 11:46:00.171(Timestamp)
[DEBUG] [2025-06-20 00:25:05] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:05] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? ORDER BY ts DESC LiMIT 1;
[DEBUG] [2025-06-20 00:25:05] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1933097753617895424(Long)
[DEBUG] [2025-06-20 00:25:05] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:05] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:05] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==> Parameters: 1932336532371279872(Long)
[DEBUG] [2025-06-20 00:25:05] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:05] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? AND ts = ? ORDER BY ts DESC
[DEBUG] [2025-06-20 00:25:05] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1933097753617895424(Long), 2025-06-14 18:10:00.164(Timestamp)
[DEBUG] [2025-06-20 00:25:05] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:05] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:05] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==> Parameters: 1932331930129272832(Long)
[DEBUG] [2025-06-20 00:25:05] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:05] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? ORDER BY ts DESC LiMIT 1;
[DEBUG] [2025-06-20 00:25:05] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932725193193558016(Long)
[DEBUG] [2025-06-20 00:25:05] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:05] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:05] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? AND ts = ? ORDER BY ts DESC
[DEBUG] [2025-06-20 00:25:05] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932725193193558016(Long), 2025-06-16 11:46:00.171(Timestamp)
[INFO ] [2025-06-20 00:25:05] T:[378f29d95b9642be8f7efbbc8fc3fa05] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> 请求完成, url=/admin/app/schResourceInfo/list，elapse=1419ms, respData={"data":{"dataList":[{"cardType":"昬腾 910A","computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749721394000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"graphicsMemory":"262144","hostIp":"************","id":1933097753617895424,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"001","fbUsedUtil":0,"resourceInfoId":1933097753617895424},"password":"Ascend12!@","port":40086,"productType":1,"resourceName":"001","resourceType":"x86","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749721394000,"updateUserId":1921773426524033024,"usedMemory":"96206.5"},{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749713323000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"graphicsMemory":"262144","hostIp":"*************","id":1933063901780381696,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"test","fbUsedUtil":0,"resourceInfoId":1933063901780381696},"password":"Ascend12!@","port":40086,"resourceName":"test","resourceType":"huawei","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749713323000,"updateUserId":1923195822141345792,"usedMemory":"95288.6"},{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749709726000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"graphicsMemory":"262144","hostIp":"*************","id":1933048812436197376,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"资源1001","fbUsedUtil":0,"resourceInfoId":1933048812436197376},"password":"Ascend12!@","port":40086,"resourceName":"资源1001","resourceType":"huawei","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749709726000,"updateUserId":1923195822141345792,"usedMemory":"93889.1"},{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749633757000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932730175653416960,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"test007","fbUsedUtil":0,"resourceInfoId":1932730175653416960},"password":"Ascend12!@","port":40086,"resourceName":"test007","resourceType":"x86","status":"offline","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749633757000,"updateUserId":1923195822141345792,"usedMemory":"92160.0"},{"cardType":"昬腾 910A","computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749633709000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932729975991963648,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"test006","fbUsedUtil":0,"resourceInfoId":1932729975991963648},"password":"Ascend12!@","port":40086,"productType":1,"resourceName":"test006","resourceType":"x86","status":"error","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749633709000,"updateUserId":1921773426524033024,"usedMemory":"92148.9"},{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749633668000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932729802632990720,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"test005","fbUsedUtil":0,"resourceInfoId":1932729802632990720},"password":"Ascend12!@","port":40086,"resourceName":"test005","resourceType":"x86","status":"maintenance","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749633668000,"updateUserId":1923195822141345792,"usedMemory":"92093.2"},{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749632569000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932725193193558016,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"testHH2","fbUsedUtil":0,"resourceInfoId":1932725193193558016},"password":"Ascend12!@","port":40086,"resourceName":"testHH2","resourceType":"huawei","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749632569000,"updateUserId":1923195822141345792,"usedMemory":"91710.6"},{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749632031000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932722934955118592,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"222","fbUsedUtil":0,"resourceInfoId":1932722934955118592},"password":"Ascend12!@","port":40086,"resourceName":"222","resourceType":"x86","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749632031000,"updateUserId":1923195822141345792,"usedMemory":"91962.7"},{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749630775000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932717669144858624,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"test003","fbUsedUtil":0,"resourceInfoId":1932717669144858624},"password":"Ascend12!@","port":40086,"resourceName":"test003","resourceType":"x86","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749630775000,"updateUserId":1923195822141345792,"usedMemory":"91731.7"},{"cardType":"昬腾 910A","computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749628848000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932709585647308800,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"test002","fbUsedUtil":0,"resourceInfoId":1932709585647308800},"password":"Ascend12!@","port":40086,"productType":1,"resourceName":"test002","resourceType":"huawei","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749628848000,"updateUserId":1921773426524033024,"usedMemory":"90748.8"},{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749627967000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932705890410958848,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"testHH","fbUsedUtil":0,"resourceInfoId":1932705890410958848},"password":"Ascend12!@","port":40086,"resourceName":"testHH","resourceType":"huawei","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749627967000,"updateUserId":1923195822141345792,"usedMemory":"91517.6"},{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749627549000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932704139536502784,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"test001","fbUsedUtil":0,"resourceInfoId":1932704139536502784},"password":"Ascend12!@","port":40086,"resourceName":"test001","resourceType":"huawei","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749627549000,"updateUserId":1923195822141345792,"usedMemory":"90642.6"},{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749626758000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932700819912658944,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"test","fbUsedUtil":0,"resourceInfoId":1932700819912658944},"password":"Ascend12!@","port":40086,"resourceName":"test","resourceType":"","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749626758000,"updateUserId":1923195822141345792,"usedMemory":"91420.2"},{"availableMemory":"682410.3","computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749540650000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"diskCapacity":"5036419","gpuCount":"8","graphicsMemory":"262144.0","hostIp":"*************","id":1932339656674775040,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"test333","fbUsedUtil":0,"resourceInfoId":1932339656674775040},"password":"Ascend12!@","port":40086,"resourceName":"test333","resourceType":"","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749540650000,"updateUserId":1923195822141345792,"usedMemory":"90810.2"},{"availableMemory":"683186.7","computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749539905000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"diskCapacity":"5036419","gpuCount":"8","graphicsMemory":"262144.0","hostIp":"*************","id":1932336532371279872,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"test6","fbUsedUtil":0,"resourceInfoId":1932336532371279872},"password":"Ascend12!@","port":40086,"resourceName":"test6","resourceType":"huawei","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749539905000,"updateUserId":1923195822141345792,"usedMemory":"90035.2"},{"availableMemory":"683286.7","computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749538808000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"diskCapacity":"5036419","gpuCount":"8","graphicsMemory":"262144.0","hostIp":"*************","id":1932331930129272832,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"test","fbUsedUtil":0,"resourceInfoId":1932331930129272832},"password":"Ascend12!@","port":40086,"resourceName":"test","resourceType":"huawei","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749538808000,"updateUserId":1923195822141345792,"usedMemory":"89933.8"}]},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[DEBUG] [2025-06-20 00:25:05] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:05] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? ORDER BY ts DESC LiMIT 1;
[DEBUG] [2025-06-20 00:25:05] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932722934955118592(Long)
[DEBUG] [2025-06-20 00:25:05] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:05] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? AND ts = ? ORDER BY ts DESC
[DEBUG] [2025-06-20 00:25:05] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932722934955118592(Long), 2025-06-16 11:46:00.171(Timestamp)
[DEBUG] [2025-06-20 00:25:05] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:05] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? ORDER BY ts DESC LiMIT 1;
[DEBUG] [2025-06-20 00:25:05] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932725193193558016(Long)
[DEBUG] [2025-06-20 00:25:05] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:05] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? AND ts = ? ORDER BY ts DESC
[DEBUG] [2025-06-20 00:25:05] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932725193193558016(Long), 2025-06-16 11:46:00.171(Timestamp)
[DEBUG] [2025-06-20 00:25:06] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:06] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? ORDER BY ts DESC LiMIT 1;
[DEBUG] [2025-06-20 00:25:06] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932729802632990720(Long)
[DEBUG] [2025-06-20 00:25:06] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:06] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? AND ts = ? ORDER BY ts DESC
[DEBUG] [2025-06-20 00:25:06] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932729802632990720(Long), 2025-06-16 11:43:00.258(Timestamp)
[DEBUG] [2025-06-20 00:25:06] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:06] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? ORDER BY ts DESC LiMIT 1;
[DEBUG] [2025-06-20 00:25:06] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932705890410958848(Long)
[DEBUG] [2025-06-20 00:25:06] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:06] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? AND ts = ? ORDER BY ts DESC
[DEBUG] [2025-06-20 00:25:06] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932705890410958848(Long), 2025-06-16 11:46:00.171(Timestamp)
[DEBUG] [2025-06-20 00:25:06] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:06] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? ORDER BY ts DESC LiMIT 1;
[DEBUG] [2025-06-20 00:25:06] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932336532371279872(Long)
[DEBUG] [2025-06-20 00:25:06] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:06] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? AND ts = ? ORDER BY ts DESC
[DEBUG] [2025-06-20 00:25:06] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932336532371279872(Long), 2025-06-16 11:46:00.171(Timestamp)
[DEBUG] [2025-06-20 00:25:06] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[INFO ] [2025-06-20 00:25:06] T:[0fa67fbad7f04895b57942f11e394f3b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> 请求完成, url=/admin/app/schTaskInfo/taskOccupyResource，elapse=2280ms, respData={"data":[{"approveState":"approved","containerName":"1935177092941484032-remotetask-1","containerStatus":"running","createTime":1750217147000,"dictId":1933758313216872448,"exitCode":"running","graphicNeededMb":0,"id":1935177092941484032,"isDelete":1,"memoryNeededMb":2048,"poolId":1932706048758517760,"resourceId":1932730175653416960,"runCommand":"\"python train.py --task_type binary_classification --data_path /workspace/output/titanic_train.json --label_column survived --feature_columns age siblings_spouses parents_children fare family_size is_alone passenger_class gender embarked_port title age_band fare_band --categorical_columns passenger_class gender embarked_port title age_band fare_band --num_boost_round 500 --early_stopping_rounds 30 --learning_rate 0.1 --verbose_eval 25\"","schNodeBasicMetrics":{"availableMemory":671418.02734375,"cpuUtilization":0.000000,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"diskIoWrittenAndReadSum":2603898.2099609375,"diskUsedSum":1.0876231281168451E9,"diskUtilization":9.573692095050411,"id":1934456356069535745,"isDelete":1,"memoryUtilization":13.166035,"networkSpeedSum":1.25E9,"nowThread":8.0,"overallCpuUsage":3.235465,"processMemoryAmount":28.31640625,"processMemoryUsage":2.3343283774340744,"resourceId":1932730175653416960,"systemMemoryUtilization":0.0,"ts":1750045280198,"videoUtilization":0.000000},"status":"running","taskImageId":1934868264496664576,"taskName":"local_test_100","updateTime":1750217154000},{"approveState":"approved","containerName":"1935177238563524608-remotetask-1","containerStatus":"running","createTime":1750217182000,"dictId":1933758313216872448,"exitCode":"running","graphicNeededMb":0,"id":1935177238563524608,"isDelete":1,"memoryNeededMb":2048,"poolId":0,"resourceId":1932331930129272832,"runCommand":"\"python train.py --task_type binary_classification --data_path /workspace/output/titanic_train.json --label_column survived --feature_columns age siblings_spouses parents_children fare family_size is_alone passenger_class gender embarked_port title age_band fare_band --categorical_columns passenger_class gender embarked_port title age_band fare_band --num_boost_round 500 --early_stopping_rounds 30 --learning_rate 0.1 --verbose_eval 25\"","schNodeBasicMetrics":{"availableMemory":672779.90625,"cpuUtilization":0.000000,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"diskIoWrittenAndReadSum":2604480.6279296875,"diskUsedSum":1.0877001305238562E9,"diskUtilization":9.57404981912865,"id":1934457423700258818,"isDelete":1,"memoryUtilization":12.989904,"networkSpeedSum":1.25E9,"nowThread":8.0,"overallCpuUsage":3.235553,"processMemoryAmount":26.05859375,"processMemoryUsage":2.1482003870689352,"resourceId":1932331930129272832,"systemMemoryUtilization":0.0,"ts":1750045560171,"videoUtilization":0.000000},"status":"running","taskImageId":1934868264496664576,"taskName":"local_test_100","updateTime":1750217188000},{"approveState":"approved","containerName":"1935177248243978240-remotetask-1","containerStatus":"running","createTime":1750217184000,"dictId":1933758313216872448,"exitCode":"running","graphicNeededMb":0,"id":1935177248243978240,"isDelete":1,"memoryNeededMb":2048,"poolId":1930925656795779072,"resourceId":1932705890410958848,"runCommand":"\"python train.py --task_type binary_classification --data_path /workspace/output/titanic_train.json --label_column survived --feature_columns age siblings_spouses parents_children fare family_size is_alone passenger_class gender embarked_port title age_band fare_band --categorical_columns passenger_class gender embarked_port title age_band fare_band --num_boost_round 500 --early_stopping_rounds 30 --learning_rate 0.1 --verbose_eval 25\"","schNodeBasicMetrics":{"availableMemory":672825.5234375,"cpuUtilization":0.000000,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"diskIoWrittenAndReadSum":2604525.2685546875,"diskUsedSum":1.0877169133554041E9,"diskUtilization":9.574127785820586,"id":1934457447154806786,"isDelete":1,"memoryUtilization":12.984004,"networkSpeedSum":1.25E9,"nowThread":8.0,"overallCpuUsage":3.235549,"processMemoryAmount":26.0,"processMemoryUsage":2.1433700759002896,"resourceId":1932705890410958848,"systemMemoryUtilization":0.0,"ts":1750045560171,"videoUtilization":0.000000},"status":"running","taskImageId":1934868264496664576,"taskName":"local_test_100","updateTime":1750217190000},{"approveState":"approved","containerName":"1935177257802797056-remotetask-1","containerStatus":"running","createTime":1750217187000,"dictId":1933758313216872448,"exitCode":"running","graphicNeededMb":0,"id":1935177257802797056,"isDelete":1,"memoryNeededMb":2048,"poolId":1932711144816906240,"resourceId":1932705890410958848,"runCommand":"\"python train.py --task_type binary_classification --data_path /workspace/output/titanic_train.json --label_column survived --feature_columns age siblings_spouses parents_children fare family_size is_alone passenger_class gender embarked_port title age_band fare_band --categorical_columns passenger_class gender embarked_port title age_band fare_band --num_boost_round 500 --early_stopping_rounds 30 --learning_rate 0.1 --verbose_eval 25\"","schNodeBasicMetrics":{"availableMemory":672825.5234375,"cpuUtilization":0.000000,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"diskIoWrittenAndReadSum":2604525.2685546875,"diskUsedSum":1.0877169133554041E9,"diskUtilization":9.574127785820586,"id":1934457447154806786,"isDelete":1,"memoryUtilization":12.984004,"networkSpeedSum":1.25E9,"nowThread":8.0,"overallCpuUsage":3.235549,"processMemoryAmount":26.0,"processMemoryUsage":2.1433700759002896,"resourceId":1932705890410958848,"systemMemoryUtilization":0.0,"ts":1750045560171,"videoUtilization":0.000000},"status":"running","taskImageId":1934868264496664576,"taskName":"local_test_100","updateTime":1750217192000},{"approveState":"approved","containerName":"1935177267361615872-remotetask-1","containerStatus":"running","createTime":1750217189000,"dictId":1933758313216872448,"exitCode":"running","graphicNeededMb":0,"id":1935177267361615872,"isDelete":1,"memoryNeededMb":2048,"poolId":1932706048758517760,"resourceId":1932729975991963648,"runCommand":"\"python train.py --task_type binary_classification --data_path /workspace/output/titanic_train.json --label_column survived --feature_columns age siblings_spouses parents_children fare family_size is_alone passenger_class gender embarked_port title age_band fare_band --categorical_columns passenger_class gender embarked_port title age_band fare_band --num_boost_round 500 --early_stopping_rounds 30 --learning_rate 0.1 --verbose_eval 25\"","schNodeBasicMetrics":{"availableMemory":671637.85546875,"cpuUtilization":0.000000,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"diskIoWrittenAndReadSum":2603822.9130859375,"diskUsedSum":1.0875690625121713E9,"diskUtilization":9.573440926686372,"id":1934456265380294658,"isDelete":1,"memoryUtilization":13.137604,"networkSpeedSum":1.25E9,"nowThread":8.0,"overallCpuUsage":3.235459,"processMemoryAmount":27.5625,"processMemoryUsage":2.272178373730836,"resourceId":1932729975991963648,"systemMemoryUtilization":0.0,"ts":1750045280198,"videoUtilization":0.000000},"status":"running","taskImageId":1934868264496664576,"taskName":"local_test_100","updateTime":1750217195000},{"approveState":"approved","containerName":"1935177277050458112-remotetask-1","containerStatus":"running","createTime":1750217191000,"dictId":1933758313216872448,"exitCode":"running","graphicNeededMb":0,"id":1935177277050458112,"isDelete":1,"memoryNeededMb":2048,"poolId":1932706048758517760,"resourceId":1932729975991963648,"runCommand":"\"python train.py --task_type binary_classification --data_path /workspace/output/titanic_train.json --label_column survived --feature_columns age siblings_spouses parents_children fare family_size is_alone passenger_class gender embarked_port title age_band fare_band --categorical_columns passenger_class gender embarked_port title age_band fare_band --num_boost_round 500 --early_stopping_rounds 30 --learning_rate 0.1 --verbose_eval 25\"","schNodeBasicMetrics":{"availableMemory":671637.85546875,"cpuUtilization":0.000000,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"diskIoWrittenAndReadSum":2603822.9130859375,"diskUsedSum":1.0875690625121713E9,"diskUtilization":9.573440926686372,"id":1934456265380294658,"isDelete":1,"memoryUtilization":13.137604,"networkSpeedSum":1.25E9,"nowThread":8.0,"overallCpuUsage":3.235459,"processMemoryAmount":27.5625,"processMemoryUsage":2.272178373730836,"resourceId":1932729975991963648,"systemMemoryUtilization":0.0,"ts":1750045280198,"videoUtilization":0.000000},"status":"running","taskImageId":1934868264496664576,"taskName":"local_test_100","updateTime":1750217197000},{"approveState":"approved","containerName":"1935177286965792768-remotetask-1","containerStatus":"running","createTime":1750217194000,"dictId":1933758313216872448,"exitCode":"running","graphicNeededMb":0,"id":1935177286965792768,"isDelete":1,"memoryNeededMb":2048,"poolId":1932706048758517760,"resourceId":1932709585647308800,"runCommand":"\"python train.py --task_type binary_classification --data_path /workspace/output/titanic_train.json --label_column survived --feature_columns age siblings_spouses parents_children fare family_size is_alone passenger_class gender embarked_port title age_band fare_band --categorical_columns passenger_class gender embarked_port title age_band fare_band --num_boost_round 500 --early_stopping_rounds 30 --learning_rate 0.1 --verbose_eval 25\"","schNodeBasicMetrics":{"availableMemory":672824.74609375,"cpuUtilization":0.000000,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"diskIoWrittenAndReadSum":2604525.3779296875,"diskUsedSum":1.0877175754157739E9,"diskUtilization":9.574130861502827,"id":1934457449985961986,"isDelete":1,"memoryUtilization":12.984105,"networkSpeedSum":1.25E9,"nowThread":8.0,"overallCpuUsage":3.235552,"processMemoryAmount":27.45703125,"processMemoryUsage":2.263483813627274,"resourceId":1932709585647308800,"systemMemoryUtilization":0.0,"ts":1750045560171,"videoUtilization":0.000000},"status":"running","taskImageId":1934868264496664576,"taskName":"local_test_100","updateTime":1750217200000},{"approveState":"approved","containerName":"1935177296956624896-remotetask-1","containerStatus":"running","createTime":1750217196000,"dictId":1933758313216872448,"exitCode":"running","graphicNeededMb":0,"id":1935177296956624896,"isDelete":1,"memoryNeededMb":2048,"poolId":1932706048758517760,"resourceId":1932729975991963648,"runCommand":"\"python train.py --task_type binary_classification --data_path /workspace/output/titanic_train.json --label_column survived --feature_columns age siblings_spouses parents_children fare family_size is_alone passenger_class gender embarked_port title age_band fare_band --categorical_columns passenger_class gender embarked_port title age_band fare_band --num_boost_round 500 --early_stopping_rounds 30 --learning_rate 0.1 --verbose_eval 25\"","schNodeBasicMetrics":{"availableMemory":671637.85546875,"cpuUtilization":0.000000,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"diskIoWrittenAndReadSum":2603822.9130859375,"diskUsedSum":1.0875690625121713E9,"diskUtilization":9.573440926686372,"id":1934456265380294658,"isDelete":1,"memoryUtilization":13.137604,"networkSpeedSum":1.25E9,"nowThread":8.0,"overallCpuUsage":3.235459,"processMemoryAmount":27.5625,"processMemoryUsage":2.272178373730836,"resourceId":1932729975991963648,"systemMemoryUtilization":0.0,"ts":1750045280198,"videoUtilization":0.000000},"status":"running","taskImageId":1934868264496664576,"taskName":"local_test_100","updateTime":1750217202000},{"approveState":"approved","containerName":"1935177307492716544-remotetask-1","containerStatus":"running","createTime":1750217199000,"dictId":1933758313216872448,"exitCode":"running","graphicNeededMb":0,"id":1935177307492716544,"isDelete":1,"memoryNeededMb":2048,"poolId":1932706048758517760,"resourceId":1932729802632990720,"runCommand":"\"python train.py --task_type binary_classification --data_path /workspace/output/titanic_train.json --label_column survived --feature_columns age siblings_spouses parents_children fare family_size is_alone passenger_class gender embarked_port title age_band fare_band --categorical_columns passenger_class gender embarked_port title age_band fare_band --num_boost_round 500 --early_stopping_rounds 30 --learning_rate 0.1 --verbose_eval 25\"","schNodeBasicMetrics":{"availableMemory":671322.35546875,"cpuUtilization":0.000000,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"diskIoWrittenAndReadSum":2604265.7880859375,"diskUsedSum":1.0877264095345666E9,"diskUtilization":9.574171901479689,"id":1934456916063645698,"isDelete":1,"memoryUtilization":13.178408,"networkSpeedSum":1.25E9,"nowThread":8.0,"overallCpuUsage":3.235570,"processMemoryAmount":28.859375,"processMemoryUsage":2.379089260930189,"resourceId":1932729802632990720,"systemMemoryUtilization":0.0,"ts":1750045380258,"videoUtilization":0.000000},"status":"running","taskImageId":1934868264496664576,"taskName":"local_test_100","updateTime":1750217207000},{"approveState":"approved","containerName":"1935177317588406272-remotetask-1","containerStatus":"running","createTime":1750217201000,"dictId":1933758313216872448,"exitCode":"running","graphicNeededMb":0,"id":1935177317588406272,"isDelete":1,"memoryNeededMb":2048,"poolId":1928361548711989248,"resourceId":1932704139536502784,"runCommand":"\"python train.py --task_type binary_classification --data_path /workspace/output/titanic_train.json --label_column survived --feature_columns age siblings_spouses parents_children fare family_size is_alone passenger_class gender embarked_port title age_band fare_band --categorical_columns passenger_class gender embarked_port title age_band fare_band --num_boost_round 500 --early_stopping_rounds 30 --learning_rate 0.1 --verbose_eval 25\"","schNodeBasicMetrics":{"availableMemory":672855.69140625,"cpuUtilization":0.000000,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"diskIoWrittenAndReadSum":2604524.7490234375,"diskUsedSum":1.0877113456669912E9,"diskUtilization":9.574101920444654,"id":1934457443006640129,"isDelete":1,"memoryUtilization":12.980103,"networkSpeedSum":1.25E9,"nowThread":8.0,"overallCpuUsage":3.235550,"processMemoryAmount":26.36328125,"processMemoryUsage":2.1733180051458914,"resourceId":1932704139536502784,"systemMemoryUtilization":0.0,"ts":1750045560171,"videoUtilization":0.000000},"status":"running","taskImageId":1934868264496664576,"taskName":"local_test_100","updateTime":1750217211000},{"approveState":"approved","containerName":"1935177329294708736-remotetask-1","containerStatus":"running","createTime":1750217204000,"dictId":1933758313216872448,"exitCode":"running","graphicNeededMb":0,"id":1935177329294708736,"isDelete":1,"memoryNeededMb":2048,"poolId":1932706048758517760,"resourceId":1933063901780381696,"runCommand":"\"python train.py --task_type binary_classification --data_path /workspace/output/titanic_train.json --label_column survived --feature_columns age siblings_spouses parents_children fare family_size is_alone passenger_class gender embarked_port title age_band fare_band --categorical_columns passenger_class gender embarked_port title age_band fare_band --num_boost_round 500 --early_stopping_rounds 30 --learning_rate 0.1 --verbose_eval 25\"","schNodeBasicMetrics":{"availableMemory":671309.13671875,"cpuUtilization":0.000000,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"diskIoWrittenAndReadSum":2603765.9873046875,"diskUsedSum":1.0875825111119766E9,"diskUtilization":9.57350340379788,"id":1934456164381454337,"isDelete":1,"memoryUtilization":13.180117,"networkSpeedSum":1.25E9,"nowThread":8.0,"overallCpuUsage":3.235454,"processMemoryAmount":26.3046875,"processMemoryUsage":2.168487693977246,"resourceId":1933063901780381696,"systemMemoryUtilization":0.0,"ts":1750045260273,"videoUtilization":0.000000},"status":"running","taskImageId":1934868264496664576,"taskName":"local_test_100","updateTime":1750217212000},{"approveState":"approved","computeDeviceId":1932705891413397506,"containerName":"1935320370617782272-remotetask-1","containerStatus":"running","createTime":1750251308000,"dictId":1933758313216872448,"graphicNeededMb":169,"id":1935320370617782272,"isDelete":1,"memoryNeededMb":167,"partitionId":203,"poolId":1932711144816906240,"resourceId":1932705890410958848,"schCardMonitor":{"cardUtilization":0.00,"computeDeviceId":1932705891413397506,"createUserId":1923195822141345792,"hbTotal":32768,"hbUsed":0,"hbUtil":"0.0","id":1934457443480244230,"isDelete":1,"memoryTotal":15038,"memoryUsed":2341,"monitorType":1,"resourceId":1932705890410958848,"serialNumber":6,"temp":40,"ts":1750045560171,"updateUserId":1923195822141345792},"schNodeBasicMetrics":{"availableMemory":672825.5234375,"cpuUtilization":0.000000,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"diskIoWrittenAndReadSum":2604525.2685546875,"diskUsedSum":1.0877169133554041E9,"diskUtilization":9.574127785820586,"id":1934457447154806786,"isDelete":1,"memoryUtilization":12.984004,"networkSpeedSum":1.25E9,"nowThread":8.0,"overallCpuUsage":3.235549,"processMemoryAmount":26.0,"processMemoryUsage":2.1433700759002896,"resourceId":1932705890410958848,"systemMemoryUtilization":0.0,"ts":1750045560171,"videoUtilization":0.000000},"status":"running","taskImageId":1933866164367134720,"taskName":"jupyter-test-2","updateTime":1750251337000},{"approveState":"approved","containerName":"1935323446900690944-remotetask-1","containerStatus":"running","createTime":1750252041000,"dictId":1933758313216872448,"graphicNeededMb":169,"id":1935323446900690944,"isDelete":1,"memoryNeededMb":167,"poolId":0,"resourceId":1933097753617895424,"schNodeBasicMetrics":{"availableMemory":678471.53515625,"cpuUtilization":0.000000,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"diskIoWrittenAndReadSum":2420743.8466796875,"diskUsedSum":1.0611045592366115E9,"diskUtilization":9.450496847302304,"id":1933829276750065666,"isDelete":1,"memoryUtilization":12.253810,"networkSpeedSum":1.25E9,"nowThread":8.0,"overallCpuUsage":3.238029,"processMemoryAmount":27.75390625,"processMemoryUsage":2.2879573902150776,"resourceId":1933097753617895424,"systemMemoryUtilization":0.0,"ts":1749895800164,"videoUtilization":0.000000},"status":"running","taskImageId":1933866164367134720,"taskName":"jupyter-test-server","updateTime":1750252045000},{"approveState":"approved","containerName":"1935688491694297088-remotetask-1","containerStatus":"running","createTime":1750339074000,"dictId":1933758313216872448,"graphicNeededMb":0,"id":1935688491694297088,"isDelete":1,"memoryNeededMb":1024,"poolId":1932711144816906240,"resourceId":1932725193193558016,"schNodeBasicMetrics":{"availableMemory":672880.375,"cpuUtilization":0.000000,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"diskIoWrittenAndReadSum":2604528.7412109375,"diskUsedSum":1.08772610642259E9,"diskUtilization":9.574170493336009,"id":1934457460949872642,"isDelete":1,"memoryUtilization":12.976910,"networkSpeedSum":1.25E9,"nowThread":8.0,"overallCpuUsage":3.235550,"processMemoryAmount":26.8515625,"processMemoryUsage":2.2135705982179372,"resourceId":1932725193193558016,"systemMemoryUtilization":0.0,"ts":1750045560171,"videoUtilization":0.000000},"status":"running","taskImageId":1933866164367134720,"taskName":"nggxin-prxoy","updateTime":1750339170000},{"approveState":"approved","containerName":"1935691387219480576-remotetask-1","containerStatus":"running","createTime":1750339765000,"dictId":1933758313216872448,"graphicNeededMb":0,"id":1935691387219480576,"isDelete":1,"memoryNeededMb":1024,"poolId":1930925656795779072,"resourceId":1932722934955118592,"schNodeBasicMetrics":{"availableMemory":672842.546875,"cpuUtilization":0.000000,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"diskIoWrittenAndReadSum":2604528.5849609375,"diskUsedSum":1.087718476775073E9,"diskUtilization":9.57413504887744,"id":1934457455119790081,"isDelete":1,"memoryUtilization":12.981803,"networkSpeedSum":1.25E9,"nowThread":8.0,"overallCpuUsage":3.235551,"processMemoryAmount":28.0390625,"processMemoryUsage":2.3114649045691524,"resourceId":1932722934955118592,"systemMemoryUtilization":0.0,"ts":1750045560171,"videoUtilization":0.000000},"status":"running","taskImageId":1933866164367134720,"taskName":"nggxin-prxoy","updateTime":1750339769000},{"approveState":"approved","containerName":"1935692207218495488-remotetask-1","containerStatus":"running","createTime":1750339960000,"dictId":1933758313216872448,"graphicNeededMb":0,"id":1935692207218495488,"isDelete":1,"memoryNeededMb":1024,"poolId":1932711144816906240,"resourceId":1932725193193558016,"schNodeBasicMetrics":{"availableMemory":672880.375,"cpuUtilization":0.000000,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"diskIoWrittenAndReadSum":2604528.7412109375,"diskUsedSum":1.08772610642259E9,"diskUtilization":9.574170493336009,"id":1934457460949872642,"isDelete":1,"memoryUtilization":12.976910,"networkSpeedSum":1.25E9,"nowThread":8.0,"overallCpuUsage":3.235550,"processMemoryAmount":26.8515625,"processMemoryUsage":2.2135705982179372,"resourceId":1932725193193558016,"systemMemoryUtilization":0.0,"ts":1750045560171,"videoUtilization":0.000000},"status":"running","taskImageId":1933866164367134720,"taskName":"nggxin-prxoy","updateTime":1750339994000},{"approveState":"approved","containerName":"1935702064554315776-remotetask-1","containerStatus":"running","createTime":1750342310000,"dictId":1933758313216872448,"graphicNeededMb":0,"id":1935702064554315776,"isDelete":1,"memoryNeededMb":1024,"poolId":1932706048758517760,"resourceId":1932729802632990720,"schNodeBasicMetrics":{"availableMemory":671322.35546875,"cpuUtilization":0.000000,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"diskIoWrittenAndReadSum":2604265.7880859375,"diskUsedSum":1.0877264095345666E9,"diskUtilization":9.574171901479689,"id":1934456916063645698,"isDelete":1,"memoryUtilization":13.178408,"networkSpeedSum":1.25E9,"nowThread":8.0,"overallCpuUsage":3.235570,"processMemoryAmount":28.859375,"processMemoryUsage":2.379089260930189,"resourceId":1932729802632990720,"systemMemoryUtilization":0.0,"ts":1750045380258,"videoUtilization":0.000000},"status":"running","taskImageId":1933866164367134720,"taskName":"nggxin-prxoy_test","updateTime":1750342314000},{"approveState":"approved","containerName":"1935710901441138688-remotetask-1","containerStatus":"running","createTime":1750344417000,"dictId":1933758313216872448,"graphicNeededMb":169,"id":1935710901441138688,"isDelete":1,"memoryNeededMb":149,"poolId":1930925656795779072,"resourceId":1932705890410958848,"schNodeBasicMetrics":{"availableMemory":672825.5234375,"cpuUtilization":0.000000,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"diskIoWrittenAndReadSum":2604525.2685546875,"diskUsedSum":1.0877169133554041E9,"diskUtilization":9.574127785820586,"id":1934457447154806786,"isDelete":1,"memoryUtilization":12.984004,"networkSpeedSum":1.25E9,"nowThread":8.0,"overallCpuUsage":3.235549,"processMemoryAmount":26.0,"processMemoryUsage":2.1433700759002896,"resourceId":1932705890410958848,"systemMemoryUtilization":0.0,"ts":1750045560171,"videoUtilization":0.000000},"status":"running","taskImageId":1933866164367134720,"taskName":"jupyter","updateTime":1750344421000},{"approveState":"approved","containerName":"1935711602212868096-remotetask-1","containerStatus":"running","createTime":1750344584000,"dictId":1933758313216872448,"graphicNeededMb":169,"id":1935711602212868096,"isDelete":1,"memoryNeededMb":149,"poolId":0,"resourceId":1932336532371279872,"runCommand":"\"start-notebook.py --NotebookApp.token='b17f24ff026d40949c85a24f4f375d42' \"","schNodeBasicMetrics":{"availableMemory":672798.109375,"cpuUtilization":0.000000,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"diskIoWrittenAndReadSum":2604480.7021484375,"diskUsedSum":1.087700776630964E9,"diskUtilization":9.574052820698064,"id":1934457425940017154,"isDelete":1,"memoryUtilization":12.987550,"networkSpeedSum":1.25E9,"nowThread":8.0,"overallCpuUsage":3.235552,"processMemoryAmount":26.31640625,"processMemoryUsage":2.169453756210975,"resourceId":1932336532371279872,"systemMemoryUtilization":0.0,"ts":1750045560171,"videoUtilization":0.000000},"status":"running","taskImageId":1935703876279734272,"taskName":"jupyter","updateTime":1750344588000}],"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":[{"$ref":"$.data[0]"},{"$ref":"$.data[1]"},{"$ref":"$.data[2]"},{"$ref":"$.data[3]"},{"$ref":"$.data[4]"},{"$ref":"$.data[5]"},{"$ref":"$.data[6]"},{"$ref":"$.data[7]"},{"$ref":"$.data[8]"},{"$ref":"$.data[9]"},{"$ref":"$.data[10]"},{"$ref":"$.data[11]"},{"$ref":"$.data[12]"},{"$ref":"$.data[13]"},{"$ref":"$.data[14]"},{"$ref":"$.data[15]"},{"$ref":"$.data[16]"},{"$ref":"$.data[17]"},{"$ref":"$.data[18]"}],"success":true}
[INFO ] [2025-06-20 00:25:08] T:[0d5d17bdf0f649f9b472e79aac3f86a4] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> 开始请求，url=/admin/app/schTaskTemplate/list, reqData={"schTaskTemplateDtoFilter":{},"orderParam":[{"asc":false,"dateAggregateBy":"","fieldName":"createTime"}],"pageParam":{"count":true,"pageNum":1,"pageSize":10}}
[INFO ] [2025-06-20 00:25:08] T:[c20b1173a7104012aad1667feea71b62] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> 开始请求，url=/admin/app/schTaskTemplate/templateCount, reqData={}
[DEBUG] [2025-06-20 00:25:08] T:[c20b1173a7104012aad1667feea71b62] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==>  Preparing: SELECT (SELECT COUNT(id) FROM sch_task_template WHERE is_delete = 1) AS total, (SELECT COUNT(tt.image_id) FROM sch_task_template tt JOIN sch_task_image ti ON tt.image_id = ti.id WHERE tt.is_delete = 1 AND ti.is_delete = 1) AS relationImage, (SELECT COUNT(*) FROM sch_task_template WHERE is_delete = 1 AND update_time >= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)) AS relateUpdate
[DEBUG] [2025-06-20 00:25:08] T:[c20b1173a7104012aad1667feea71b62] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==> Parameters: 
[DEBUG] [2025-06-20 00:25:08] T:[0d5d17bdf0f649f9b472e79aac3f86a4] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> ==>  Preparing: SELECT count(0) FROM sch_task_template WHERE sch_task_template.is_delete = 1
[DEBUG] [2025-06-20 00:25:08] T:[0d5d17bdf0f649f9b472e79aac3f86a4] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> ==> Parameters: 
[DEBUG] [2025-06-20 00:25:08] T:[c20b1173a7104012aad1667feea71b62] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:08] T:[0d5d17bdf0f649f9b472e79aac3f86a4] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:08] T:[0d5d17bdf0f649f9b472e79aac3f86a4] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> ==>  Preparing: SELECT * FROM sch_task_template WHERE sch_task_template.is_delete = 1 ORDER BY sch_task_template.create_time DESC LIMIT ?
[DEBUG] [2025-06-20 00:25:08] T:[0d5d17bdf0f649f9b472e79aac3f86a4] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> ==> Parameters: 10(Integer)
[INFO ] [2025-06-20 00:25:08] T:[c20b1173a7104012aad1667feea71b62] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> 请求完成, url=/admin/app/schTaskTemplate/templateCount，elapse=118ms, respData={"data":{"relateUpdate":1,"relationImage":0,"total":8},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[DEBUG] [2025-06-20 00:25:08] T:[0d5d17bdf0f649f9b472e79aac3f86a4] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:09] T:[0d5d17bdf0f649f9b472e79aac3f86a4] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,image_name,image_desc,image_url,image_version FROM sch_task_image WHERE is_delete=1 AND (id IN (?,?,?))
[DEBUG] [2025-06-20 00:25:09] T:[0d5d17bdf0f649f9b472e79aac3f86a4] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> ==> Parameters: 1927994908065402880(Long), 1930147594567159808(Long), 1925802254888079360(Long)
[DEBUG] [2025-06-20 00:25:09] T:[0d5d17bdf0f649f9b472e79aac3f86a4] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> <==      Total: 0
[INFO ] [2025-06-20 00:25:09] T:[0d5d17bdf0f649f9b472e79aac3f86a4] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> 请求完成, url=/admin/app/schTaskTemplate/list，elapse=276ms, respData={"data":{"dataList":[{"createTime":1750325730000,"createUserId":1921773426524033024,"dataDeptId":1923195822141345795,"dataUserId":1921773426524033024,"envConfig":"[]","id":1935632519462391808,"imageId":1930147594567159808,"runCommand":"","templateDesc":"用于测试","templateName":"测试-副本","updateTime":1750325730000,"updateUserId":1921773426524033024},{"createTime":1749695447000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"envConfig":"[{\"value\":\"1\",\"key\":\"test\"},{\"value\":\"1\",\"key\":\"test\"}]","id":1932988922334285824,"imageId":1930147594567159808,"runCommand":"","templateDesc":"","templateName":"测试002","updateTime":1749695447000,"updateUserId":1923195822141345792},{"createTime":1749633373000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"envConfig":"[{\"value\":\"1\",\"key\":\"test\"}]","id":1932728567368519680,"imageId":1930147594567159808,"runCommand":"yum","templateDesc":"任务模板是算力调度平台中用于快速创建任务的标准化配置集合。它集成预设的资源需求（如显存、内存、CPU 核数等）、执行流程及镜像信息，支持一键复用。通过任务模板，可规范任务参数、简化创建流程，适配模型训练、数据处理等多样场景，助力用户高效调度算力资源，提升任务部署的一致性与便捷性 。","templateName":"测试001-副本","updateTime":1749633373000,"updateUserId":1923195822141345792},{"createTime":1749632944000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"envConfig":"[{\"value\":\"1\",\"key\":\"test\"}]","id":1932726765583601664,"imageId":1930147594567159808,"runCommand":"yum","templateDesc":"任务模板是算力调度平台中用于快速创建任务的标准化配置集合。它集成预设的资源需求（如显存、内存、CPU 核数等）、执行流程及镜像信息，支持一键复用。通过任务模板，可规范任务参数、简化创建流程，适配模型训练、数据处理等多样场景，助力用户高效调度算力资源，提升任务部署的一致性与便捷性 。","templateName":"测试001","updateTime":1749632944000,"updateUserId":1921773426524033024},{"createTime":1749628725000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"envConfig":"[]","id":1932709069190074368,"imageId":1930147594567159808,"runCommand":"","templateDesc":"用于测试","templateName":"测试","updateTime":1749628725000,"updateUserId":1923195822141345792},{"createTime":1749435475000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"envConfig":"[{\"value\":\"1\",\"key\":\"test\"}]","id":1931898521351360512,"imageId":1927994908065402880,"runCommand":"","templateDesc":"test","templateName":"test","updateTime":1749435475000,"updateUserId":1923195822141345792},{"createTime":1747984152000,"createUserId":1923195822141345792,"envConfig":"[{\"value\":\"d\",\"key\":\"gg\"}]","id":1925811230891577344,"imageId":1925802254888079360,"runCommand":"yh","templateDesc":"烦烦烦烦烦烦","templateName":"水水水水水水水水","updateTime":1747984152000,"updateUserId":0},{"createTime":1747984057000,"createUserId":1923195822141345792,"envConfig":"[{\"value\":\"s\",\"key\":\"fff\"}]","id":1925810831442841600,"imageId":1925802254888079360,"runCommand":"pip","templateDesc":"撒打发","templateName":"啊撒打发","updateTime":1747984057000,"updateUserId":0}],"totalCount":8},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[INFO ] [2025-06-20 00:25:14] T:[b7440185e86049f1990bffe551a4d498] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> 开始请求，url=/admin/app/schBusinessDict/list, reqData={"orderParam":[{"asc":false,"dateAggregateBy":"","fieldName":"createTime"}],"schBusinessDictDtoFilter":{"bindType":"TaskMonitor"},"pageParam":{"count":true,"pageNum":1,"pageSize":10}}
[INFO ] [2025-06-20 00:25:14] T:[a2169c3fa75d4499a8087deea01b7844] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-3] ==> 开始请求，url=/admin/app/schTaskMonitoring/taskCount, reqData={}
[DEBUG] [2025-06-20 00:25:14] T:[a2169c3fa75d4499a8087deea01b7844] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-3] ==> ==>  Preparing: SELECT COUNT(id) AS totalTask, SUM(IF(ti.approve_state = 'pending', 1, 0)) AS noCommit, SUM(IF(ti.status= 'pending',1,0)) AS pending, SUM(IF(ti.status = 'failed',1,0)) AS failed, SUM(IF(ti.status = 'starting', 1, 0)) AS start, SUM(IF(ti.status = 'queued', 1, 0)) AS queue, SUM(IF(ti.status = 'running', 1, 0)) AS run, SUM(IF(ti.status = 'finished', 1, 0)) AS finish, SUM(IF(ti.status = 'stop' AND ti.container_status IN ('exited', 'paused','exited(0)'), 1, 0)) AS stop, SUM(IF(ti.status = 'running' AND ti.container_status IN ('dead','exited','paused','failed','restarting'), 1, 0)) AS exception FROM sch_task_info ti WHERE ti.is_delete = 1
[DEBUG] [2025-06-20 00:25:14] T:[a2169c3fa75d4499a8087deea01b7844] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-3] ==> ==> Parameters: 
[DEBUG] [2025-06-20 00:25:14] T:[b7440185e86049f1990bffe551a4d498] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT count(0) FROM sch_business_dict WHERE sch_business_dict.bind_type LIKE ? AND sch_business_dict.is_delete = 1
[DEBUG] [2025-06-20 00:25:14] T:[b7440185e86049f1990bffe551a4d498] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: %TaskMonitor%(String)
[DEBUG] [2025-06-20 00:25:14] T:[a2169c3fa75d4499a8087deea01b7844] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-3] ==> <==      Total: 1
[INFO ] [2025-06-20 00:25:14] T:[a2169c3fa75d4499a8087deea01b7844] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-3] ==> 请求完成, url=/admin/app/schTaskMonitoring/taskCount，elapse=51ms, respData={"data":{"exception":0,"failed":17,"finish":2,"noCommit":0,"pending":0,"queue":0,"run":20,"start":0,"stop":2,"totalTask":41},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[DEBUG] [2025-06-20 00:25:14] T:[b7440185e86049f1990bffe551a4d498] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:14] T:[b7440185e86049f1990bffe551a4d498] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT * FROM sch_business_dict WHERE sch_business_dict.bind_type LIKE ? AND sch_business_dict.is_delete = 1 ORDER BY sch_business_dict.create_time DESC LIMIT ?
[DEBUG] [2025-06-20 00:25:14] T:[b7440185e86049f1990bffe551a4d498] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: %TaskMonitor%(String), 10(Integer)
[DEBUG] [2025-06-20 00:25:14] T:[b7440185e86049f1990bffe551a4d498] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 5
[INFO ] [2025-06-20 00:25:14] T:[b7440185e86049f1990bffe551a4d498] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> 请求完成, url=/admin/app/schBusinessDict/list，elapse=216ms, respData={"data":{"dataList":[{"bindType":"TaskMonitor","colorData":"","createTime":1749632889000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"dictDescription":"","dictLevel":0,"dictName":"测试任务HH","id":1932726533772808192,"showOrder":0,"strId":"88a0d36e3034136a6be1c29502290286","updateTime":1749632889000,"updateUserId":1923195822141345792},{"bindType":"TaskMonitor","colorData":"","createTime":1749632636000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"dictDescription":"","dictLevel":0,"dictName":"测试任务@","id":1932725472521621504,"showOrder":0,"strId":"c8dbbbbb821f052abeaa616b5a83e2fb","updateTime":1749632636000,"updateUserId":1923195822141345792},{"bindType":"TaskMonitor","colorData":"","createTime":1749460618000,"createUserId":1923195822141345792,"dictDescription":"","dictLevel":0,"dictName":"测试任务321","id":***********45006592,"showOrder":0,"strId":"d26b2ecf9311ea9ddb51717ef281be5b","updateTime":1749460618000,"updateUserId":1923195822141345792},{"bindType":"TaskMonitor","colorData":"","createTime":1749460600000,"createUserId":1923195822141345792,"dataDeptId":0,"dataUserId":0,"dictDescription":"","dictLevel":0,"dictName":"测试任务haha","id":1932003904598315008,"showOrder":0,"strId":"391223d895fd9155f61e0834da7228b5","updateTime":1749522525000,"updateUserId":1923195822141345792},{"bindType":"TaskMonitor","colorData":"","createTime":1749457922000,"createUserId":1923195822141345792,"dataDeptId":0,"dataUserId":0,"dictDescription":"","dictLevel":0,"dictName":"测试任务456-test","id":1931992669626568704,"showOrder":0,"strId":"86dfa84fcccd1f7484eb59369638e1e0","updateTime":1749632612000,"updateUserId":1923195822141345792}],"totalCount":5},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[INFO ] [2025-06-20 00:25:14] T:[5f6a8da3c9fd4611ad311cda87f9c23a] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> 开始请求，url=/admin/app/schTaskInfo/list, reqData={"orderParam":[{"asc":false,"dateAggregateBy":"","fieldName":"createTime"}],"schTaskInfoDtoFilter":{"dictId":1932726533772808192},"pageParam":{"count":true,"pageNum":1,"pageSize":10}}
[DEBUG] [2025-06-20 00:25:14] T:[5f6a8da3c9fd4611ad311cda87f9c23a] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT count(0) FROM sch_task_info WHERE sch_task_info.dict_id = ? AND sch_task_info.is_delete = 1
[DEBUG] [2025-06-20 00:25:14] T:[5f6a8da3c9fd4611ad311cda87f9c23a] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932726533772808192(Long)
[DEBUG] [2025-06-20 00:25:14] T:[5f6a8da3c9fd4611ad311cda87f9c23a] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:14] T:[5f6a8da3c9fd4611ad311cda87f9c23a] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_task_info WHERE sch_task_info.dict_id = ? AND sch_task_info.is_delete = 1 ORDER BY sch_task_info.create_time DESC LIMIT ?
[DEBUG] [2025-06-20 00:25:14] T:[5f6a8da3c9fd4611ad311cda87f9c23a] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932726533772808192(Long), 10(Integer)
[DEBUG] [2025-06-20 00:25:14] T:[5f6a8da3c9fd4611ad311cda87f9c23a] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 3
[DEBUG] [2025-06-20 00:25:14] T:[5f6a8da3c9fd4611ad311cda87f9c23a] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,image_name,image_desc,image_url,image_version FROM sch_task_image WHERE is_delete=1 AND (id IN (?,?))
[DEBUG] [2025-06-20 00:25:14] T:[5f6a8da3c9fd4611ad311cda87f9c23a] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1930147594567159808(Long), 1933920684832985088(Long)
[DEBUG] [2025-06-20 00:25:14] T:[5f6a8da3c9fd4611ad311cda87f9c23a] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:14] T:[5f6a8da3c9fd4611ad311cda87f9c23a] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,pool_name,pool_description FROM sch_resource_pool WHERE is_delete=1 AND (id IN (?,?))
[DEBUG] [2025-06-20 00:25:14] T:[5f6a8da3c9fd4611ad311cda87f9c23a] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932711144816906240(Long), 1932706048758517760(Long)
[DEBUG] [2025-06-20 00:25:15] T:[5f6a8da3c9fd4611ad311cda87f9c23a] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 2
[DEBUG] [2025-06-20 00:25:15] T:[5f6a8da3c9fd4611ad311cda87f9c23a] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE is_delete=1 AND (id IN (?,?,?))
[DEBUG] [2025-06-20 00:25:15] T:[5f6a8da3c9fd4611ad311cda87f9c23a] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932725193193558016(Long), 1932705890410958848(Long), 1932730175653416960(Long)
[DEBUG] [2025-06-20 00:25:15] T:[5f6a8da3c9fd4611ad311cda87f9c23a] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 3
[DEBUG] [2025-06-20 00:25:15] T:[5f6a8da3c9fd4611ad311cda87f9c23a] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (id IN (?,?,?))
[DEBUG] [2025-06-20 00:25:15] T:[5f6a8da3c9fd4611ad311cda87f9c23a] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932725194112110592(Long), 1932705891413397506(Long), 1932730176622301186(Long)
[DEBUG] [2025-06-20 00:25:15] T:[5f6a8da3c9fd4611ad311cda87f9c23a] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 3
[DEBUG] [2025-06-20 00:25:15] T:[5f6a8da3c9fd4611ad311cda87f9c23a] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT id,str_id,is_delete,create_user_id,create_time,update_user_id,update_time,data_user_id,data_dept_id,compute_device_id,template_id,vcc_id,vgroup_id FROM sch_virtual_compute_card_situation WHERE is_delete=1 AND (id IN (?,?,?))
[DEBUG] [2025-06-20 00:25:15] T:[5f6a8da3c9fd4611ad311cda87f9c23a] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 103(Long), 218(Long), 1932719052900274176(Long)
[DEBUG] [2025-06-20 00:25:15] T:[5f6a8da3c9fd4611ad311cda87f9c23a] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:15] T:[5f6a8da3c9fd4611ad311cda87f9c23a] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,task_id,status,decision_time FROM sch_task_approval WHERE is_delete=1 AND (task_id IN (?,?,?))
[DEBUG] [2025-06-20 00:25:15] T:[5f6a8da3c9fd4611ad311cda87f9c23a] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1933783331619475456(Long), 1935676707801403392(Long), 1935674439718932480(Long)
[DEBUG] [2025-06-20 00:25:15] T:[5f6a8da3c9fd4611ad311cda87f9c23a] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 0
[INFO ] [2025-06-20 00:25:15] T:[5f6a8da3c9fd4611ad311cda87f9c23a] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> 请求完成, url=/admin/app/schTaskInfo/list，elapse=363ms, respData={"data":{"dataList":[{"allowPreemption":1,"approveState":"approved","computeDeviceId":1932730176622301186,"cpuNeed":"2","createTime":1750336265000,"createUserId":1921773426524033024,"dataDeptId":1923195822141345795,"dataUserId":1921773426524033024,"dictId":1932726533772808192,"envConfig":"[{\"value\":\"1\",\"key\":\"test\"}]","graphicNeededMb":1,"id":1935676707801403392,"memoryNeededMb":1,"partitionId":218,"poolId":1932706048758517760,"releasePolicy":1,"resourceId":1932730175653416960,"runCommand":"yum","schComputeDevice":{"createTime":1749633757000,"createUserId":1923195822141345792,"dataDeptId":1787740798154969088,"deviceName":"910B-Ascend-V1","deviceNumber":7,"id":1932730176622301186,"isDelete":1,"memorySize":32768,"modelNumber":"910B-Ascend-V1","resourceId":1932730175653416960,"updateTime":1749633757000},"schResourceInfo":{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749633757000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932730175653416960,"isDelete":1,"loginName":"root","memoryCapacity":"773220.5","password":"Ascend12!@","port":40086,"resourceName":"test007","resourceType":"x86","status":"offline","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749633757000,"updateUserId":1923195822141345792,"usedMemory":"92160.0"},"schResourcePool":{"createTime":1749628005000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"id":1932706048758517760,"isDelete":1,"poolDescription":"资源池是算力调度平台的核心基础模块，汇聚多样算力资源（含CPU、内存、显存等），可动态分配、灵活调度。支持按任务需求快速切分、精准匹配，实时监控资源占用与状态，保障任务高效运行，为算力密集型业务（如模型训练、数据处理 ）提供稳定、弹性的资源供给，助力优化资源利用与业务流程。","poolName":"source-test","updateTime":1749628005000,"updateUserId":1923195822141345792},"schedulingPolicies":1,"status":"failed","taskImageId":1930147594567159808,"taskName":"001","taskPriority":0,"updateTime":1750336286000,"updateUserId":1921773426524033024},{"allowPreemption":1,"approveState":"approved","computeDeviceId":1932725194112110592,"cpuNeed":"1","createTime":1750335724000,"createUserId":1921773426524033024,"dataDeptId":1923195822141345795,"dataUserId":1921773426524033024,"dictId":1932726533772808192,"envConfig":"[]","graphicNeededMb":56,"id":1935674439718932480,"memoryNeededMb":345,"partitionId":103,"poolId":1932711144816906240,"releasePolicy":1,"resourceId":1932725193193558016,"runCommand":"yum install","schComputeDevice":{"createTime":1749632569000,"createUserId":1923195822141345792,"dataDeptId":1787740798154969088,"deviceName":"910B-Ascend-V1","deviceNumber":0,"id":1932725194112110592,"isDelete":1,"memorySize":32768,"modelNumber":"910B-Ascend-V1","resourceId":1932725193193558016,"updateTime":1749632569000},"schResourceInfo":{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749632569000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932725193193558016,"isDelete":1,"loginName":"root","memoryCapacity":"773220.5","password":"Ascend12!@","port":40086,"resourceName":"testHH2","resourceType":"huawei","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749632569000,"updateUserId":1923195822141345792,"usedMemory":"91710.6"},"schResourcePool":{"createTime":1749629220000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"id":1932711144816906240,"isDelete":1,"poolDescription":"wuhu","poolName":"testHH","updateTime":1749629220000,"updateUserId":1923195822141345792},"schedulingPolicies":1,"status":"failed","taskImage":{"createTime":1749917596000,"id":1933920684832985088,"imageName":"python-calculator:v1.0","isDelete":1,"updateTime":1749917596000},"taskImageId":1933920684832985088,"taskName":"任务监控","taskPriority":0,"updateTime":1750336213000,"updateUserId":1921773426524033024},{"allowPreemption":1,"approveState":"approved","computeDeviceId":1932705891413397506,"cpuNeed":"4","createTime":1749884849000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"dictId":1932726533772808192,"envConfig":"[{\"value\":\"1\",\"key\":\"test\"},{\"value\":\"1\",\"key\":\"test\"}]","failReason":"等待时间超过30分钟","graphicNeededMb":24,"id":1933783331619475456,"memoryNeededMb":24,"partition":{"computeDeviceId":1932705891413397506,"createTime":1749631105000,"createUserId":1923195822141345792,"id":1932719052900274176,"isDelete":1,"templateId":1928336030277046272,"updateTime":1749631105000,"updateUserId":1923195822141345792,"vccId":203},"partitionId":1932719052900274176,"poolId":1932711144816906240,"releasePolicy":1,"resourceId":1932705890410958848,"runCommand":"森岛帆高多发点三个地方会更大飞机上代发给","schComputeDevice":{"createTime":1749627967000,"createUserId":1923195822141345792,"dataDeptId":1787740798154969088,"deviceName":"910B-Ascend-V1","deviceNumber":6,"id":1932705891413397506,"isDelete":1,"memorySize":32768,"modelNumber":"910B-Ascend-V1","resourceId":1932705890410958848,"updateTime":1749627967000},"schResourceInfo":{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749627967000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932705890410958848,"isDelete":1,"loginName":"root","memoryCapacity":"773220.5","password":"Ascend12!@","port":40086,"resourceName":"testHH","resourceType":"huawei","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749627967000,"updateUserId":1923195822141345792,"usedMemory":"91517.6"},"schResourcePool":{"$ref":"$.data.dataList[1].schResourcePool"},"schedulingPolicies":1,"status":"failed","taskImageId":1930147594567159808,"taskName":"审核测试","taskPriority":0,"updateTime":1750326833000,"updateUserId":1923195822141345792}],"totalCount":3},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[INFO ] [2025-06-20 00:25:18] T:[e0292b17176a42f8814b8524c91e9302] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> 开始请求，url=/admin/app/schResourceInfo/resourceTotal, reqData={}
[DEBUG] [2025-06-20 00:25:18] T:[e0292b17176a42f8814b8524c91e9302] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> ==>  Preparing: SELECT COALESCE(COUNT(*), 0) AS 'resourceTotal', COALESCE(SUM( CASE WHEN cpu_core_count != 'Unknown' AND cpu_core_count REGEXP '^[0-9]+(\\.[0-9]+)?$' THEN CAST(cpu_core_count AS DECIMAL(20,2)) ELSE 0 END ), 0) AS 'cpuCoreTotal', COALESCE(SUM( CASE WHEN memory_capacity != 'Unknown' AND memory_capacity REGEXP '^[0-9]+(\\.[0-9]+)?$' THEN CAST(memory_capacity AS DECIMAL(20,2)) ELSE 0 END ), 0) AS 'memoryTotal', COALESCE(SUM( CASE WHEN graphics_memory REGEXP '^[0-9]+(\\.[0-9]+)?$' THEN CAST(graphics_memory AS DECIMAL(20,2)) ELSE 0 END ), 0) AS 'gpuMemoryTotal', COALESCE(SUM(CASE WHEN status = 'offline' THEN 1 ELSE 0 END), 0) AS 'offlineCount', COALESCE(SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END), 0) AS 'activeCount', COALESCE(SUM(CASE WHEN status = 'maintenance' THEN 1 ELSE 0 END), 0) AS 'maintenanceCount', COALESCE(SUM(CASE WHEN status = 'error' THEN 1 ELSE 0 END), 0) AS 'errorCount' FROM sch_resource_info WHERE is_delete = 1;
[DEBUG] [2025-06-20 00:25:18] T:[e0292b17176a42f8814b8524c91e9302] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> ==> Parameters: 
[INFO ] [2025-06-20 00:25:18] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> 开始请求，url=/admin/app/schResourceInfo/list, reqData={"orderParam":[{"asc":false,"dateAggregateBy":"","fieldName":"createTime"}],"schResourceInfoDtoFilter":{},"pageParam":{"count":true,"pageNum":1,"pageSize":10}}
[DEBUG] [2025-06-20 00:25:18] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==>  Preparing: SELECT count(0) FROM sch_resource_info WHERE sch_resource_info.is_delete = 1
[DEBUG] [2025-06-20 00:25:18] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==> Parameters: 
[DEBUG] [2025-06-20 00:25:18] T:[e0292b17176a42f8814b8524c91e9302] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:18] T:[e0292b17176a42f8814b8524c91e9302] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> ==>  Preparing: SELECT s.status, COALESCE(t.count, 0) AS count FROM ( SELECT 'running' AS status UNION ALL SELECT 'queued' UNION ALL SELECT 'starting' UNION ALL SELECT 'pending' UNION ALL SELECT 'stop' UNION ALL SELECT 'finished' UNION ALL SELECT 'failed' ) s LEFT JOIN ( SELECT status, COUNT(*) AS count FROM sch_task_info WHERE is_delete = 1 AND status IN ('pending', 'queued', 'starting', 'running', 'stop', 'finished', 'failed') GROUP BY status ) t ON s.status = t.status ORDER BY FIELD(s.status, 'running', 'queued', 'starting', 'pending', 'stop', 'finished', 'failed' )
[DEBUG] [2025-06-20 00:25:18] T:[e0292b17176a42f8814b8524c91e9302] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> ==> Parameters: 
[DEBUG] [2025-06-20 00:25:18] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:18] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==>  Preparing: SELECT * FROM sch_resource_info WHERE sch_resource_info.is_delete = 1 ORDER BY sch_resource_info.create_time DESC LIMIT ?
[DEBUG] [2025-06-20 00:25:18] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==> Parameters: 10(Integer)
[DEBUG] [2025-06-20 00:25:18] T:[e0292b17176a42f8814b8524c91e9302] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> <==      Total: 7
[DEBUG] [2025-06-20 00:25:18] T:[e0292b17176a42f8814b8524c91e9302] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> ==>  Preparing: SELECT t.id AS taskId, t.task_name AS taskName, t.status AS taskStatus, t.graphic_needed_mb AS graphicNeededMb, t.memory_needed_mb AS memoryNeededMb, t.cpu_need AS cpuNeed, c.id AS computeDeviceId, c.device_name AS deviceName, c.device_type AS deviceType, r.id AS resourceId, r.resource_name AS resourceName, r.host_ip AS hostIp FROM sch_resource_info r JOIN sch_compute_device c ON r.id = c.resource_id AND c.is_delete = 1 LEFT JOIN sch_task_info t ON c.id = t.compute_device_id AND t.is_delete = 1 WHERE r.is_delete = 1;
[DEBUG] [2025-06-20 00:25:18] T:[e0292b17176a42f8814b8524c91e9302] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> ==> Parameters: 
[DEBUG] [2025-06-20 00:25:18] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> <==      Total: 10
[DEBUG] [2025-06-20 00:25:18] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id IN ( ? , ? , ? , ? , ? , ? , ? , ? , ? , ? ) AND ts >= now() - INTERVAL 60 MINUTE; ORDER BY ts DESC, resource_id DESC
[DEBUG] [2025-06-20 00:25:18] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==> Parameters: 1933097753617895424(Long), 1933063901780381696(Long), 1933048812436197376(Long), 1932730175653416960(Long), 1932729975991963648(Long), 1932729802632990720(Long), 1932725193193558016(Long), 1932722934955118592(Long), 1932717669144858624(Long), 1932709585647308800(Long)
[DEBUG] [2025-06-20 00:25:18] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> <==      Total: 0
[DEBUG] [2025-06-20 00:25:18] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==>  Preparing: SELECT t.* FROM sch_card_monitor t JOIN ( SELECT resource_id, max(ts) AS latest_ts FROM sch_card_monitor WHERE resource_id IN ( ? , ? , ? , ? , ? , ? , ? , ? , ? , ? ) GROUP BY resource_id ) m ON t.resource_id = m.resource_id AND t.ts = m.latest_ts;
[DEBUG] [2025-06-20 00:25:18] T:[e0292b17176a42f8814b8524c91e9302] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> <==      Total: 132
[DEBUG] [2025-06-20 00:25:18] T:[e0292b17176a42f8814b8524c91e9302] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> ==>  Preparing: SELECT * FROM sch_compute_device WHERE resource_id IN (SELECT id FROM sch_resource_info WHERE is_delete = 1) AND is_delete = 1
[DEBUG] [2025-06-20 00:25:18] T:[e0292b17176a42f8814b8524c91e9302] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> ==> Parameters: 
[DEBUG] [2025-06-20 00:25:18] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==> Parameters: 1933097753617895424(Long), 1933063901780381696(Long), 1933048812436197376(Long), 1932730175653416960(Long), 1932729975991963648(Long), 1932729802632990720(Long), 1932725193193558016(Long), 1932722934955118592(Long), 1932717669144858624(Long), 1932709585647308800(Long)
[DEBUG] [2025-06-20 00:25:18] T:[e0292b17176a42f8814b8524c91e9302] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> <==      Total: 128
[INFO ] [2025-06-20 00:25:18] T:[e0292b17176a42f8814b8524c91e9302] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> 请求完成, url=/admin/app/schResourceInfo/resourceTotal，elapse=269ms, respData={"data":{"taskTotalCount":41,"usedComputeDevice":1,"resourceStatistic":{"resourceTotal":16,"activeCount":13,"offlineCount":1,"memoryTotal":12371528.00,"gpuMemoryTotal":4194304.00,"cpuCoreTotal":3072.00,"maintenanceCount":1,"errorCount":1},"freeComputeDevice":127,"totalComputeDevice":128},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[DEBUG] [2025-06-20 00:25:18] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> <==      Total: 80
[DEBUG] [2025-06-20 00:25:18] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:18] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==> Parameters: 1933097753617895424(Long)
[DEBUG] [2025-06-20 00:25:18] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:18] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:18] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==> Parameters: 1933063901780381696(Long)
[DEBUG] [2025-06-20 00:25:18] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:18] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:18] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==> Parameters: 1933048812436197376(Long)
[DEBUG] [2025-06-20 00:25:18] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:18] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:18] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==> Parameters: 1932730175653416960(Long)
[DEBUG] [2025-06-20 00:25:18] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:18] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:18] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==> Parameters: 1932729975991963648(Long)
[DEBUG] [2025-06-20 00:25:18] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:18] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:18] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==> Parameters: 1932729802632990720(Long)
[DEBUG] [2025-06-20 00:25:18] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:18] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:18] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==> Parameters: 1932725193193558016(Long)
[DEBUG] [2025-06-20 00:25:19] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:19] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:19] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==> Parameters: 1932722934955118592(Long)
[DEBUG] [2025-06-20 00:25:19] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:19] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:19] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==> Parameters: 1932717669144858624(Long)
[DEBUG] [2025-06-20 00:25:19] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:19] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:19] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==> Parameters: 1932709585647308800(Long)
[DEBUG] [2025-06-20 00:25:19] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> <==      Total: 8
[INFO ] [2025-06-20 00:25:19] T:[5e97f4e077624b1eb0b96427f7fb9f5b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> 请求完成, url=/admin/app/schResourceInfo/list，elapse=1050ms, respData={"data":{"dataList":[{"cardType":"昬腾 910A","computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749721394000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"graphicsMemory":"262144","hostIp":"************","id":1933097753617895424,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"001","fbUsedUtil":0,"resourceInfoId":1933097753617895424},"password":"Ascend12!@","port":40086,"productType":1,"resourceName":"001","resourceType":"x86","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749721394000,"updateUserId":1921773426524033024,"usedMemory":"96206.5"},{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749713323000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"graphicsMemory":"262144","hostIp":"*************","id":1933063901780381696,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"test","fbUsedUtil":0,"resourceInfoId":1933063901780381696},"password":"Ascend12!@","port":40086,"resourceName":"test","resourceType":"huawei","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749713323000,"updateUserId":1923195822141345792,"usedMemory":"95288.6"},{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749709726000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"graphicsMemory":"262144","hostIp":"*************","id":1933048812436197376,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"资源1001","fbUsedUtil":0,"resourceInfoId":1933048812436197376},"password":"Ascend12!@","port":40086,"resourceName":"资源1001","resourceType":"huawei","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749709726000,"updateUserId":1923195822141345792,"usedMemory":"93889.1"},{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749633757000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932730175653416960,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"test007","fbUsedUtil":0,"resourceInfoId":1932730175653416960},"password":"Ascend12!@","port":40086,"resourceName":"test007","resourceType":"x86","status":"offline","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749633757000,"updateUserId":1923195822141345792,"usedMemory":"92160.0"},{"cardType":"昬腾 910A","computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749633709000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932729975991963648,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"test006","fbUsedUtil":0,"resourceInfoId":1932729975991963648},"password":"Ascend12!@","port":40086,"productType":1,"resourceName":"test006","resourceType":"x86","status":"error","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749633709000,"updateUserId":1921773426524033024,"usedMemory":"92148.9"},{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749633668000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932729802632990720,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"test005","fbUsedUtil":0,"resourceInfoId":1932729802632990720},"password":"Ascend12!@","port":40086,"resourceName":"test005","resourceType":"x86","status":"maintenance","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749633668000,"updateUserId":1923195822141345792,"usedMemory":"92093.2"},{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749632569000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932725193193558016,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"testHH2","fbUsedUtil":0,"resourceInfoId":1932725193193558016},"password":"Ascend12!@","port":40086,"resourceName":"testHH2","resourceType":"huawei","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749632569000,"updateUserId":1923195822141345792,"usedMemory":"91710.6"},{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749632031000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932722934955118592,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"222","fbUsedUtil":0,"resourceInfoId":1932722934955118592},"password":"Ascend12!@","port":40086,"resourceName":"222","resourceType":"x86","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749632031000,"updateUserId":1923195822141345792,"usedMemory":"91962.7"},{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749630775000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932717669144858624,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"test003","fbUsedUtil":0,"resourceInfoId":1932717669144858624},"password":"Ascend12!@","port":40086,"resourceName":"test003","resourceType":"x86","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749630775000,"updateUserId":1923195822141345792,"usedMemory":"91731.7"},{"cardType":"昬腾 910A","computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749628848000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932709585647308800,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"test002","fbUsedUtil":0,"resourceInfoId":1932709585647308800},"password":"Ascend12!@","port":40086,"productType":1,"resourceName":"test002","resourceType":"huawei","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749628848000,"updateUserId":1921773426524033024,"usedMemory":"90748.8"}]},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[INFO ] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> 开始请求，url=/admin/app/schResourceInfo/list, reqData={"orderParam":[{"asc":false,"dateAggregateBy":"","fieldName":"createTime"}],"schResourceInfoDtoFilter":{},"pageParam":{"count":true,"pageNum":1,"pageSize":10}}
[DEBUG] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==>  Preparing: SELECT count(0) FROM sch_resource_info WHERE sch_resource_info.is_delete = 1
[DEBUG] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==> Parameters: 
[DEBUG] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==>  Preparing: SELECT * FROM sch_resource_info WHERE sch_resource_info.is_delete = 1 ORDER BY sch_resource_info.create_time DESC LIMIT ?
[DEBUG] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==> Parameters: 10(Integer)
[DEBUG] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> <==      Total: 10
[DEBUG] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id IN ( ? , ? , ? , ? , ? , ? , ? , ? , ? , ? ) AND ts >= now() - INTERVAL 60 MINUTE; ORDER BY ts DESC, resource_id DESC
[DEBUG] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==> Parameters: 1933097753617895424(Long), 1933063901780381696(Long), 1933048812436197376(Long), 1932730175653416960(Long), 1932729975991963648(Long), 1932729802632990720(Long), 1932725193193558016(Long), 1932722934955118592(Long), 1932717669144858624(Long), 1932709585647308800(Long)
[DEBUG] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> <==      Total: 0
[DEBUG] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==>  Preparing: SELECT t.* FROM sch_card_monitor t JOIN ( SELECT resource_id, max(ts) AS latest_ts FROM sch_card_monitor WHERE resource_id IN ( ? , ? , ? , ? , ? , ? , ? , ? , ? , ? ) GROUP BY resource_id ) m ON t.resource_id = m.resource_id AND t.ts = m.latest_ts;
[DEBUG] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==> Parameters: 1933097753617895424(Long), 1933063901780381696(Long), 1933048812436197376(Long), 1932730175653416960(Long), 1932729975991963648(Long), 1932729802632990720(Long), 1932725193193558016(Long), 1932722934955118592(Long), 1932717669144858624(Long), 1932709585647308800(Long)
[DEBUG] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> <==      Total: 80
[DEBUG] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==> Parameters: 1933097753617895424(Long)
[DEBUG] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==> Parameters: 1933063901780381696(Long)
[DEBUG] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==> Parameters: 1933048812436197376(Long)
[DEBUG] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==> Parameters: 1932730175653416960(Long)
[DEBUG] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==> Parameters: 1932729975991963648(Long)
[DEBUG] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==> Parameters: 1932729802632990720(Long)
[DEBUG] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==> Parameters: 1932725193193558016(Long)
[DEBUG] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:19] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==> Parameters: 1932722934955118592(Long)
[DEBUG] [2025-06-20 00:25:20] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:20] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:20] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==> Parameters: 1932717669144858624(Long)
[DEBUG] [2025-06-20 00:25:20] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:20] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:25:20] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==> Parameters: 1932709585647308800(Long)
[DEBUG] [2025-06-20 00:25:20] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> <==      Total: 8
[INFO ] [2025-06-20 00:25:20] T:[b6c5a7db58ac4c608482a30033596b00] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> 请求完成, url=/admin/app/schResourceInfo/list，elapse=883ms, respData={"data":{"dataList":[{"cardType":"昬腾 910A","computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749721394000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"graphicsMemory":"262144","hostIp":"************","id":1933097753617895424,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"001","fbUsedUtil":0,"resourceInfoId":1933097753617895424},"password":"Ascend12!@","port":40086,"productType":1,"resourceName":"001","resourceType":"x86","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749721394000,"updateUserId":1921773426524033024,"usedMemory":"96206.5"},{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749713323000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"graphicsMemory":"262144","hostIp":"*************","id":1933063901780381696,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"test","fbUsedUtil":0,"resourceInfoId":1933063901780381696},"password":"Ascend12!@","port":40086,"resourceName":"test","resourceType":"huawei","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749713323000,"updateUserId":1923195822141345792,"usedMemory":"95288.6"},{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749709726000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"graphicsMemory":"262144","hostIp":"*************","id":1933048812436197376,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"资源1001","fbUsedUtil":0,"resourceInfoId":1933048812436197376},"password":"Ascend12!@","port":40086,"resourceName":"资源1001","resourceType":"huawei","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749709726000,"updateUserId":1923195822141345792,"usedMemory":"93889.1"},{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749633757000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932730175653416960,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"test007","fbUsedUtil":0,"resourceInfoId":1932730175653416960},"password":"Ascend12!@","port":40086,"resourceName":"test007","resourceType":"x86","status":"offline","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749633757000,"updateUserId":1923195822141345792,"usedMemory":"92160.0"},{"cardType":"昬腾 910A","computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749633709000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932729975991963648,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"test006","fbUsedUtil":0,"resourceInfoId":1932729975991963648},"password":"Ascend12!@","port":40086,"productType":1,"resourceName":"test006","resourceType":"x86","status":"error","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749633709000,"updateUserId":1921773426524033024,"usedMemory":"92148.9"},{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749633668000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932729802632990720,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"test005","fbUsedUtil":0,"resourceInfoId":1932729802632990720},"password":"Ascend12!@","port":40086,"resourceName":"test005","resourceType":"x86","status":"maintenance","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749633668000,"updateUserId":1923195822141345792,"usedMemory":"92093.2"},{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749632569000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932725193193558016,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"testHH2","fbUsedUtil":0,"resourceInfoId":1932725193193558016},"password":"Ascend12!@","port":40086,"resourceName":"testHH2","resourceType":"huawei","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749632569000,"updateUserId":1923195822141345792,"usedMemory":"91710.6"},{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749632031000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932722934955118592,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"222","fbUsedUtil":0,"resourceInfoId":1932722934955118592},"password":"Ascend12!@","port":40086,"resourceName":"222","resourceType":"x86","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749632031000,"updateUserId":1923195822141345792,"usedMemory":"91962.7"},{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749630775000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932717669144858624,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"test003","fbUsedUtil":0,"resourceInfoId":1932717669144858624},"password":"Ascend12!@","port":40086,"resourceName":"test003","resourceType":"x86","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749630775000,"updateUserId":1923195822141345792,"usedMemory":"91731.7"},{"cardType":"昬腾 910A","computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749628848000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932709585647308800,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"test002","fbUsedUtil":0,"resourceInfoId":1932709585647308800},"password":"Ascend12!@","port":40086,"productType":1,"resourceName":"test002","resourceType":"huawei","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749628848000,"updateUserId":1921773426524033024,"usedMemory":"90748.8"}]},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[INFO ] [2025-06-20 00:25:25] T:[b6b18d7459044c2c9860227a02998211] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> 开始请求，url=/admin/app/schResourceInfo/connectionTest, reqData={"schResourceInfoDto":{"hostIp":"*************","id":1933063901780381696,"loginName":"root","password":"Ascend12!@","port":40086}}
[INFO ] [2025-06-20 00:25:25] T:[d133180b9f7840959472accea8592485] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> 开始请求，url=/admin/app/schComputeDevice/list, reqData={"schComputeDeviceDtoFilter":{"resourceId":1933063901780381696},"orderParam":[{"asc":false,"dateAggregateBy":"","fieldName":"createTime"}],"pageParam":{"count":true,"pageNum":1,"pageSize":10}}
[DEBUG] [2025-06-20 00:25:25] T:[d133180b9f7840959472accea8592485] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT count(0) FROM sch_compute_device WHERE sch_compute_device.resource_id = ? AND sch_compute_device.is_delete = 1
[DEBUG] [2025-06-20 00:25:25] T:[d133180b9f7840959472accea8592485] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: 1933063901780381696(Long)
[DEBUG] [2025-06-20 00:25:25] T:[d133180b9f7840959472accea8592485] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:25] T:[d133180b9f7840959472accea8592485] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT * FROM sch_compute_device WHERE sch_compute_device.resource_id = ? AND sch_compute_device.is_delete = 1 ORDER BY sch_compute_device.create_time DESC LIMIT ?
[DEBUG] [2025-06-20 00:25:25] T:[d133180b9f7840959472accea8592485] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: 1933063901780381696(Long), 10(Integer)
[DEBUG] [2025-06-20 00:25:25] T:[d133180b9f7840959472accea8592485] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:25:25] T:[d133180b9f7840959472accea8592485] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT * FROM sch_virtual_compute_card_situation WHERE sch_virtual_compute_card_situation.compute_device_id = ? AND sch_virtual_compute_card_situation.is_delete = 1
[DEBUG] [2025-06-20 00:25:25] T:[d133180b9f7840959472accea8592485] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: 1933063902719905792(Long)
[DEBUG] [2025-06-20 00:25:25] T:[d133180b9f7840959472accea8592485] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 0
[DEBUG] [2025-06-20 00:25:25] T:[d133180b9f7840959472accea8592485] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT * FROM sch_virtual_compute_card_situation WHERE sch_virtual_compute_card_situation.compute_device_id = ? AND sch_virtual_compute_card_situation.is_delete = 1
[DEBUG] [2025-06-20 00:25:25] T:[d133180b9f7840959472accea8592485] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: 1933063902719905793(Long)
[DEBUG] [2025-06-20 00:25:25] T:[d133180b9f7840959472accea8592485] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 0
[DEBUG] [2025-06-20 00:25:25] T:[d133180b9f7840959472accea8592485] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT * FROM sch_virtual_compute_card_situation WHERE sch_virtual_compute_card_situation.compute_device_id = ? AND sch_virtual_compute_card_situation.is_delete = 1
[DEBUG] [2025-06-20 00:25:25] T:[d133180b9f7840959472accea8592485] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: 1933063902719905794(Long)
[DEBUG] [2025-06-20 00:25:25] T:[d133180b9f7840959472accea8592485] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 0
[DEBUG] [2025-06-20 00:25:25] T:[d133180b9f7840959472accea8592485] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT * FROM sch_virtual_compute_card_situation WHERE sch_virtual_compute_card_situation.compute_device_id = ? AND sch_virtual_compute_card_situation.is_delete = 1
[DEBUG] [2025-06-20 00:25:25] T:[d133180b9f7840959472accea8592485] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: 1933063902724100096(Long)
[DEBUG] [2025-06-20 00:25:25] T:[d133180b9f7840959472accea8592485] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:25] T:[d133180b9f7840959472accea8592485] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT id,str_id,is_delete,create_user_id,create_time,update_user_id,update_time,data_user_id,data_dept_id,product_type,example,ai_core_number,memory,ai_cpu,vpc,vdec,jpegd FROM sch_virtual_compute_card_template WHERE is_delete=1 AND (id IN (?))
[DEBUG] [2025-06-20 00:25:25] T:[d133180b9f7840959472accea8592485] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: 1928336030277046272(Long)
[DEBUG] [2025-06-20 00:25:25] T:[d133180b9f7840959472accea8592485] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:25] T:[d133180b9f7840959472accea8592485] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT * FROM sch_virtual_compute_card_situation WHERE sch_virtual_compute_card_situation.compute_device_id = ? AND sch_virtual_compute_card_situation.is_delete = 1
[DEBUG] [2025-06-20 00:25:25] T:[d133180b9f7840959472accea8592485] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: 1933063902724100097(Long)
[DEBUG] [2025-06-20 00:25:25] T:[d133180b9f7840959472accea8592485] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 0
[DEBUG] [2025-06-20 00:25:25] T:[d133180b9f7840959472accea8592485] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT * FROM sch_virtual_compute_card_situation WHERE sch_virtual_compute_card_situation.compute_device_id = ? AND sch_virtual_compute_card_situation.is_delete = 1
[DEBUG] [2025-06-20 00:25:25] T:[d133180b9f7840959472accea8592485] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: 1933063902724100098(Long)
[DEBUG] [2025-06-20 00:25:25] T:[d133180b9f7840959472accea8592485] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 0
[DEBUG] [2025-06-20 00:25:25] T:[d133180b9f7840959472accea8592485] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT * FROM sch_virtual_compute_card_situation WHERE sch_virtual_compute_card_situation.compute_device_id = ? AND sch_virtual_compute_card_situation.is_delete = 1
[DEBUG] [2025-06-20 00:25:25] T:[d133180b9f7840959472accea8592485] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: 1933063902724100099(Long)
[DEBUG] [2025-06-20 00:25:25] T:[d133180b9f7840959472accea8592485] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 0
[DEBUG] [2025-06-20 00:25:25] T:[d133180b9f7840959472accea8592485] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT * FROM sch_virtual_compute_card_situation WHERE sch_virtual_compute_card_situation.compute_device_id = ? AND sch_virtual_compute_card_situation.is_delete = 1
[DEBUG] [2025-06-20 00:25:25] T:[d133180b9f7840959472accea8592485] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: 1933063902724100100(Long)
[DEBUG] [2025-06-20 00:25:25] T:[d133180b9f7840959472accea8592485] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 0
[INFO ] [2025-06-20 00:25:25] T:[d133180b9f7840959472accea8592485] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> 请求完成, url=/admin/app/schComputeDevice/list，elapse=675ms, respData={"data":{"dataList":[{"createTime":1749713324000,"createUserId":1923195822141345792,"dataDeptId":1787740798154969088,"deviceName":"910B-Ascend-V1","deviceNumber":0,"id":1933063902719905792,"memorySize":32768,"modelNumber":"910B-Ascend-V1","resourceId":1933063901780381696,"updateTime":1749713324000},{"createTime":1749713324000,"createUserId":1923195822141345792,"dataDeptId":1787740798154969088,"deviceName":"910B-Ascend-V1","deviceNumber":1,"id":1933063902719905793,"memorySize":32768,"modelNumber":"910B-Ascend-V1","resourceId":1933063901780381696,"updateTime":1749713324000},{"createTime":1749713324000,"createUserId":1923195822141345792,"dataDeptId":1787740798154969088,"deviceName":"910B-Ascend-V1","deviceNumber":2,"id":1933063902719905794,"memorySize":32768,"modelNumber":"910B-Ascend-V1","resourceId":1933063901780381696,"updateTime":1749713324000},{"createTime":1749713324000,"createUserId":1923195822141345792,"dataDeptId":1787740798154969088,"deviceName":"910B-Ascend-V1","deviceNumber":3,"id":1933063902724100096,"memorySize":32768,"modelNumber":"910B-Ascend-V1","resourceId":1933063901780381696,"schVirtualComputeCardSituationList":[{"computeDeviceId":1933063902724100096,"createTime":1750346848000,"createUserId":1921773426524033024,"id":1935721095680233472,"isDelete":1,"schVirtualComputeCardTemplate":{"aiCoreNumber":4,"aiCpu":1,"createTime":1748586111000,"createUserId":1923195822141345792,"example":"vir04","id":1928336030277046272,"isDelete":1,"jpegd":2,"memory":4,"productType":1,"updateTime":1748586111000,"updateUserId":1923195822141345792,"vdec":2,"vpc":2},"templateId":1928336030277046272,"updateTime":1750346848000,"updateUserId":1921773426524033024,"vccId":148}],"updateTime":1749713324000},{"createTime":1749713324000,"createUserId":1923195822141345792,"dataDeptId":1787740798154969088,"deviceName":"910B-Ascend-V1","deviceNumber":4,"id":1933063902724100097,"memorySize":32768,"modelNumber":"910B-Ascend-V1","resourceId":1933063901780381696,"updateTime":1749713324000},{"createTime":1749713324000,"createUserId":1923195822141345792,"dataDeptId":1787740798154969088,"deviceName":"910B-Ascend-V1","deviceNumber":5,"id":1933063902724100098,"memorySize":32768,"modelNumber":"910B-Ascend-V1","resourceId":1933063901780381696,"updateTime":1749713324000},{"createTime":1749713324000,"createUserId":1923195822141345792,"dataDeptId":1787740798154969088,"deviceName":"910B-Ascend-V1","deviceNumber":6,"id":1933063902724100099,"memorySize":32768,"modelNumber":"910B-Ascend-V1","resourceId":1933063901780381696,"updateTime":1749713324000},{"createTime":1749713324000,"createUserId":1923195822141345792,"dataDeptId":1787740798154969088,"deviceName":"910B-Ascend-V1","deviceNumber":7,"id":1933063902724100100,"memorySize":32768,"modelNumber":"910B-Ascend-V1","resourceId":1933063901780381696,"updateTime":1749713324000}],"totalCount":8},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[INFO ] [2025-06-20 00:25:27] T:[b6b18d7459044c2c9860227a02998211] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> SSH连接建立成功: root@*************:40086
[DEBUG] [2025-06-20 00:25:27] T:[b6b18d7459044c2c9860227a02998211] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==>  Preparing: SELECT memory_capacity,cpu_core_count,system_version,systemInfo FROM sch_resource_info WHERE id=?
[DEBUG] [2025-06-20 00:25:27] T:[b6b18d7459044c2c9860227a02998211] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==> Parameters: 1933063901780381696(Long)
[DEBUG] [2025-06-20 00:25:27] T:[b6b18d7459044c2c9860227a02998211] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> <==      Total: 1
[INFO ] [2025-06-20 00:25:27] T:[b6b18d7459044c2c9860227a02998211] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> 请求完成, url=/admin/app/schResourceInfo/connectionTest，elapse=2389ms, respData={"data":{"cpuCoreCount":"192","memoryCapacity":"773220.5","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)"},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[INFO ] [2025-06-20 00:25:31] T:[acf3f3e87163468296c4e5649b319ac0] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-5] ==> 开始请求，url=/admin/app/schVirtualComputeCardSituation/list, reqData={"orderParam":[{"asc":false,"dateAggregateBy":"","fieldName":"createTime"}],"schVirtualComputeCardSituationDtoFilter":{},"pageParam":{"count":true,"pageNum":1,"pageSize":10}}
[DEBUG] [2025-06-20 00:25:31] T:[acf3f3e87163468296c4e5649b319ac0] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-5] ==> ==>  Preparing: SELECT count(0) FROM sch_virtual_compute_card_situation WHERE sch_virtual_compute_card_situation.is_delete = 1
[DEBUG] [2025-06-20 00:25:31] T:[acf3f3e87163468296c4e5649b319ac0] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-5] ==> ==> Parameters: 
[DEBUG] [2025-06-20 00:25:31] T:[acf3f3e87163468296c4e5649b319ac0] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-5] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:31] T:[acf3f3e87163468296c4e5649b319ac0] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-5] ==> ==>  Preparing: SELECT * FROM sch_virtual_compute_card_situation WHERE sch_virtual_compute_card_situation.is_delete = 1 ORDER BY sch_virtual_compute_card_situation.create_time DESC LIMIT ?
[DEBUG] [2025-06-20 00:25:31] T:[acf3f3e87163468296c4e5649b319ac0] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-5] ==> ==> Parameters: 10(Integer)
[DEBUG] [2025-06-20 00:25:31] T:[acf3f3e87163468296c4e5649b319ac0] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-5] ==> <==      Total: 10
[DEBUG] [2025-06-20 00:25:31] T:[acf3f3e87163468296c4e5649b319ac0] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-5] ==> ==>  Preparing: SELECT id,str_id,is_delete,create_user_id,create_time,update_user_id,update_time,data_user_id,data_dept_id,product_type,example,ai_core_number,memory,ai_cpu,vpc,vdec,jpegd FROM sch_virtual_compute_card_template WHERE is_delete=1 AND (id IN (?,?,?))
[DEBUG] [2025-06-20 00:25:31] T:[acf3f3e87163468296c4e5649b319ac0] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-5] ==> ==> Parameters: 1928336210011361280(Long), 1928336030277046272(Long), 1928335353085693952(Long)
[DEBUG] [2025-06-20 00:25:31] T:[acf3f3e87163468296c4e5649b319ac0] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-5] ==> <==      Total: 3
[INFO ] [2025-06-20 00:25:31] T:[acf3f3e87163468296c4e5649b319ac0] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-5] ==> 请求完成, url=/admin/app/schVirtualComputeCardSituation/list，elapse=192ms, respData={"data":{"dataList":[{"computeDeviceId":1933063902724100096,"createTime":1750346848000,"createUserId":1921773426524033024,"id":1935721095680233472,"schVirtualComputeCardTemplate":{"aiCoreNumber":4,"aiCpu":1,"createTime":1748586111000,"createUserId":1923195822141345792,"example":"vir04","id":1928336030277046272,"isDelete":1,"jpegd":2,"memory":4,"productType":1,"updateTime":1748586111000,"updateUserId":1923195822141345792,"vdec":2,"vpc":2},"templateId":1928336030277046272,"updateTime":1750346848000,"updateUserId":1921773426524033024,"vccId":148},{"computeDeviceId":1933048813337972736,"createTime":1750339977000,"createUserId":1921773426524033024,"id":1935692277137543168,"schVirtualComputeCardTemplate":{"aiCoreNumber":2,"aiCpu":1,"createTime":1748586154000,"createUserId":1923195822141345792,"example":"vir02","id":1928336210011361280,"isDelete":1,"jpegd":1,"memory":2,"productType":1,"updateTime":1748586154000,"updateUserId":1923195822141345792,"vdec":1,"vpc":1},"templateId":1928336210011361280,"updateTime":1750339977000,"updateUserId":1921773426524033024,"vccId":105},{"computeDeviceId":1933097754570002433,"createTime":1750041200000,"createUserId":1921773426524033024,"id":1934439114405318656,"schVirtualComputeCardTemplate":{"$ref":"$.data.dataList[1].schVirtualComputeCardTemplate"},"templateId":1928336210011361280,"updateTime":1750041200000,"updateUserId":1921773426524033024,"vccId":116},{"computeDeviceId":1932725194112110592,"createTime":1750039286000,"createUserId":1921773426524033024,"id":1934431089007267840,"schVirtualComputeCardTemplate":{"$ref":"$.data.dataList[1].schVirtualComputeCardTemplate"},"templateId":1928336210011361280,"updateTime":1750039286000,"updateUserId":1921773426524033024,"vccId":104},{"computeDeviceId":1932725194112110592,"createTime":1750036761000,"createUserId":1921773426524033024,"id":1934420497013936128,"schVirtualComputeCardTemplate":{"$ref":"$.data.dataList[1].schVirtualComputeCardTemplate"},"templateId":1928336210011361280,"updateTime":1750036761000,"updateUserId":1921773426524033024,"vccId":103},{"computeDeviceId":1932730176622301186,"createTime":1749635186000,"createUserId":1923195822141345792,"id":1932736168579829760,"schVirtualComputeCardTemplate":{"$ref":"$.data.dataList[1].schVirtualComputeCardTemplate"},"templateId":1928336210011361280,"updateTime":1749635186000,"updateUserId":1923195822141345792,"vccId":218},{"computeDeviceId":1932717670214406144,"createTime":1749632052000,"createUserId":1923195822141345792,"id":1932723023631093760,"schVirtualComputeCardTemplate":{"$ref":"$.data.dataList[0].schVirtualComputeCardTemplate"},"templateId":1928336030277046272,"updateTime":1749632052000,"updateUserId":1923195822141345792,"vccId":217},{"computeDeviceId":1932717670214406144,"createTime":1749631994000,"createUserId":1923195822141345792,"id":1932722782693494784,"schVirtualComputeCardTemplate":{"aiCoreNumber":8,"aiCpu":3,"createTime":1748585950000,"createUserId":1923195822141345792,"example":"vir08","id":1928335353085693952,"isDelete":1,"jpegd":4,"memory":8,"productType":1,"updateTime":1748585950000,"updateUserId":1923195822141345792,"vdec":4,"vpc":4},"templateId":1928335353085693952,"updateTime":1749631994000,"updateUserId":1923195822141345792,"vccId":216},{"computeDeviceId":1932717670214406144,"createTime":1749631937000,"createUserId":1923195822141345792,"id":1932722542930300928,"schVirtualComputeCardTemplate":{"$ref":"$.data.dataList[0].schVirtualComputeCardTemplate"},"templateId":1928336030277046272,"updateTime":1749631937000,"updateUserId":1923195822141345792,"vccId":215},{"computeDeviceId":1932717670214406144,"createTime":1749631913000,"createUserId":1923195822141345792,"id":1932722443105865728,"schVirtualComputeCardTemplate":{"$ref":"$.data.dataList[7].schVirtualComputeCardTemplate"},"templateId":1928335353085693952,"updateTime":1749631913000,"updateUserId":1923195822141345792,"vccId":214}],"totalCount":24},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[INFO ] [2025-06-20 00:25:34] T:[e54d460261884e22aabaca3892e99861] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> 开始请求，url=/admin/app/schVirtualComputeCardSituation/list, reqData={"orderParam":[{"asc":false,"dateAggregateBy":"","fieldName":"createTime"}],"schVirtualComputeCardSituationDtoFilter":{"computeDeviceId":1933063902719905792},"pageParam":{"count":true,"pageNum":1,"pageSize":10}}
[INFO ] [2025-06-20 00:25:34] T:[2e9e5529d5274526bd76255d56056b9f] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> 开始请求，url=/admin/app/schVirtualComputeCardSituation/list, reqData={"orderParam":[{"asc":false,"dateAggregateBy":"","fieldName":"createTime"}],"schVirtualComputeCardSituationDtoFilter":{"computeDeviceId":1933063902719905792},"pageParam":{"count":true,"pageNum":1,"pageSize":10}}
[DEBUG] [2025-06-20 00:25:34] T:[2e9e5529d5274526bd76255d56056b9f] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> ==>  Preparing: SELECT count(0) FROM sch_virtual_compute_card_situation WHERE sch_virtual_compute_card_situation.compute_device_id = ? AND sch_virtual_compute_card_situation.is_delete = 1
[DEBUG] [2025-06-20 00:25:34] T:[e54d460261884e22aabaca3892e99861] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT count(0) FROM sch_virtual_compute_card_situation WHERE sch_virtual_compute_card_situation.compute_device_id = ? AND sch_virtual_compute_card_situation.is_delete = 1
[DEBUG] [2025-06-20 00:25:34] T:[2e9e5529d5274526bd76255d56056b9f] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> ==> Parameters: 1933063902719905792(Long)
[DEBUG] [2025-06-20 00:25:34] T:[e54d460261884e22aabaca3892e99861] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1933063902719905792(Long)
[DEBUG] [2025-06-20 00:25:34] T:[e54d460261884e22aabaca3892e99861] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:34] T:[2e9e5529d5274526bd76255d56056b9f] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> <==      Total: 1
[INFO ] [2025-06-20 00:25:34] T:[2e9e5529d5274526bd76255d56056b9f] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> 请求完成, url=/admin/app/schVirtualComputeCardSituation/list，elapse=49ms, respData={"data":{"dataList":[],"totalCount":0},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[INFO ] [2025-06-20 00:25:34] T:[e54d460261884e22aabaca3892e99861] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> 请求完成, url=/admin/app/schVirtualComputeCardSituation/list，elapse=49ms, respData={"data":{"dataList":[],"totalCount":0},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[INFO ] [2025-06-20 00:25:34] T:[527dce8e072648dda4e51fe90162df97] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> 开始请求，url=/admin/app/schVirtualComputeCardTemplate/list, reqData={"orderParam":[{"asc":false,"dateAggregateBy":"","fieldName":"createTime"}],"schVirtualComputeCardTemplateDtoFilter":{},"pageParam":{"count":true,"pageNum":1,"pageSize":10}}
[DEBUG] [2025-06-20 00:25:34] T:[527dce8e072648dda4e51fe90162df97] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==>  Preparing: SELECT count(0) FROM sch_virtual_compute_card_template WHERE sch_virtual_compute_card_template.is_delete = 1
[DEBUG] [2025-06-20 00:25:34] T:[527dce8e072648dda4e51fe90162df97] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==> Parameters: 
[DEBUG] [2025-06-20 00:25:34] T:[527dce8e072648dda4e51fe90162df97] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:34] T:[527dce8e072648dda4e51fe90162df97] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==>  Preparing: SELECT * FROM sch_virtual_compute_card_template WHERE sch_virtual_compute_card_template.is_delete = 1 ORDER BY sch_virtual_compute_card_template.create_time DESC LIMIT ?
[DEBUG] [2025-06-20 00:25:34] T:[527dce8e072648dda4e51fe90162df97] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==> Parameters: 10(Integer)
[DEBUG] [2025-06-20 00:25:34] T:[527dce8e072648dda4e51fe90162df97] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> <==      Total: 4
[INFO ] [2025-06-20 00:25:34] T:[527dce8e072648dda4e51fe90162df97] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> 请求完成, url=/admin/app/schVirtualComputeCardTemplate/list，elapse=147ms, respData={"data":{"dataList":[{"aiCoreNumber":2,"aiCpu":1,"createTime":1748586154000,"createUserId":1923195822141345792,"example":"vir02","id":1928336210011361280,"jpegd":1,"memory":2,"productType":1,"updateTime":1748586154000,"updateUserId":1923195822141345792,"vdec":1,"vpc":1},{"aiCoreNumber":4,"aiCpu":1,"createTime":1748586111000,"createUserId":1923195822141345792,"example":"vir04","id":1928336030277046272,"jpegd":2,"memory":4,"productType":1,"updateTime":1748586111000,"updateUserId":1923195822141345792,"vdec":2,"vpc":2},{"aiCoreNumber":8,"aiCpu":3,"createTime":1748585950000,"createUserId":1923195822141345792,"example":"vir08","id":1928335353085693952,"jpegd":4,"memory":8,"productType":1,"updateTime":1748585950000,"updateUserId":1923195822141345792,"vdec":4,"vpc":4},{"aiCoreNumber":16,"aiCpu":7,"createTime":1748585832000,"createUserId":1923195822141345792,"example":"vir16","id":1928334861026725888,"jpegd":8,"memory":16,"productType":1,"updateTime":1748585832000,"updateUserId":1923195822141345792,"vdec":8,"vpc":8}],"totalCount":4},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[INFO ] [2025-06-20 00:25:46] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> 开始请求，url=/admin/app/schVirtualComputeCardSituation/createVNPU, reqData={"schVirtualComputeCardSituationDto":{"computeDeviceId":1933063902719905792,"templateId":1928336210011361280}}
[DEBUG] [2025-06-20 00:25:46] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE id=? AND is_delete=1
[DEBUG] [2025-06-20 00:25:46] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: 1933063902719905792(Long)
[DEBUG] [2025-06-20 00:25:46] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:46] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-06-20 00:25:46] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: 1933063901780381696(Long)
[DEBUG] [2025-06-20 00:25:46] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:46] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-06-20 00:25:46] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: 1933063901780381696(Long)
[DEBUG] [2025-06-20 00:25:46] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:46] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-06-20 00:25:46] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: 1933063901780381696(Long)
[DEBUG] [2025-06-20 00:25:46] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 1
[INFO ] [2025-06-20 00:25:47] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> SSH连接建立成功: root@*************:40086
[DEBUG] [2025-06-20 00:25:48] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT id,str_id,is_delete,create_user_id,create_time,update_user_id,update_time,data_user_id,data_dept_id,product_type,example,ai_core_number,memory,ai_cpu,vpc,vdec,jpegd FROM sch_virtual_compute_card_template WHERE is_delete=1 AND (example = ?)
[DEBUG] [2025-06-20 00:25:48] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: vir02(String)
[DEBUG] [2025-06-20 00:25:48] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:48] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT id,str_id,is_delete,create_user_id,create_time,update_user_id,update_time,data_user_id,data_dept_id,product_type,example,ai_core_number,memory,ai_cpu,vpc,vdec,jpegd FROM sch_virtual_compute_card_template WHERE is_delete=1 AND (example = ?)
[DEBUG] [2025-06-20 00:25:48] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: vir02(String)
[DEBUG] [2025-06-20 00:25:49] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:49] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT id,str_id,is_delete,create_user_id,create_time,update_user_id,update_time,data_user_id,data_dept_id,product_type,example,ai_core_number,memory,ai_cpu,vpc,vdec,jpegd FROM sch_virtual_compute_card_template WHERE is_delete=1 AND (example = ?)
[DEBUG] [2025-06-20 00:25:49] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: vir02(String)
[DEBUG] [2025-06-20 00:25:49] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:49] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT id,str_id,is_delete,create_user_id,create_time,update_user_id,update_time,data_user_id,data_dept_id,product_type,example,ai_core_number,memory,ai_cpu,vpc,vdec,jpegd FROM sch_virtual_compute_card_template WHERE is_delete=1 AND (example = ?)
[DEBUG] [2025-06-20 00:25:49] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: vir02(String)
[DEBUG] [2025-06-20 00:25:49] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:49] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT id,str_id,is_delete,create_user_id,create_time,update_user_id,update_time,data_user_id,data_dept_id,product_type,example,ai_core_number,memory,ai_cpu,vpc,vdec,jpegd FROM sch_virtual_compute_card_template WHERE is_delete=1 AND (example = ?)
[DEBUG] [2025-06-20 00:25:49] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: vir02(String)
[DEBUG] [2025-06-20 00:25:49] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:49] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT id,str_id,is_delete,create_user_id,create_time,update_user_id,update_time,data_user_id,data_dept_id,product_type,example,ai_core_number,memory,ai_cpu,vpc,vdec,jpegd FROM sch_virtual_compute_card_template WHERE is_delete=1 AND (example = ?)
[DEBUG] [2025-06-20 00:25:49] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: vir02(String)
[DEBUG] [2025-06-20 00:25:49] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:49] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT id,str_id,is_delete,create_user_id,create_time,update_user_id,update_time,data_user_id,data_dept_id,product_type,example,ai_core_number,memory,ai_cpu,vpc,vdec,jpegd FROM sch_virtual_compute_card_template WHERE is_delete=1 AND (example IN (?,?,?,?,?,?))
[DEBUG] [2025-06-20 00:25:49] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: vir02(String), vir02(String), vir02(String), vir02(String), vir02(String), vir02(String)
[DEBUG] [2025-06-20 00:25:49] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:49] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT id,str_id,is_delete,create_user_id,create_time,update_user_id,update_time,data_user_id,data_dept_id,product_type,example,ai_core_number,memory,ai_cpu,vpc,vdec,jpegd FROM sch_virtual_compute_card_template WHERE id=? AND is_delete=1
[DEBUG] [2025-06-20 00:25:49] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: 1928336210011361280(Long)
[DEBUG] [2025-06-20 00:25:49] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:25:49] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-06-20 00:25:49] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: 1933063901780381696(Long)
[DEBUG] [2025-06-20 00:25:49] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 1
[INFO ] [2025-06-20 00:25:50] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> SSH连接建立成功: root@*************:40086
[DEBUG] [2025-06-20 00:25:52] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: INSERT INTO sch_virtual_compute_card_situation ( id, is_delete, create_user_id, create_time, update_user_id, update_time, compute_device_id, template_id, vcc_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ? )
[DEBUG] [2025-06-20 00:25:52] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: 1935735795411324928(Long), 1(Integer), 1921773426524033024(Long), 2025-06-20 00:25:52.515(Timestamp), 1921773426524033024(Long), 2025-06-20 00:25:52.515(Timestamp), 1933063902719905792(Long), 1928336210011361280(Long), 106(Integer)
[DEBUG] [2025-06-20 00:25:52] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==    Updates: 1
[INFO ] [2025-06-20 00:25:52] T:[91a3d42ce50341d2a3ca846fc676908b] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> 请求完成, url=/admin/app/schVirtualComputeCardSituation/createVNPU，elapse=6288ms, respData={"data":"Create vnpu success","errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":"Create vnpu success","success":true}
[INFO ] [2025-06-20 00:28:05] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> 开始请求，url=/admin/app/schResourceInfo/list, reqData={"orderParam":[{"asc":false,"dateAggregateBy":"","fieldName":"createTime"}],"schResourceInfoDtoFilter":{},"pageParam":{"count":true,"pageNum":1,"pageSize":10}}
[DEBUG] [2025-06-20 00:28:05] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> ==>  Preparing: SELECT count(0) FROM sch_resource_info WHERE sch_resource_info.is_delete = 1
[DEBUG] [2025-06-20 00:28:05] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> ==> Parameters: 
[DEBUG] [2025-06-20 00:28:05] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:28:05] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> ==>  Preparing: SELECT * FROM sch_resource_info WHERE sch_resource_info.is_delete = 1 ORDER BY sch_resource_info.create_time DESC LIMIT ?
[DEBUG] [2025-06-20 00:28:05] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> ==> Parameters: 10(Integer)
[DEBUG] [2025-06-20 00:28:05] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> <==      Total: 10
[DEBUG] [2025-06-20 00:28:05] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id IN ( ? , ? , ? , ? , ? , ? , ? , ? , ? , ? ) AND ts >= now() - INTERVAL 60 MINUTE; ORDER BY ts DESC, resource_id DESC
[DEBUG] [2025-06-20 00:28:05] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> ==> Parameters: 1933097753617895424(Long), 1933063901780381696(Long), 1933048812436197376(Long), 1932730175653416960(Long), 1932729975991963648(Long), 1932729802632990720(Long), 1932725193193558016(Long), 1932722934955118592(Long), 1932717669144858624(Long), 1932709585647308800(Long)
[DEBUG] [2025-06-20 00:28:05] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> <==      Total: 0
[DEBUG] [2025-06-20 00:28:05] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> ==>  Preparing: SELECT t.* FROM sch_card_monitor t JOIN ( SELECT resource_id, max(ts) AS latest_ts FROM sch_card_monitor WHERE resource_id IN ( ? , ? , ? , ? , ? , ? , ? , ? , ? , ? ) GROUP BY resource_id ) m ON t.resource_id = m.resource_id AND t.ts = m.latest_ts;
[DEBUG] [2025-06-20 00:28:05] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> ==> Parameters: 1933097753617895424(Long), 1933063901780381696(Long), 1933048812436197376(Long), 1932730175653416960(Long), 1932729975991963648(Long), 1932729802632990720(Long), 1932725193193558016(Long), 1932722934955118592(Long), 1932717669144858624(Long), 1932709585647308800(Long)
[DEBUG] [2025-06-20 00:28:06] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> <==      Total: 80
[DEBUG] [2025-06-20 00:28:06] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:28:06] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> ==> Parameters: 1933097753617895424(Long)
[DEBUG] [2025-06-20 00:28:06] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:28:06] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:28:06] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> ==> Parameters: 1933063901780381696(Long)
[DEBUG] [2025-06-20 00:28:06] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:28:06] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:28:06] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> ==> Parameters: 1933048812436197376(Long)
[DEBUG] [2025-06-20 00:28:06] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:28:06] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:28:06] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> ==> Parameters: 1932730175653416960(Long)
[DEBUG] [2025-06-20 00:28:06] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:28:06] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:28:06] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> ==> Parameters: 1932729975991963648(Long)
[DEBUG] [2025-06-20 00:28:06] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:28:06] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:28:06] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> ==> Parameters: 1932729802632990720(Long)
[DEBUG] [2025-06-20 00:28:06] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:28:06] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:28:06] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> ==> Parameters: 1932725193193558016(Long)
[DEBUG] [2025-06-20 00:28:06] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:28:06] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:28:06] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> ==> Parameters: 1932722934955118592(Long)
[DEBUG] [2025-06-20 00:28:06] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:28:06] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:28:06] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> ==> Parameters: 1932717669144858624(Long)
[DEBUG] [2025-06-20 00:28:06] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:28:06] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-06-20 00:28:06] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> ==> Parameters: 1932709585647308800(Long)
[DEBUG] [2025-06-20 00:28:06] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> <==      Total: 8
[INFO ] [2025-06-20 00:28:06] T:[afad6451b23a42c9b7e59844e5e71f35] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-6] ==> 请求完成, url=/admin/app/schResourceInfo/list，elapse=1021ms, respData={"data":{"dataList":[{"cardType":"昬腾 910A","computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749721394000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"graphicsMemory":"262144","hostIp":"************","id":1933097753617895424,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"001","fbUsedUtil":0,"resourceInfoId":1933097753617895424},"password":"Ascend12!@","port":40086,"productType":1,"resourceName":"001","resourceType":"x86","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749721394000,"updateUserId":1921773426524033024,"usedMemory":"96206.5"},{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749713323000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"graphicsMemory":"262144","hostIp":"*************","id":1933063901780381696,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"test","fbUsedUtil":0,"resourceInfoId":1933063901780381696},"password":"Ascend12!@","port":40086,"resourceName":"test","resourceType":"huawei","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749713323000,"updateUserId":1923195822141345792,"usedMemory":"95288.6"},{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749709726000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"graphicsMemory":"262144","hostIp":"*************","id":1933048812436197376,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"资源1001","fbUsedUtil":0,"resourceInfoId":1933048812436197376},"password":"Ascend12!@","port":40086,"resourceName":"资源1001","resourceType":"huawei","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749709726000,"updateUserId":1923195822141345792,"usedMemory":"93889.1"},{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749633757000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932730175653416960,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"test007","fbUsedUtil":0,"resourceInfoId":1932730175653416960},"password":"Ascend12!@","port":40086,"resourceName":"test007","resourceType":"x86","status":"offline","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749633757000,"updateUserId":1923195822141345792,"usedMemory":"92160.0"},{"cardType":"昬腾 910A","computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749633709000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932729975991963648,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"test006","fbUsedUtil":0,"resourceInfoId":1932729975991963648},"password":"Ascend12!@","port":40086,"productType":1,"resourceName":"test006","resourceType":"x86","status":"error","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749633709000,"updateUserId":1921773426524033024,"usedMemory":"92148.9"},{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749633668000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932729802632990720,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"test005","fbUsedUtil":0,"resourceInfoId":1932729802632990720},"password":"Ascend12!@","port":40086,"resourceName":"test005","resourceType":"x86","status":"maintenance","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749633668000,"updateUserId":1923195822141345792,"usedMemory":"92093.2"},{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749632569000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932725193193558016,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"testHH2","fbUsedUtil":0,"resourceInfoId":1932725193193558016},"password":"Ascend12!@","port":40086,"resourceName":"testHH2","resourceType":"huawei","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749632569000,"updateUserId":1923195822141345792,"usedMemory":"91710.6"},{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749632031000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932722934955118592,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"222","fbUsedUtil":0,"resourceInfoId":1932722934955118592},"password":"Ascend12!@","port":40086,"resourceName":"222","resourceType":"x86","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749632031000,"updateUserId":1923195822141345792,"usedMemory":"91962.7"},{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749630775000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932717669144858624,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"test003","fbUsedUtil":0,"resourceInfoId":1932717669144858624},"password":"Ascend12!@","port":40086,"resourceName":"test003","resourceType":"x86","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749630775000,"updateUserId":1923195822141345792,"usedMemory":"91731.7"},{"cardType":"昬腾 910A","computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749628848000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932709585647308800,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"test002","fbUsedUtil":0,"resourceInfoId":1932709585647308800},"password":"Ascend12!@","port":40086,"productType":1,"resourceName":"test002","resourceType":"huawei","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749628848000,"updateUserId":1921773426524033024,"usedMemory":"90748.8"}]},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[INFO ] [2025-06-20 00:28:29] T:[c34a9a6aece543acb5fdcc76cf36075e] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> 开始请求，url=/admin/app/schBusinessDict/list, reqData={"orderParam":[{"asc":false,"dateAggregateBy":"","fieldName":"createTime"}],"schBusinessDictDtoFilter":{"bindType":"TaskMonitor"},"pageParam":{"count":true,"pageNum":1,"pageSize":10}}
[INFO ] [2025-06-20 00:28:29] T:[f898b8913da64f388eb18095ab531c10] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> 开始请求，url=/admin/app/schTaskMonitoring/taskCount, reqData={}
[DEBUG] [2025-06-20 00:28:29] T:[f898b8913da64f388eb18095ab531c10] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> ==>  Preparing: SELECT COUNT(id) AS totalTask, SUM(IF(ti.approve_state = 'pending', 1, 0)) AS noCommit, SUM(IF(ti.status= 'pending',1,0)) AS pending, SUM(IF(ti.status = 'failed',1,0)) AS failed, SUM(IF(ti.status = 'starting', 1, 0)) AS start, SUM(IF(ti.status = 'queued', 1, 0)) AS queue, SUM(IF(ti.status = 'running', 1, 0)) AS run, SUM(IF(ti.status = 'finished', 1, 0)) AS finish, SUM(IF(ti.status = 'stop' AND ti.container_status IN ('exited', 'paused','exited(0)'), 1, 0)) AS stop, SUM(IF(ti.status = 'running' AND ti.container_status IN ('dead','exited','paused','failed','restarting'), 1, 0)) AS exception FROM sch_task_info ti WHERE ti.is_delete = 1
[DEBUG] [2025-06-20 00:28:29] T:[f898b8913da64f388eb18095ab531c10] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> ==> Parameters: 
[DEBUG] [2025-06-20 00:28:29] T:[c34a9a6aece543acb5fdcc76cf36075e] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT count(0) FROM sch_business_dict WHERE sch_business_dict.bind_type LIKE ? AND sch_business_dict.is_delete = 1
[DEBUG] [2025-06-20 00:28:29] T:[f898b8913da64f388eb18095ab531c10] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:28:29] T:[c34a9a6aece543acb5fdcc76cf36075e] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: %TaskMonitor%(String)
[INFO ] [2025-06-20 00:28:29] T:[f898b8913da64f388eb18095ab531c10] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> 请求完成, url=/admin/app/schTaskMonitoring/taskCount，elapse=89ms, respData={"data":{"exception":0,"failed":17,"finish":2,"noCommit":0,"pending":0,"queue":0,"run":20,"start":0,"stop":2,"totalTask":41},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[DEBUG] [2025-06-20 00:28:29] T:[c34a9a6aece543acb5fdcc76cf36075e] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:28:29] T:[c34a9a6aece543acb5fdcc76cf36075e] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==>  Preparing: SELECT * FROM sch_business_dict WHERE sch_business_dict.bind_type LIKE ? AND sch_business_dict.is_delete = 1 ORDER BY sch_business_dict.create_time DESC LIMIT ?
[DEBUG] [2025-06-20 00:28:29] T:[c34a9a6aece543acb5fdcc76cf36075e] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> ==> Parameters: %TaskMonitor%(String), 10(Integer)
[DEBUG] [2025-06-20 00:28:29] T:[c34a9a6aece543acb5fdcc76cf36075e] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> <==      Total: 5
[INFO ] [2025-06-20 00:28:29] T:[c34a9a6aece543acb5fdcc76cf36075e] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-9] ==> 请求完成, url=/admin/app/schBusinessDict/list，elapse=179ms, respData={"data":{"dataList":[{"bindType":"TaskMonitor","colorData":"","createTime":1749632889000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"dictDescription":"","dictLevel":0,"dictName":"测试任务HH","id":1932726533772808192,"showOrder":0,"strId":"88a0d36e3034136a6be1c29502290286","updateTime":1749632889000,"updateUserId":1923195822141345792},{"bindType":"TaskMonitor","colorData":"","createTime":1749632636000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"dictDescription":"","dictLevel":0,"dictName":"测试任务@","id":1932725472521621504,"showOrder":0,"strId":"c8dbbbbb821f052abeaa616b5a83e2fb","updateTime":1749632636000,"updateUserId":1923195822141345792},{"bindType":"TaskMonitor","colorData":"","createTime":1749460618000,"createUserId":1923195822141345792,"dictDescription":"","dictLevel":0,"dictName":"测试任务321","id":***********45006592,"showOrder":0,"strId":"d26b2ecf9311ea9ddb51717ef281be5b","updateTime":1749460618000,"updateUserId":1923195822141345792},{"bindType":"TaskMonitor","colorData":"","createTime":1749460600000,"createUserId":1923195822141345792,"dataDeptId":0,"dataUserId":0,"dictDescription":"","dictLevel":0,"dictName":"测试任务haha","id":1932003904598315008,"showOrder":0,"strId":"391223d895fd9155f61e0834da7228b5","updateTime":1749522525000,"updateUserId":1923195822141345792},{"bindType":"TaskMonitor","colorData":"","createTime":1749457922000,"createUserId":1923195822141345792,"dataDeptId":0,"dataUserId":0,"dictDescription":"","dictLevel":0,"dictName":"测试任务456-test","id":1931992669626568704,"showOrder":0,"strId":"86dfa84fcccd1f7484eb59369638e1e0","updateTime":1749632612000,"updateUserId":1923195822141345792}],"totalCount":5},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[INFO ] [2025-06-20 00:28:30] T:[c3fe26181fea48f0834a01cb88202128] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> 开始请求，url=/admin/app/schTaskInfo/list, reqData={"orderParam":[{"asc":false,"dateAggregateBy":"","fieldName":"createTime"}],"schTaskInfoDtoFilter":{"dictId":1932726533772808192},"pageParam":{"count":true,"pageNum":1,"pageSize":10}}
[DEBUG] [2025-06-20 00:28:30] T:[c3fe26181fea48f0834a01cb88202128] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> ==>  Preparing: SELECT count(0) FROM sch_task_info WHERE sch_task_info.dict_id = ? AND sch_task_info.is_delete = 1
[DEBUG] [2025-06-20 00:28:30] T:[c3fe26181fea48f0834a01cb88202128] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> ==> Parameters: 1932726533772808192(Long)
[DEBUG] [2025-06-20 00:28:30] T:[c3fe26181fea48f0834a01cb88202128] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:28:30] T:[c3fe26181fea48f0834a01cb88202128] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> ==>  Preparing: SELECT * FROM sch_task_info WHERE sch_task_info.dict_id = ? AND sch_task_info.is_delete = 1 ORDER BY sch_task_info.create_time DESC LIMIT ?
[DEBUG] [2025-06-20 00:28:30] T:[c3fe26181fea48f0834a01cb88202128] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> ==> Parameters: 1932726533772808192(Long), 10(Integer)
[DEBUG] [2025-06-20 00:28:30] T:[c3fe26181fea48f0834a01cb88202128] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> <==      Total: 3
[DEBUG] [2025-06-20 00:28:30] T:[c3fe26181fea48f0834a01cb88202128] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,image_name,image_desc,image_url,image_version FROM sch_task_image WHERE is_delete=1 AND (id IN (?,?))
[DEBUG] [2025-06-20 00:28:30] T:[c3fe26181fea48f0834a01cb88202128] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> ==> Parameters: 1930147594567159808(Long), 1933920684832985088(Long)
[DEBUG] [2025-06-20 00:28:30] T:[c3fe26181fea48f0834a01cb88202128] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:28:30] T:[c3fe26181fea48f0834a01cb88202128] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,pool_name,pool_description FROM sch_resource_pool WHERE is_delete=1 AND (id IN (?,?))
[DEBUG] [2025-06-20 00:28:30] T:[c3fe26181fea48f0834a01cb88202128] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> ==> Parameters: 1932711144816906240(Long), 1932706048758517760(Long)
[DEBUG] [2025-06-20 00:28:30] T:[c3fe26181fea48f0834a01cb88202128] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> <==      Total: 2
[DEBUG] [2025-06-20 00:28:30] T:[c3fe26181fea48f0834a01cb88202128] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE is_delete=1 AND (id IN (?,?,?))
[DEBUG] [2025-06-20 00:28:30] T:[c3fe26181fea48f0834a01cb88202128] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> ==> Parameters: 1932725193193558016(Long), 1932705890410958848(Long), 1932730175653416960(Long)
[DEBUG] [2025-06-20 00:28:30] T:[c3fe26181fea48f0834a01cb88202128] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> <==      Total: 3
[DEBUG] [2025-06-20 00:28:30] T:[c3fe26181fea48f0834a01cb88202128] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (id IN (?,?,?))
[DEBUG] [2025-06-20 00:28:30] T:[c3fe26181fea48f0834a01cb88202128] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> ==> Parameters: 1932725194112110592(Long), 1932705891413397506(Long), 1932730176622301186(Long)
[DEBUG] [2025-06-20 00:28:30] T:[c3fe26181fea48f0834a01cb88202128] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> <==      Total: 3
[DEBUG] [2025-06-20 00:28:30] T:[c3fe26181fea48f0834a01cb88202128] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> ==>  Preparing: SELECT id,str_id,is_delete,create_user_id,create_time,update_user_id,update_time,data_user_id,data_dept_id,compute_device_id,template_id,vcc_id,vgroup_id FROM sch_virtual_compute_card_situation WHERE is_delete=1 AND (id IN (?,?,?))
[DEBUG] [2025-06-20 00:28:30] T:[c3fe26181fea48f0834a01cb88202128] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> ==> Parameters: 103(Long), 218(Long), 1932719052900274176(Long)
[DEBUG] [2025-06-20 00:28:30] T:[c3fe26181fea48f0834a01cb88202128] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:28:30] T:[c3fe26181fea48f0834a01cb88202128] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,task_id,status,decision_time FROM sch_task_approval WHERE is_delete=1 AND (task_id IN (?,?,?))
[DEBUG] [2025-06-20 00:28:30] T:[c3fe26181fea48f0834a01cb88202128] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> ==> Parameters: 1933783331619475456(Long), 1935676707801403392(Long), 1935674439718932480(Long)
[DEBUG] [2025-06-20 00:28:30] T:[c3fe26181fea48f0834a01cb88202128] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> <==      Total: 0
[INFO ] [2025-06-20 00:28:30] T:[c3fe26181fea48f0834a01cb88202128] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-1] ==> 请求完成, url=/admin/app/schTaskInfo/list，elapse=348ms, respData={"data":{"dataList":[{"allowPreemption":1,"approveState":"approved","computeDeviceId":1932730176622301186,"cpuNeed":"2","createTime":1750336265000,"createUserId":1921773426524033024,"dataDeptId":1923195822141345795,"dataUserId":1921773426524033024,"dictId":1932726533772808192,"envConfig":"[{\"value\":\"1\",\"key\":\"test\"}]","graphicNeededMb":1,"id":1935676707801403392,"memoryNeededMb":1,"partitionId":218,"poolId":1932706048758517760,"releasePolicy":1,"resourceId":1932730175653416960,"runCommand":"yum","schComputeDevice":{"createTime":1749633757000,"createUserId":1923195822141345792,"dataDeptId":1787740798154969088,"deviceName":"910B-Ascend-V1","deviceNumber":7,"id":1932730176622301186,"isDelete":1,"memorySize":32768,"modelNumber":"910B-Ascend-V1","resourceId":1932730175653416960,"updateTime":1749633757000},"schResourceInfo":{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749633757000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932730175653416960,"isDelete":1,"loginName":"root","memoryCapacity":"773220.5","password":"Ascend12!@","port":40086,"resourceName":"test007","resourceType":"x86","status":"offline","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749633757000,"updateUserId":1923195822141345792,"usedMemory":"92160.0"},"schResourcePool":{"createTime":1749628005000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"id":1932706048758517760,"isDelete":1,"poolDescription":"资源池是算力调度平台的核心基础模块，汇聚多样算力资源（含CPU、内存、显存等），可动态分配、灵活调度。支持按任务需求快速切分、精准匹配，实时监控资源占用与状态，保障任务高效运行，为算力密集型业务（如模型训练、数据处理 ）提供稳定、弹性的资源供给，助力优化资源利用与业务流程。","poolName":"source-test","updateTime":1749628005000,"updateUserId":1923195822141345792},"schedulingPolicies":1,"status":"failed","taskImageId":1930147594567159808,"taskName":"001","taskPriority":0,"updateTime":1750336286000,"updateUserId":1921773426524033024},{"allowPreemption":1,"approveState":"approved","computeDeviceId":1932725194112110592,"cpuNeed":"1","createTime":1750335724000,"createUserId":1921773426524033024,"dataDeptId":1923195822141345795,"dataUserId":1921773426524033024,"dictId":1932726533772808192,"envConfig":"[]","graphicNeededMb":56,"id":1935674439718932480,"memoryNeededMb":345,"partitionId":103,"poolId":1932711144816906240,"releasePolicy":1,"resourceId":1932725193193558016,"runCommand":"yum install","schComputeDevice":{"createTime":1749632569000,"createUserId":1923195822141345792,"dataDeptId":1787740798154969088,"deviceName":"910B-Ascend-V1","deviceNumber":0,"id":1932725194112110592,"isDelete":1,"memorySize":32768,"modelNumber":"910B-Ascend-V1","resourceId":1932725193193558016,"updateTime":1749632569000},"schResourceInfo":{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749632569000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932725193193558016,"isDelete":1,"loginName":"root","memoryCapacity":"773220.5","password":"Ascend12!@","port":40086,"resourceName":"testHH2","resourceType":"huawei","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749632569000,"updateUserId":1923195822141345792,"usedMemory":"91710.6"},"schResourcePool":{"createTime":1749629220000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"id":1932711144816906240,"isDelete":1,"poolDescription":"wuhu","poolName":"testHH","updateTime":1749629220000,"updateUserId":1923195822141345792},"schedulingPolicies":1,"status":"failed","taskImage":{"createTime":1749917596000,"id":1933920684832985088,"imageName":"python-calculator:v1.0","isDelete":1,"updateTime":1749917596000},"taskImageId":1933920684832985088,"taskName":"任务监控","taskPriority":0,"updateTime":1750336213000,"updateUserId":1921773426524033024},{"allowPreemption":1,"approveState":"approved","computeDeviceId":1932705891413397506,"cpuNeed":"4","createTime":1749884849000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"dictId":1932726533772808192,"envConfig":"[{\"value\":\"1\",\"key\":\"test\"},{\"value\":\"1\",\"key\":\"test\"}]","failReason":"等待时间超过30分钟","graphicNeededMb":24,"id":1933783331619475456,"memoryNeededMb":24,"partition":{"computeDeviceId":1932705891413397506,"createTime":1749631105000,"createUserId":1923195822141345792,"id":1932719052900274176,"isDelete":1,"templateId":1928336030277046272,"updateTime":1749631105000,"updateUserId":1923195822141345792,"vccId":203},"partitionId":1932719052900274176,"poolId":1932711144816906240,"releasePolicy":1,"resourceId":1932705890410958848,"runCommand":"森岛帆高多发点三个地方会更大飞机上代发给","schComputeDevice":{"createTime":1749627967000,"createUserId":1923195822141345792,"dataDeptId":1787740798154969088,"deviceName":"910B-Ascend-V1","deviceNumber":6,"id":1932705891413397506,"isDelete":1,"memorySize":32768,"modelNumber":"910B-Ascend-V1","resourceId":1932705890410958848,"updateTime":1749627967000},"schResourceInfo":{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749627967000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932705890410958848,"isDelete":1,"loginName":"root","memoryCapacity":"773220.5","password":"Ascend12!@","port":40086,"resourceName":"testHH","resourceType":"huawei","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749627967000,"updateUserId":1923195822141345792,"usedMemory":"91517.6"},"schResourcePool":{"$ref":"$.data.dataList[1].schResourcePool"},"schedulingPolicies":1,"status":"failed","taskImageId":1930147594567159808,"taskName":"审核测试","taskPriority":0,"updateTime":1750326833000,"updateUserId":1923195822141345792}],"totalCount":3},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[INFO ] [2025-06-20 00:28:32] T:[89e26c16656f4e07ac3996245ce3a5d3] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> 开始请求，url=/admin/app/schTaskInfo/relationResource, reqData={}
[INFO ] [2025-06-20 00:28:32] T:[f5118e10598b42a2ad82c9e5dbe029fb] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> 开始请求，url=/admin/app/schTaskTemplate/list, reqData={"schTaskTemplateDtoFilter":{},"orderParam":[{"asc":false,"dateAggregateBy":"","fieldName":"createTime"}],"pageParam":{"count":true,"pageNum":1,"pageSize":10}}
[INFO ] [2025-06-20 00:28:32] T:[1e5580cac8d54b0c9e8ad1ab8d052217] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-3] ==> 开始请求，url=/admin/app/schTaskImage/list, reqData={"orderParam":[{"asc":false,"dateAggregateBy":"","fieldName":"createTime"}],"pageParam":{"count":true,"pageNum":1,"pageSize":10},"schTaskImageDtoFilter":{}}
[DEBUG] [2025-06-20 00:28:32] T:[89e26c16656f4e07ac3996245ce3a5d3] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==>  Preparing: SELECT sri.id AS resourceId, sri.resource_name AS resourceName, scd.id AS deviceId, scd.device_name AS deviceName, scd.model_number AS modelNumber, scc.id AS sliceId, scc.vcc_id AS vnpuId FROM sch_resource_info sri JOIN sch_compute_device scd ON sri.id = scd.resource_id JOIN sch_virtual_compute_card_situation scc ON scc.compute_device_id = scd.id WHERE sri.is_delete = 1 AND scd.is_delete = 1 AND scc.is_delete = 1
[DEBUG] [2025-06-20 00:28:32] T:[89e26c16656f4e07ac3996245ce3a5d3] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==> Parameters: 
[DEBUG] [2025-06-20 00:28:32] T:[1e5580cac8d54b0c9e8ad1ab8d052217] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-3] ==> ==>  Preparing: SELECT count(0) FROM sch_task_image WHERE sch_task_image.is_delete = 1
[DEBUG] [2025-06-20 00:28:32] T:[1e5580cac8d54b0c9e8ad1ab8d052217] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-3] ==> ==> Parameters: 
[DEBUG] [2025-06-20 00:28:32] T:[89e26c16656f4e07ac3996245ce3a5d3] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> <==      Total: 19
[DEBUG] [2025-06-20 00:28:32] T:[f5118e10598b42a2ad82c9e5dbe029fb] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT count(0) FROM sch_task_template WHERE sch_task_template.is_delete = 1
[DEBUG] [2025-06-20 00:28:32] T:[89e26c16656f4e07ac3996245ce3a5d3] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==>  Preparing: SELECT sri2.id AS resourceId, sri2.resource_name AS resourceName, srp.id AS poolId, srp.pool_name AS poolName FROM sch_resource_info sri2 JOIN sch_resource_pool_member rmp ON sri2.id = rmp.resource_id JOIN sch_resource_pool srp ON rmp.pool_id = srp.id WHERE sri2.is_delete = 1 AND srp.is_delete = 1
[DEBUG] [2025-06-20 00:28:32] T:[1e5580cac8d54b0c9e8ad1ab8d052217] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-3] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:28:32] T:[f5118e10598b42a2ad82c9e5dbe029fb] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 
[DEBUG] [2025-06-20 00:28:32] T:[89e26c16656f4e07ac3996245ce3a5d3] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==> Parameters: 
[DEBUG] [2025-06-20 00:28:32] T:[1e5580cac8d54b0c9e8ad1ab8d052217] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-3] ==> ==>  Preparing: SELECT * FROM sch_task_image WHERE sch_task_image.is_delete = 1 ORDER BY sch_task_image.create_time DESC LIMIT ?
[DEBUG] [2025-06-20 00:28:32] T:[1e5580cac8d54b0c9e8ad1ab8d052217] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-3] ==> ==> Parameters: 10(Integer)
[DEBUG] [2025-06-20 00:28:32] T:[f5118e10598b42a2ad82c9e5dbe029fb] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:28:32] T:[1e5580cac8d54b0c9e8ad1ab8d052217] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-3] ==> <==      Total: 10
[DEBUG] [2025-06-20 00:28:32] T:[f5118e10598b42a2ad82c9e5dbe029fb] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_task_template WHERE sch_task_template.is_delete = 1 ORDER BY sch_task_template.create_time DESC LIMIT ?
[WARN ] [2025-06-20 00:28:32] T:[1e5580cac8d54b0c9e8ad1ab8d052217] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-3] ==> getGroupedSchTaskImageListWithRelation...businessFile 文件拷贝失败...执行手动拷贝
[WARN ] [2025-06-20 00:28:32] T:[1e5580cac8d54b0c9e8ad1ab8d052217] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-3] ==> getGroupedSchTaskImageListWithRelation...businessFile 文件拷贝失败...执行手动拷贝
[WARN ] [2025-06-20 00:28:32] T:[1e5580cac8d54b0c9e8ad1ab8d052217] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-3] ==> getGroupedSchTaskImageListWithRelation...businessFile 文件拷贝失败...执行手动拷贝
[DEBUG] [2025-06-20 00:28:32] T:[f5118e10598b42a2ad82c9e5dbe029fb] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 10(Integer)
[WARN ] [2025-06-20 00:28:32] T:[1e5580cac8d54b0c9e8ad1ab8d052217] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-3] ==> getGroupedSchTaskImageListWithRelation...businessFile 文件拷贝失败...执行手动拷贝
[WARN ] [2025-06-20 00:28:32] T:[1e5580cac8d54b0c9e8ad1ab8d052217] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-3] ==> getGroupedSchTaskImageListWithRelation...businessFile 文件拷贝失败...执行手动拷贝
[WARN ] [2025-06-20 00:28:32] T:[1e5580cac8d54b0c9e8ad1ab8d052217] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-3] ==> getGroupedSchTaskImageListWithRelation...businessFile 文件拷贝失败...执行手动拷贝
[WARN ] [2025-06-20 00:28:32] T:[1e5580cac8d54b0c9e8ad1ab8d052217] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-3] ==> getGroupedSchTaskImageListWithRelation...businessFile 文件拷贝失败...执行手动拷贝
[WARN ] [2025-06-20 00:28:32] T:[1e5580cac8d54b0c9e8ad1ab8d052217] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-3] ==> getGroupedSchTaskImageListWithRelation...businessFile 文件拷贝失败...执行手动拷贝
[WARN ] [2025-06-20 00:28:32] T:[1e5580cac8d54b0c9e8ad1ab8d052217] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-3] ==> getGroupedSchTaskImageListWithRelation...businessFile 文件拷贝失败...执行手动拷贝
[WARN ] [2025-06-20 00:28:32] T:[1e5580cac8d54b0c9e8ad1ab8d052217] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-3] ==> getGroupedSchTaskImageListWithRelation...businessFile 文件拷贝失败...执行手动拷贝
[DEBUG] [2025-06-20 00:28:32] T:[89e26c16656f4e07ac3996245ce3a5d3] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> <==      Total: 13
[INFO ] [2025-06-20 00:28:32] T:[1e5580cac8d54b0c9e8ad1ab8d052217] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-3] ==> 请求完成, url=/admin/app/schTaskImage/list，elapse=93ms, respData={"data":{"dataList":[{"createTime":1750342742000,"id":1935703876279734272,"imageName":" quay.io/jupyter/base-notebook","updateTime":1750342742000},{"createTime":1750143517000,"id":1934868264496664576,"imageName":"lightglm:v1.0","updateTime":1750143517000},{"createTime":1749976806000,"id":1934169027341455360,"imageName":"http://www.baidu.com","updateTime":1749976806000},{"createTime":1749917596000,"id":1933920684832985088,"imageName":"python-calculator:v1.0","updateTime":1749917596000},{"createTime":1749917573000,"id":1933920588338827264,"imageName":"python-calculator:v1.0","updateTime":1749917573000},{"createTime":1749917562000,"id":1933920540976746496,"imageName":"python-calculator:v1.0","updateTime":1749917562000},{"createTime":1749917496000,"id":1933920262118445056,"imageName":"python-calculator:v1.0","updateTime":1749917496000},{"createTime":1749917392000,"id":1933919825793388544,"imageName":"python-calculator:v1.0","updateTime":1749917392000},{"createTime":1749917389000,"id":1933919813537632256,"imageName":"python-calculator:v1.0","updateTime":1749917389000},{"createTime":1749917356000,"id":1933919674957828096,"imageName":"python-calculator:v1.0","updateTime":1749917356000}],"totalCount":32},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[DEBUG] [2025-06-20 00:28:32] T:[89e26c16656f4e07ac3996245ce3a5d3] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==>  Preparing: SELECT id,exit_code,fail_reason,scale_plan_id,dict_id,container_name,container_status,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,task_name,status,task_priority,graphic_needed_mb,memory_needed_mb,cpu_need,pool_id,resource_id,task_image_id,compute_device_id,partition_id,run_command,env_config,release_policy,start_time,end_tiem,estimat_time,scheduling_policies,approve_state,allow_preemption FROM sch_task_info WHERE is_delete=1 AND (partition_id IS NOT NULL)
[DEBUG] [2025-06-20 00:28:32] T:[89e26c16656f4e07ac3996245ce3a5d3] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==> Parameters: 
[DEBUG] [2025-06-20 00:28:32] T:[f5118e10598b42a2ad82c9e5dbe029fb] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 8
[DEBUG] [2025-06-20 00:28:32] T:[f5118e10598b42a2ad82c9e5dbe029fb] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,image_name,image_desc,image_url,image_version FROM sch_task_image WHERE is_delete=1 AND (id IN (?,?,?))
[DEBUG] [2025-06-20 00:28:32] T:[f5118e10598b42a2ad82c9e5dbe029fb] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1927994908065402880(Long), 1930147594567159808(Long), 1925802254888079360(Long)
[DEBUG] [2025-06-20 00:28:32] T:[89e26c16656f4e07ac3996245ce3a5d3] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> <==      Total: 14
[INFO ] [2025-06-20 00:28:32] T:[89e26c16656f4e07ac3996245ce3a5d3] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> 请求完成, url=/admin/app/schTaskInfo/relationResource，elapse=141ms, respData={"body":{"data":[{"childrenResource":[{"childrenDevice":[{"childrenSlice":[{"schTaskInfoList":[{"approveState":"approved","computeDeviceId":1932725194112110592,"containerName":"1935318863025541120-remotetask-1","containerStatus":"running","createTime":1750250948000,"dictId":1933758313216872448,"failReason":"java.lang.NullPointerException: Cannot invoke \"com.alibaba.fastjson.JSONArray.toJavaList(java.lang.Class)\" because \"objects\" is null","graphicNeededMb":169,"id":1935318863025541120,"isDelete":1,"memoryNeededMb":167,"partitionId":103,"poolId":1932711144816906240,"resourceId":1932725193193558016,"status":"failed","taskImageId":1933866164367134720,"taskName":"jupyter-test-2","updateTime":1750250953000},{"allowPreemption":1,"approveState":"approved","computeDeviceId":1932725194112110592,"cpuNeed":"1","createTime":1750335724000,"createUserId":1921773426524033024,"dataDeptId":1923195822141345795,"dataUserId":1921773426524033024,"dictId":1932726533772808192,"envConfig":"[]","graphicNeededMb":56,"id":1935674439718932480,"isDelete":1,"memoryNeededMb":345,"partitionId":103,"poolId":1932711144816906240,"releasePolicy":1,"resourceId":1932725193193558016,"runCommand":"yum install","schedulingPolicies":1,"status":"failed","taskImageId":1933920684832985088,"taskName":"任务监控","taskPriority":0,"updateTime":1750336213000,"updateUserId":1921773426524033024}],"sliceId":1934420497013936128,"vnpuId":103},{"schTaskInfoList":[{"approveState":"approved","computeDeviceId":1932725194112110592,"createTime":1750241054000,"dictId":1933758313216872448,"envConfig":"{\"TZ\":\"Asia/Shanghai\",\"ASCEND_VERSION\":\"ascend910\"}","failReason":"supie.common.core.exception.MyRuntimeException: 命令执行失败: yaml: line 2: did not find expected key","graphicNeededMb":169,"id":1935277364615319552,"isDelete":1,"memoryNeededMb":167,"partitionId":104,"poolId":1932711144816906240,"resourceId":1932725193193558016,"runCommand":"\"[\\\"bin/bash\\\",\\\"-c\\\",\\\"python train.py\\\"]\"","status":"failed","taskImageId":1933866164367134720,"taskName":"推理训练任务","updateTime":1750241056000}],"sliceId":1934431089007267840,"vnpuId":104}],"deviceId":1932725194112110592,"deviceName":"910B-Ascend-V1"}],"resourceId":1932725193193558016,"resourceName":"testHH2"},{"childrenDevice":[{"childrenSlice":[{"schTaskInfoList":[{"approveState":"approved","computeDeviceId":1932705891413397506,"containerName":"1935320370617782272-remotetask-1","containerStatus":"running","createTime":1750251308000,"dictId":1933758313216872448,"graphicNeededMb":169,"id":1935320370617782272,"isDelete":1,"memoryNeededMb":167,"partitionId":203,"poolId":1932711144816906240,"resourceId":1932705890410958848,"status":"running","taskImageId":1933866164367134720,"taskName":"jupyter-test-2","updateTime":1750251337000}],"sliceId":1932719052900274176,"vnpuId":203}],"deviceId":1932705891413397506,"deviceName":"910B-Ascend-V1"},{"childrenSlice":[{"sliceId":1932720849522003968,"vnpuId":213}],"deviceId":1932705891413397507,"deviceName":"910B-Ascend-V1"},{"childrenSlice":[{"sliceId":1932720574073671680,"vnpuId":167}],"deviceId":1932705891413397504,"deviceName":"910B-Ascend-V1"}],"resourceId":1932705890410958848,"resourceName":"testHH"}],"poolId":1932711144816906240,"poolName":"testHH"},{"childrenResource":[{"childrenDevice":[],"resourceId":1932700819912658944,"resourceName":"test"}],"poolId":1932254698274820096,"poolName":"资源池1002"},{"childrenResource":[{"childrenDevice":[],"resourceId":1932729802632990720,"resourceName":"test005"},{"childrenDevice":[{"childrenSlice":[{"sliceId":1932711294897491968,"vnpuId":202}],"deviceId":1932709586645553155,"deviceName":"910B-Ascend-V1"}],"resourceId":1932709585647308800,"resourceName":"test002"},{"childrenDevice":[{"childrenSlice":[{"sliceId":1935735795411324928,"vnpuId":106}],"deviceId":1933063902719905792,"deviceName":"910B-Ascend-V1"},{"childrenSlice":[{"sliceId":1935721095680233472,"vnpuId":148}],"deviceId":1933063902724100096,"deviceName":"910B-Ascend-V1"}],"resourceId":1933063901780381696,"resourceName":"test"},{"childrenDevice":[{"childrenSlice":[{"schTaskInfoList":[{"allowPreemption":1,"approveState":"approved","computeDeviceId":1932730176622301186,"cpuNeed":"2","createTime":1750336265000,"createUserId":1921773426524033024,"dataDeptId":1923195822141345795,"dataUserId":1921773426524033024,"dictId":1932726533772808192,"envConfig":"[{\"value\":\"1\",\"key\":\"test\"}]","graphicNeededMb":1,"id":1935676707801403392,"isDelete":1,"memoryNeededMb":1,"partitionId":218,"poolId":1932706048758517760,"releasePolicy":1,"resourceId":1932730175653416960,"runCommand":"yum","schedulingPolicies":1,"status":"failed","taskImageId":1930147594567159808,"taskName":"001","taskPriority":0,"updateTime":1750336286000,"updateUserId":1921773426524033024}],"sliceId":1932736168579829760,"vnpuId":218}],"deviceId":1932730176622301186,"deviceName":"910B-Ascend-V1"}],"resourceId":1932730175653416960,"resourceName":"test007"},{"childrenDevice":[{"childrenSlice":[{"sliceId":1932722443105865728,"vnpuId":214},{"sliceId":1932722542930300928,"vnpuId":215},{"sliceId":1932722782693494784,"vnpuId":216},{"sliceId":1932723023631093760,"vnpuId":217}],"deviceId":1932717670214406144,"deviceName":"910B-Ascend-V1"}],"resourceId":1932717669144858624,"resourceName":"test003"},{"childrenDevice":[],"resourceId":1932729975991963648,"resourceName":"test006"}],"poolId":1932706048758517760,"poolName":"source-test"},{"childrenResource":[{"childrenDevice":[],"resourceId":1932722934955118592,"resourceName":"222"},{"childrenDevice":[{"childrenSlice":[{"schTaskInfoList":[{"$ref":"$.body.data[0].childrenResource[1].childrenDevice[0].childrenSlice[0].schTaskInfoList[0]"}],"sliceId":1932719052900274176,"vnpuId":203}],"deviceId":1932705891413397506,"deviceName":"910B-Ascend-V1"},{"childrenSlice":[{"sliceId":1932720849522003968,"vnpuId":213}],"deviceId":1932705891413397507,"deviceName":"910B-Ascend-V1"},{"childrenSlice":[{"sliceId":1932720574073671680,"vnpuId":167}],"deviceId":1932705891413397504,"deviceName":"910B-Ascend-V1"}],"resourceId":1932705890410958848,"resourceName":"testHH"}],"poolId":1930925656795779072,"poolName":"test"},{"childrenResource":[{"childrenDevice":[{"childrenSlice":[{"sliceId":1935692277137543168,"vnpuId":105}],"deviceId":1933048813337972736,"deviceName":"910B-Ascend-V1"}],"resourceId":1933048812436197376,"resourceName":"资源1001"},{"childrenDevice":[{"childrenSlice":[{"schTaskInfoList":[{"approveState":"approved","computeDeviceId":1932704140606050304,"containerName":"1935278500109881344-remotetask-1","containerStatus":"running","createTime":1750241325000,"dictId":1933758313216872448,"failReason":"java.lang.NullPointerException: Cannot invoke \"com.alibaba.fastjson.JSONArray.toJavaList(java.lang.Class)\" because \"objects\" is null","graphicNeededMb":169,"id":1935278500109881344,"isDelete":1,"memoryNeededMb":167,"partitionId":212,"poolId":1928361548711989248,"resourceId":1932704139536502784,"status":"failed","taskImageId":1933866164367134720,"taskName":"jupyter-test","updateTime":1750241329000}],"sliceId":1932704489823801344,"vnpuId":212}],"deviceId":1932704140606050304,"deviceName":"910B-Ascend-V1"}],"resourceId":1932704139536502784,"resourceName":"test001"}],"poolId":1928361548711989248,"poolName":"池1001"}],"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":[{"$ref":"$.body.data[0]"},{"$ref":"$.body.data[1]"},{"$ref":"$.body.data[2]"},{"$ref":"$.body.data[3]"},{"$ref":"$.body.data[4]"}],"success":true},"headers":{"Content-Type":["application/json"]},"statusCode":"OK","statusCodeValue":200}
[DEBUG] [2025-06-20 00:28:32] T:[f5118e10598b42a2ad82c9e5dbe029fb] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 0
[INFO ] [2025-06-20 00:28:32] T:[f5118e10598b42a2ad82c9e5dbe029fb] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> 请求完成, url=/admin/app/schTaskTemplate/list，elapse=178ms, respData={"data":{"dataList":[{"createTime":1750325730000,"createUserId":1921773426524033024,"dataDeptId":1923195822141345795,"dataUserId":1921773426524033024,"envConfig":"[]","id":1935632519462391808,"imageId":1930147594567159808,"runCommand":"","templateDesc":"用于测试","templateName":"测试-副本","updateTime":1750325730000,"updateUserId":1921773426524033024},{"createTime":1749695447000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"envConfig":"[{\"value\":\"1\",\"key\":\"test\"},{\"value\":\"1\",\"key\":\"test\"}]","id":1932988922334285824,"imageId":1930147594567159808,"runCommand":"","templateDesc":"","templateName":"测试002","updateTime":1749695447000,"updateUserId":1923195822141345792},{"createTime":1749633373000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"envConfig":"[{\"value\":\"1\",\"key\":\"test\"}]","id":1932728567368519680,"imageId":1930147594567159808,"runCommand":"yum","templateDesc":"任务模板是算力调度平台中用于快速创建任务的标准化配置集合。它集成预设的资源需求（如显存、内存、CPU 核数等）、执行流程及镜像信息，支持一键复用。通过任务模板，可规范任务参数、简化创建流程，适配模型训练、数据处理等多样场景，助力用户高效调度算力资源，提升任务部署的一致性与便捷性 。","templateName":"测试001-副本","updateTime":1749633373000,"updateUserId":1923195822141345792},{"createTime":1749632944000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"envConfig":"[{\"value\":\"1\",\"key\":\"test\"}]","id":1932726765583601664,"imageId":1930147594567159808,"runCommand":"yum","templateDesc":"任务模板是算力调度平台中用于快速创建任务的标准化配置集合。它集成预设的资源需求（如显存、内存、CPU 核数等）、执行流程及镜像信息，支持一键复用。通过任务模板，可规范任务参数、简化创建流程，适配模型训练、数据处理等多样场景，助力用户高效调度算力资源，提升任务部署的一致性与便捷性 。","templateName":"测试001","updateTime":1749632944000,"updateUserId":1921773426524033024},{"createTime":1749628725000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"envConfig":"[]","id":1932709069190074368,"imageId":1930147594567159808,"runCommand":"","templateDesc":"用于测试","templateName":"测试","updateTime":1749628725000,"updateUserId":1923195822141345792},{"createTime":1749435475000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"envConfig":"[{\"value\":\"1\",\"key\":\"test\"}]","id":1931898521351360512,"imageId":1927994908065402880,"runCommand":"","templateDesc":"test","templateName":"test","updateTime":1749435475000,"updateUserId":1923195822141345792},{"createTime":1747984152000,"createUserId":1923195822141345792,"envConfig":"[{\"value\":\"d\",\"key\":\"gg\"}]","id":1925811230891577344,"imageId":1925802254888079360,"runCommand":"yh","templateDesc":"烦烦烦烦烦烦","templateName":"水水水水水水水水","updateTime":1747984152000,"updateUserId":0},{"createTime":1747984057000,"createUserId":1923195822141345792,"envConfig":"[{\"value\":\"s\",\"key\":\"fff\"}]","id":1925810831442841600,"imageId":1925802254888079360,"runCommand":"pip","templateDesc":"撒打发","templateName":"啊撒打发","updateTime":1747984057000,"updateUserId":0}],"totalCount":8},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[INFO ] [2025-06-20 00:28:32] T:[75be2e7e9065488ba1770be071d7b50d] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> 开始请求，url=/admin/app/schBusinessDict/list, reqData={"orderParam":[{"asc":false,"dateAggregateBy":"","fieldName":"createTime"}],"schBusinessDictDtoFilter":{"bindType":"TaskMonitor"},"pageParam":{"count":true,"pageNum":1,"pageSize":10}}
[DEBUG] [2025-06-20 00:28:32] T:[75be2e7e9065488ba1770be071d7b50d] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> ==>  Preparing: SELECT count(0) FROM sch_business_dict WHERE sch_business_dict.bind_type LIKE ? AND sch_business_dict.is_delete = 1
[DEBUG] [2025-06-20 00:28:32] T:[75be2e7e9065488ba1770be071d7b50d] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> ==> Parameters: %TaskMonitor%(String)
[INFO ] [2025-06-20 00:28:32] T:[1f642869e28443bea2a3c1fb4afd4595] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> 开始请求，url=/admin/app/schStorageVolume/list, reqData={"orderParam":[{"asc":false,"dateAggregateBy":"","fieldName":"createTime"}],"schStorageVolumeDtoFilter":{}}
[DEBUG] [2025-06-20 00:28:32] T:[1f642869e28443bea2a3c1fb4afd4595] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==>  Preparing: SELECT * FROM sch_storage_volume WHERE sch_storage_volume.is_delete = 1 ORDER BY sch_storage_volume.create_time DESC
[DEBUG] [2025-06-20 00:28:32] T:[1f642869e28443bea2a3c1fb4afd4595] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> ==> Parameters: 
[DEBUG] [2025-06-20 00:28:32] T:[75be2e7e9065488ba1770be071d7b50d] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:28:32] T:[75be2e7e9065488ba1770be071d7b50d] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> ==>  Preparing: SELECT * FROM sch_business_dict WHERE sch_business_dict.bind_type LIKE ? AND sch_business_dict.is_delete = 1 ORDER BY sch_business_dict.create_time DESC LIMIT ?
[DEBUG] [2025-06-20 00:28:32] T:[75be2e7e9065488ba1770be071d7b50d] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> ==> Parameters: %TaskMonitor%(String), 10(Integer)
[DEBUG] [2025-06-20 00:28:32] T:[1f642869e28443bea2a3c1fb4afd4595] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> <==      Total: 1
[INFO ] [2025-06-20 00:28:32] T:[1f642869e28443bea2a3c1fb4afd4595] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-4] ==> 请求完成, url=/admin/app/schStorageVolume/list，elapse=41ms, respData={"data":{"dataList":[{"createTime":1749895453000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"id":1933827806874046464,"updateTime":1749895453000,"updateUserId":1923195822141345792,"volumeName":"存储测试名称","volumePath":"asdf/asg/sdfqw/cbdfg/asdfqwwt/ssf","volumeType":"local"}],"totalCount":0},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[DEBUG] [2025-06-20 00:28:33] T:[75be2e7e9065488ba1770be071d7b50d] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> <==      Total: 5
[INFO ] [2025-06-20 00:28:33] T:[75be2e7e9065488ba1770be071d7b50d] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> 请求完成, url=/admin/app/schBusinessDict/list，elapse=182ms, respData={"data":{"dataList":[{"bindType":"TaskMonitor","colorData":"","createTime":1749632889000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"dictDescription":"","dictLevel":0,"dictName":"测试任务HH","id":1932726533772808192,"showOrder":0,"strId":"88a0d36e3034136a6be1c29502290286","updateTime":1749632889000,"updateUserId":1923195822141345792},{"bindType":"TaskMonitor","colorData":"","createTime":1749632636000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"dictDescription":"","dictLevel":0,"dictName":"测试任务@","id":1932725472521621504,"showOrder":0,"strId":"c8dbbbbb821f052abeaa616b5a83e2fb","updateTime":1749632636000,"updateUserId":1923195822141345792},{"bindType":"TaskMonitor","colorData":"","createTime":1749460618000,"createUserId":1923195822141345792,"dictDescription":"","dictLevel":0,"dictName":"测试任务321","id":***********45006592,"showOrder":0,"strId":"d26b2ecf9311ea9ddb51717ef281be5b","updateTime":1749460618000,"updateUserId":1923195822141345792},{"bindType":"TaskMonitor","colorData":"","createTime":1749460600000,"createUserId":1923195822141345792,"dataDeptId":0,"dataUserId":0,"dictDescription":"","dictLevel":0,"dictName":"测试任务haha","id":1932003904598315008,"showOrder":0,"strId":"391223d895fd9155f61e0834da7228b5","updateTime":1749522525000,"updateUserId":1923195822141345792},{"bindType":"TaskMonitor","colorData":"","createTime":1749457922000,"createUserId":1923195822141345792,"dataDeptId":0,"dataUserId":0,"dictDescription":"","dictLevel":0,"dictName":"测试任务456-test","id":1931992669626568704,"showOrder":0,"strId":"86dfa84fcccd1f7484eb59369638e1e0","updateTime":1749632612000,"updateUserId":1923195822141345792}],"totalCount":5},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[INFO ] [2025-06-20 00:28:56] T:[1af7b4d0db9a4c7c83b66a7f04c76f62] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-5] ==> 开始请求，url=/admin/app/schVirtualComputeCardSituation/view, reqData={"id":1932704489823801344}
[DEBUG] [2025-06-20 00:28:56] T:[1af7b4d0db9a4c7c83b66a7f04c76f62] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-5] ==> ==>  Preparing: SELECT id,str_id,is_delete,create_user_id,create_time,update_user_id,update_time,data_user_id,data_dept_id,compute_device_id,template_id,vcc_id,vgroup_id FROM sch_virtual_compute_card_situation WHERE id=? AND is_delete=1
[DEBUG] [2025-06-20 00:28:56] T:[1af7b4d0db9a4c7c83b66a7f04c76f62] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-5] ==> ==> Parameters: 1932704489823801344(Long)
[DEBUG] [2025-06-20 00:28:56] T:[1af7b4d0db9a4c7c83b66a7f04c76f62] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-5] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:28:56] T:[1af7b4d0db9a4c7c83b66a7f04c76f62] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-5] ==> ==>  Preparing: SELECT id,str_id,is_delete,create_user_id,create_time,update_user_id,update_time,data_user_id,data_dept_id,product_type,example,ai_core_number,memory,ai_cpu,vpc,vdec,jpegd FROM sch_virtual_compute_card_template WHERE id=? AND is_delete=1
[DEBUG] [2025-06-20 00:28:56] T:[1af7b4d0db9a4c7c83b66a7f04c76f62] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-5] ==> ==> Parameters: 1928336210011361280(Long)
[DEBUG] [2025-06-20 00:28:56] T:[1af7b4d0db9a4c7c83b66a7f04c76f62] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-5] ==> <==      Total: 1
[INFO ] [2025-06-20 00:28:56] T:[1af7b4d0db9a4c7c83b66a7f04c76f62] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-5] ==> 请求完成, url=/admin/app/schVirtualComputeCardSituation/view，elapse=94ms, respData={"data":{"computeDeviceId":1932704140606050304,"createTime":1749627633000,"createUserId":1923195822141345792,"id":1932704489823801344,"schVirtualComputeCardTemplate":{"aiCoreNumber":2,"aiCpu":1,"createTime":1748586154000,"createUserId":1923195822141345792,"example":"vir02","id":1928336210011361280,"isDelete":1,"jpegd":1,"memory":2,"productType":1,"updateTime":1748586154000,"updateUserId":1923195822141345792,"vdec":1,"vpc":1},"templateId":1928336210011361280,"updateTime":1749627633000,"updateUserId":1923195822141345792,"vccId":212},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[INFO ] [2025-06-20 00:29:09] T:[252656dfc09b4e9db80e1b7ef51aa0fe] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> 开始请求，url=/admin/app/schTaskInfo/add, reqData={"schTaskInfoDto":{"allowPreemption":1,"approveState":"","computeDeviceId":1932704140606050304,"cpuNeed":"","createUserId":1921773426524033024,"dataDeptId":1923195822141345795,"dataUserId":1921773426524033024,"dictId":1932726533772808192,"envConfig":"[{\"value\":\"1\",\"key\":\"test\"},{\"value\":\"1\",\"key\":\"test\"}]","graphicNeededMb":0,"memoryNeededMb":0,"partitionId":212,"poolId":1928361548711989248,"releasePolicy":1,"resourceId":1932704139536502784,"runCommand":"run ","schedulingPolicies":1,"status":"pending","taskImageId":1930147594567159808,"taskName":"任务1001","taskPriority":10,"updateUserId":1921773426524033024}}
[DEBUG] [2025-06-20 00:29:09] T:[252656dfc09b4e9db80e1b7ef51aa0fe] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==>  Preparing: INSERT INTO sch_task_approval ( id, update_time, create_time, create_user_id, update_user_id, data_user_id, data_dept_id, is_delete, status ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ? )
[DEBUG] [2025-06-20 00:29:09] T:[252656dfc09b4e9db80e1b7ef51aa0fe] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==> Parameters: 1935736620707745792(Long), 2025-06-20 00:29:09.281(Timestamp), 2025-06-20 00:29:09.281(Timestamp), 1921773426524033024(Long), 1921773426524033024(Long), 1921773426524033024(Long), 1923195822141345795(Long), 1(Integer), pending(String)
[DEBUG] [2025-06-20 00:29:09] T:[252656dfc09b4e9db80e1b7ef51aa0fe] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> <==    Updates: 1
[DEBUG] [2025-06-20 00:29:09] T:[252656dfc09b4e9db80e1b7ef51aa0fe] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==>  Preparing: INSERT INTO sch_task_info ( id, dict_id, update_time, create_time, create_user_id, update_user_id, data_user_id, data_dept_id, is_delete, task_name, status, task_priority, graphic_needed_mb, memory_needed_mb, cpu_need, pool_id, resource_id, task_image_id, compute_device_id, partition_id, run_command, env_config, release_policy, scheduling_policies, approve_state, allow_preemption ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[DEBUG] [2025-06-20 00:29:09] T:[252656dfc09b4e9db80e1b7ef51aa0fe] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> ==> Parameters: 1935736621068455936(Long), 1932726533772808192(Long), 2025-06-20 00:29:09.367(Timestamp), 2025-06-20 00:29:09.367(Timestamp), 1921773426524033024(Long), 1921773426524033024(Long), 1921773426524033024(Long), 1923195822141345795(Long), 1(Integer), 任务1001(String), pending(String), 10(Integer), 0(Integer), 0(Integer), (String), 1928361548711989248(Long), 1932704139536502784(Long), 1930147594567159808(Long), 1932704140606050304(Long), 212(Long), run (String), [{"value":"1","key":"test"},{"value":"1","key":"test"}](String), 1(Integer), 1(Integer), pending(String), 1(Integer)
[DEBUG] [2025-06-20 00:29:09] T:[252656dfc09b4e9db80e1b7ef51aa0fe] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> <==    Updates: 1
[INFO ] [2025-06-20 00:29:09] T:[252656dfc09b4e9db80e1b7ef51aa0fe] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-8] ==> 请求完成, url=/admin/app/schTaskInfo/add，elapse=367ms, respData={"data":1935736621068455936,"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":1935736621068455936,"success":true}
[DEBUG] [2025-06-20 00:29:09] T:[] S:[] U:[] [flowable-task-Executor-7] ==> ==>  Preparing: INSERT INTO zz_sys_operation_log ( log_id, description, operation_type, service_name, api_class, api_method, session_id, trace_id, elapse, request_method, request_url, request_arguments, response_result, request_ip, success, operator_id, operator_name, operation_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[DEBUG] [2025-06-20 00:29:09] T:[] S:[] U:[] [flowable-task-Executor-7] ==> ==> Parameters: 1935736620439310336(Long), (String), 10(Integer), application-webadmin(String), supie.webadmin.app.controller.SchTaskInfoController(String), supie.webadmin.app.controller.SchTaskInfoController.add(String), Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39(String), 252656dfc09b4e9db80e1b7ef51aa0fe(String), 367(Long), POST(String), /admin/app/schTaskInfo/add(String), {"schTaskInfoDto":{"allowPreemption":1,"approveState":"","computeDeviceId":1932704140606050304,"cpuNeed":"","createUserId":1921773426524033024,"dataDeptId":1923195822141345795,"dataUserId":1921773426524033024,"dictId":1932726533772808192,"envConfig":"[{\"value\":\"1\",\"key\":\"test\"},{\"value\":\"1\",\"key\":\"test\"}]","graphicNeededMb":0,"memoryNeededMb":0,"partitionId":212,"poolId":1928361548711989248,"releasePolicy":1,"resourceId":1932704139536502784,"runCommand":"run ","schedulingPolicies":1,"status":"pending","taskImageId":1930147594567159808,"taskName":"任务1001","taskPriority":10,"updateUserId":1921773426524033024}}(String), {"data":1935736621068455936,"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":1935736621068455936,"success":true}(String), ************(String), true(Boolean), 1921773426524033024(Long), admin(String), 2025-06-20 00:29:09.217(Timestamp)
[DEBUG] [2025-06-20 00:29:09] T:[] S:[] U:[] [flowable-task-Executor-7] ==> <==    Updates: 1
[INFO ] [2025-06-20 00:29:10] T:[5d31a23ec2734404b130a97d2f10e703] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> 开始请求，url=/admin/app/schTaskInfo/list, reqData={"orderParam":[{"asc":false,"dateAggregateBy":"","fieldName":"createTime"}],"schTaskInfoDtoFilter":{"dictId":1932726533772808192},"pageParam":{"count":true,"pageNum":1,"pageSize":10}}
[DEBUG] [2025-06-20 00:29:10] T:[5d31a23ec2734404b130a97d2f10e703] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==>  Preparing: SELECT count(0) FROM sch_task_info WHERE sch_task_info.dict_id = ? AND sch_task_info.is_delete = 1
[DEBUG] [2025-06-20 00:29:10] T:[5d31a23ec2734404b130a97d2f10e703] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==> Parameters: 1932726533772808192(Long)
[DEBUG] [2025-06-20 00:29:10] T:[5d31a23ec2734404b130a97d2f10e703] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:29:10] T:[5d31a23ec2734404b130a97d2f10e703] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==>  Preparing: SELECT * FROM sch_task_info WHERE sch_task_info.dict_id = ? AND sch_task_info.is_delete = 1 ORDER BY sch_task_info.create_time DESC LIMIT ?
[DEBUG] [2025-06-20 00:29:10] T:[5d31a23ec2734404b130a97d2f10e703] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==> Parameters: 1932726533772808192(Long), 10(Integer)
[DEBUG] [2025-06-20 00:29:10] T:[5d31a23ec2734404b130a97d2f10e703] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> <==      Total: 4
[DEBUG] [2025-06-20 00:29:10] T:[5d31a23ec2734404b130a97d2f10e703] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,image_name,image_desc,image_url,image_version FROM sch_task_image WHERE is_delete=1 AND (id IN (?,?))
[DEBUG] [2025-06-20 00:29:10] T:[5d31a23ec2734404b130a97d2f10e703] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==> Parameters: 1930147594567159808(Long), 1933920684832985088(Long)
[DEBUG] [2025-06-20 00:29:10] T:[5d31a23ec2734404b130a97d2f10e703] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:29:10] T:[5d31a23ec2734404b130a97d2f10e703] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,pool_name,pool_description FROM sch_resource_pool WHERE is_delete=1 AND (id IN (?,?,?))
[DEBUG] [2025-06-20 00:29:10] T:[5d31a23ec2734404b130a97d2f10e703] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==> Parameters: 1932711144816906240(Long), 1932706048758517760(Long), 1928361548711989248(Long)
[DEBUG] [2025-06-20 00:29:10] T:[5d31a23ec2734404b130a97d2f10e703] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> <==      Total: 3
[DEBUG] [2025-06-20 00:29:10] T:[5d31a23ec2734404b130a97d2f10e703] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE is_delete=1 AND (id IN (?,?,?,?))
[DEBUG] [2025-06-20 00:29:10] T:[5d31a23ec2734404b130a97d2f10e703] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==> Parameters: 1932725193193558016(Long), 1932705890410958848(Long), 1932730175653416960(Long), 1932704139536502784(Long)
[DEBUG] [2025-06-20 00:29:10] T:[5d31a23ec2734404b130a97d2f10e703] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> <==      Total: 4
[DEBUG] [2025-06-20 00:29:10] T:[5d31a23ec2734404b130a97d2f10e703] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (id IN (?,?,?,?))
[DEBUG] [2025-06-20 00:29:10] T:[5d31a23ec2734404b130a97d2f10e703] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==> Parameters: 1932725194112110592(Long), 1932705891413397506(Long), 1932730176622301186(Long), 1932704140606050304(Long)
[DEBUG] [2025-06-20 00:29:10] T:[5d31a23ec2734404b130a97d2f10e703] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> <==      Total: 4
[DEBUG] [2025-06-20 00:29:10] T:[5d31a23ec2734404b130a97d2f10e703] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==>  Preparing: SELECT id,str_id,is_delete,create_user_id,create_time,update_user_id,update_time,data_user_id,data_dept_id,compute_device_id,template_id,vcc_id,vgroup_id FROM sch_virtual_compute_card_situation WHERE is_delete=1 AND (id IN (?,?,?,?))
[DEBUG] [2025-06-20 00:29:10] T:[5d31a23ec2734404b130a97d2f10e703] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==> Parameters: 212(Long), 103(Long), 218(Long), 1932719052900274176(Long)
[DEBUG] [2025-06-20 00:29:10] T:[5d31a23ec2734404b130a97d2f10e703] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:29:10] T:[5d31a23ec2734404b130a97d2f10e703] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,task_id,status,decision_time FROM sch_task_approval WHERE is_delete=1 AND (task_id IN (?,?,?,?))
[DEBUG] [2025-06-20 00:29:10] T:[5d31a23ec2734404b130a97d2f10e703] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==> Parameters: 1933783331619475456(Long), 1935676707801403392(Long), 1935674439718932480(Long), 1935736621068455936(Long)
[DEBUG] [2025-06-20 00:29:10] T:[5d31a23ec2734404b130a97d2f10e703] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> <==      Total: 0
[INFO ] [2025-06-20 00:29:10] T:[5d31a23ec2734404b130a97d2f10e703] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> 请求完成, url=/admin/app/schTaskInfo/list，elapse=446ms, respData={"data":{"dataList":[{"allowPreemption":1,"approveState":"pending","computeDeviceId":1932704140606050304,"cpuNeed":"","createTime":1750350549000,"createUserId":1921773426524033024,"dataDeptId":1923195822141345795,"dataUserId":1921773426524033024,"dictId":1932726533772808192,"envConfig":"[{\"value\":\"1\",\"key\":\"test\"},{\"value\":\"1\",\"key\":\"test\"}]","graphicNeededMb":0,"id":1935736621068455936,"memoryNeededMb":0,"partitionId":212,"poolId":1928361548711989248,"releasePolicy":1,"resourceId":1932704139536502784,"runCommand":"run ","schComputeDevice":{"createTime":1749627550000,"createUserId":1923195822141345792,"dataDeptId":1787740798154969088,"deviceName":"910B-Ascend-V1","deviceNumber":7,"id":1932704140606050304,"isDelete":1,"memorySize":32768,"modelNumber":"910B-Ascend-V1","resourceId":1932704139536502784,"updateTime":1749627550000},"schResourceInfo":{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749627549000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932704139536502784,"isDelete":1,"loginName":"root","memoryCapacity":"773220.5","password":"Ascend12!@","port":40086,"resourceName":"test001","resourceType":"huawei","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749627549000,"updateUserId":1923195822141345792,"usedMemory":"90642.6"},"schResourcePool":{"createTime":1748592195000,"createUserId":1923195822141345792,"id":1928361548711989248,"isDelete":1,"poolDescription":"测试池1001","poolName":"池1001","updateTime":1748592195000,"updateUserId":1923195822141345792},"schedulingPolicies":1,"status":"pending","taskImageId":1930147594567159808,"taskName":"任务1001","taskPriority":10,"updateTime":1750350549000,"updateUserId":1921773426524033024},{"allowPreemption":1,"approveState":"approved","computeDeviceId":1932730176622301186,"cpuNeed":"2","createTime":1750336265000,"createUserId":1921773426524033024,"dataDeptId":1923195822141345795,"dataUserId":1921773426524033024,"dictId":1932726533772808192,"envConfig":"[{\"value\":\"1\",\"key\":\"test\"}]","graphicNeededMb":1,"id":1935676707801403392,"memoryNeededMb":1,"partitionId":218,"poolId":1932706048758517760,"releasePolicy":1,"resourceId":1932730175653416960,"runCommand":"yum","schComputeDevice":{"createTime":1749633757000,"createUserId":1923195822141345792,"dataDeptId":1787740798154969088,"deviceName":"910B-Ascend-V1","deviceNumber":7,"id":1932730176622301186,"isDelete":1,"memorySize":32768,"modelNumber":"910B-Ascend-V1","resourceId":1932730175653416960,"updateTime":1749633757000},"schResourceInfo":{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749633757000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932730175653416960,"isDelete":1,"loginName":"root","memoryCapacity":"773220.5","password":"Ascend12!@","port":40086,"resourceName":"test007","resourceType":"x86","status":"offline","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749633757000,"updateUserId":1923195822141345792,"usedMemory":"92160.0"},"schResourcePool":{"createTime":1749628005000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"id":1932706048758517760,"isDelete":1,"poolDescription":"资源池是算力调度平台的核心基础模块，汇聚多样算力资源（含CPU、内存、显存等），可动态分配、灵活调度。支持按任务需求快速切分、精准匹配，实时监控资源占用与状态，保障任务高效运行，为算力密集型业务（如模型训练、数据处理 ）提供稳定、弹性的资源供给，助力优化资源利用与业务流程。","poolName":"source-test","updateTime":1749628005000,"updateUserId":1923195822141345792},"schedulingPolicies":1,"status":"failed","taskImageId":1930147594567159808,"taskName":"001","taskPriority":0,"updateTime":1750336286000,"updateUserId":1921773426524033024},{"allowPreemption":1,"approveState":"approved","computeDeviceId":1932725194112110592,"cpuNeed":"1","createTime":1750335724000,"createUserId":1921773426524033024,"dataDeptId":1923195822141345795,"dataUserId":1921773426524033024,"dictId":1932726533772808192,"envConfig":"[]","graphicNeededMb":56,"id":1935674439718932480,"memoryNeededMb":345,"partitionId":103,"poolId":1932711144816906240,"releasePolicy":1,"resourceId":1932725193193558016,"runCommand":"yum install","schComputeDevice":{"createTime":1749632569000,"createUserId":1923195822141345792,"dataDeptId":1787740798154969088,"deviceName":"910B-Ascend-V1","deviceNumber":0,"id":1932725194112110592,"isDelete":1,"memorySize":32768,"modelNumber":"910B-Ascend-V1","resourceId":1932725193193558016,"updateTime":1749632569000},"schResourceInfo":{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749632569000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932725193193558016,"isDelete":1,"loginName":"root","memoryCapacity":"773220.5","password":"Ascend12!@","port":40086,"resourceName":"testHH2","resourceType":"huawei","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749632569000,"updateUserId":1923195822141345792,"usedMemory":"91710.6"},"schResourcePool":{"createTime":1749629220000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"id":1932711144816906240,"isDelete":1,"poolDescription":"wuhu","poolName":"testHH","updateTime":1749629220000,"updateUserId":1923195822141345792},"schedulingPolicies":1,"status":"failed","taskImage":{"createTime":1749917596000,"id":1933920684832985088,"imageName":"python-calculator:v1.0","isDelete":1,"updateTime":1749917596000},"taskImageId":1933920684832985088,"taskName":"任务监控","taskPriority":0,"updateTime":1750336213000,"updateUserId":1921773426524033024},{"allowPreemption":1,"approveState":"approved","computeDeviceId":1932705891413397506,"cpuNeed":"4","createTime":1749884849000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"dictId":1932726533772808192,"envConfig":"[{\"value\":\"1\",\"key\":\"test\"},{\"value\":\"1\",\"key\":\"test\"}]","failReason":"等待时间超过30分钟","graphicNeededMb":24,"id":1933783331619475456,"memoryNeededMb":24,"partition":{"computeDeviceId":1932705891413397506,"createTime":1749631105000,"createUserId":1923195822141345792,"id":1932719052900274176,"isDelete":1,"templateId":1928336030277046272,"updateTime":1749631105000,"updateUserId":1923195822141345792,"vccId":203},"partitionId":1932719052900274176,"poolId":1932711144816906240,"releasePolicy":1,"resourceId":1932705890410958848,"runCommand":"森岛帆高多发点三个地方会更大飞机上代发给","schComputeDevice":{"createTime":1749627967000,"createUserId":1923195822141345792,"dataDeptId":1787740798154969088,"deviceName":"910B-Ascend-V1","deviceNumber":6,"id":1932705891413397506,"isDelete":1,"memorySize":32768,"modelNumber":"910B-Ascend-V1","resourceId":1932705890410958848,"updateTime":1749627967000},"schResourceInfo":{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749627967000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932705890410958848,"isDelete":1,"loginName":"root","memoryCapacity":"773220.5","password":"Ascend12!@","port":40086,"resourceName":"testHH","resourceType":"huawei","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749627967000,"updateUserId":1923195822141345792,"usedMemory":"91517.6"},"schResourcePool":{"$ref":"$.data.dataList[2].schResourcePool"},"schedulingPolicies":1,"status":"failed","taskImageId":1930147594567159808,"taskName":"审核测试","taskPriority":0,"updateTime":1750326833000,"updateUserId":1923195822141345792}],"totalCount":4},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[INFO ] [2025-06-20 00:29:28] T:[001c99690c754bcaa95b8223609e7da8] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> 开始请求，url=/admin/app/schTaskInfo/audit, reqData={"params":{"taskId":"1935736621068455936","status":"approved"}}
[DEBUG] [2025-06-20 00:29:28] T:[001c99690c754bcaa95b8223609e7da8] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: UPDATE sch_task_approval SET decision_time=?,update_time=?,status=? WHERE is_delete=1 AND (task_id = ?)
[DEBUG] [2025-06-20 00:29:28] T:[001c99690c754bcaa95b8223609e7da8] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 2025-06-20 00:29:28.252(Timestamp), 2025-06-20 00:29:28.253(Timestamp), approved(String), 1935736621068455936(Long)
[DEBUG] [2025-06-20 00:29:28] T:[001c99690c754bcaa95b8223609e7da8] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==    Updates: 0
[DEBUG] [2025-06-20 00:29:28] T:[001c99690c754bcaa95b8223609e7da8] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: UPDATE sch_task_info SET update_time=?,update_user_id=?,approve_state=? WHERE is_delete=1 AND (id = ?)
[DEBUG] [2025-06-20 00:29:28] T:[001c99690c754bcaa95b8223609e7da8] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 2025-06-20 00:29:28.342(Timestamp), 1921773426524033024(Long), approved(String), 1935736621068455936(Long)
[DEBUG] [2025-06-20 00:29:28] T:[001c99690c754bcaa95b8223609e7da8] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==    Updates: 1
[DEBUG] [2025-06-20 00:29:28] T:[001c99690c754bcaa95b8223609e7da8] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,task_id,status,decision_time FROM sch_task_approval WHERE is_delete=1 AND (task_id = ?)
[DEBUG] [2025-06-20 00:29:28] T:[001c99690c754bcaa95b8223609e7da8] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1935736621068455936(Long)
[DEBUG] [2025-06-20 00:29:28] T:[001c99690c754bcaa95b8223609e7da8] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 0
[INFO ] [2025-06-20 00:29:28] T:[001c99690c754bcaa95b8223609e7da8] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> 请求完成, url=/admin/app/schTaskInfo/audit，elapse=227ms, respData={"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","success":true}
[INFO ] [2025-06-20 00:29:28] T:[a4cbac01ac624909af0c8700f02f7288] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> 开始请求，url=/admin/app/schTaskInfo/list, reqData={"orderParam":[{"asc":false,"dateAggregateBy":"","fieldName":"createTime"}],"schTaskInfoDtoFilter":{"dictId":1932726533772808192},"pageParam":{"count":true,"pageNum":1,"pageSize":10}}
[DEBUG] [2025-06-20 00:29:28] T:[a4cbac01ac624909af0c8700f02f7288] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> ==>  Preparing: SELECT count(0) FROM sch_task_info WHERE sch_task_info.dict_id = ? AND sch_task_info.is_delete = 1
[DEBUG] [2025-06-20 00:29:28] T:[a4cbac01ac624909af0c8700f02f7288] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> ==> Parameters: 1932726533772808192(Long)
[DEBUG] [2025-06-20 00:29:28] T:[a4cbac01ac624909af0c8700f02f7288] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:29:28] T:[a4cbac01ac624909af0c8700f02f7288] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> ==>  Preparing: SELECT * FROM sch_task_info WHERE sch_task_info.dict_id = ? AND sch_task_info.is_delete = 1 ORDER BY sch_task_info.create_time DESC LIMIT ?
[DEBUG] [2025-06-20 00:29:28] T:[a4cbac01ac624909af0c8700f02f7288] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> ==> Parameters: 1932726533772808192(Long), 10(Integer)
[DEBUG] [2025-06-20 00:29:28] T:[a4cbac01ac624909af0c8700f02f7288] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> <==      Total: 4
[DEBUG] [2025-06-20 00:29:28] T:[a4cbac01ac624909af0c8700f02f7288] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,image_name,image_desc,image_url,image_version FROM sch_task_image WHERE is_delete=1 AND (id IN (?,?))
[DEBUG] [2025-06-20 00:29:28] T:[a4cbac01ac624909af0c8700f02f7288] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> ==> Parameters: 1930147594567159808(Long), 1933920684832985088(Long)
[DEBUG] [2025-06-20 00:29:28] T:[a4cbac01ac624909af0c8700f02f7288] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:29:28] T:[a4cbac01ac624909af0c8700f02f7288] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,pool_name,pool_description FROM sch_resource_pool WHERE is_delete=1 AND (id IN (?,?,?))
[DEBUG] [2025-06-20 00:29:28] T:[a4cbac01ac624909af0c8700f02f7288] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> ==> Parameters: 1932711144816906240(Long), 1932706048758517760(Long), 1928361548711989248(Long)
[DEBUG] [2025-06-20 00:29:28] T:[a4cbac01ac624909af0c8700f02f7288] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> <==      Total: 3
[DEBUG] [2025-06-20 00:29:28] T:[a4cbac01ac624909af0c8700f02f7288] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE is_delete=1 AND (id IN (?,?,?,?))
[DEBUG] [2025-06-20 00:29:28] T:[a4cbac01ac624909af0c8700f02f7288] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> ==> Parameters: 1932725193193558016(Long), 1932705890410958848(Long), 1932730175653416960(Long), 1932704139536502784(Long)
[DEBUG] [2025-06-20 00:29:28] T:[a4cbac01ac624909af0c8700f02f7288] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> <==      Total: 4
[DEBUG] [2025-06-20 00:29:28] T:[a4cbac01ac624909af0c8700f02f7288] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (id IN (?,?,?,?))
[DEBUG] [2025-06-20 00:29:28] T:[a4cbac01ac624909af0c8700f02f7288] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> ==> Parameters: 1932725194112110592(Long), 1932705891413397506(Long), 1932730176622301186(Long), 1932704140606050304(Long)
[DEBUG] [2025-06-20 00:29:28] T:[a4cbac01ac624909af0c8700f02f7288] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> <==      Total: 4
[DEBUG] [2025-06-20 00:29:28] T:[a4cbac01ac624909af0c8700f02f7288] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> ==>  Preparing: SELECT id,str_id,is_delete,create_user_id,create_time,update_user_id,update_time,data_user_id,data_dept_id,compute_device_id,template_id,vcc_id,vgroup_id FROM sch_virtual_compute_card_situation WHERE is_delete=1 AND (id IN (?,?,?,?))
[DEBUG] [2025-06-20 00:29:28] T:[a4cbac01ac624909af0c8700f02f7288] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> ==> Parameters: 212(Long), 103(Long), 218(Long), 1932719052900274176(Long)
[DEBUG] [2025-06-20 00:29:28] T:[a4cbac01ac624909af0c8700f02f7288] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:29:28] T:[a4cbac01ac624909af0c8700f02f7288] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,task_id,status,decision_time FROM sch_task_approval WHERE is_delete=1 AND (task_id IN (?,?,?,?))
[DEBUG] [2025-06-20 00:29:28] T:[a4cbac01ac624909af0c8700f02f7288] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> ==> Parameters: 1933783331619475456(Long), 1935676707801403392(Long), 1935674439718932480(Long), 1935736621068455936(Long)
[DEBUG] [2025-06-20 00:29:28] T:[a4cbac01ac624909af0c8700f02f7288] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> <==      Total: 0
[INFO ] [2025-06-20 00:29:28] T:[a4cbac01ac624909af0c8700f02f7288] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-10] ==> 请求完成, url=/admin/app/schTaskInfo/list，elapse=403ms, respData={"data":{"dataList":[{"allowPreemption":1,"approveState":"approved","computeDeviceId":1932704140606050304,"cpuNeed":"","createTime":1750350549000,"createUserId":1921773426524033024,"dataDeptId":1923195822141345795,"dataUserId":1921773426524033024,"dictId":1932726533772808192,"envConfig":"[{\"value\":\"1\",\"key\":\"test\"},{\"value\":\"1\",\"key\":\"test\"}]","graphicNeededMb":0,"id":1935736621068455936,"memoryNeededMb":0,"partitionId":212,"poolId":1928361548711989248,"releasePolicy":1,"resourceId":1932704139536502784,"runCommand":"run ","schComputeDevice":{"createTime":1749627550000,"createUserId":1923195822141345792,"dataDeptId":1787740798154969088,"deviceName":"910B-Ascend-V1","deviceNumber":7,"id":1932704140606050304,"isDelete":1,"memorySize":32768,"modelNumber":"910B-Ascend-V1","resourceId":1932704139536502784,"updateTime":1749627550000},"schResourceInfo":{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749627549000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932704139536502784,"isDelete":1,"loginName":"root","memoryCapacity":"773220.5","password":"Ascend12!@","port":40086,"resourceName":"test001","resourceType":"huawei","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749627549000,"updateUserId":1923195822141345792,"usedMemory":"90642.6"},"schResourcePool":{"createTime":1748592195000,"createUserId":1923195822141345792,"id":1928361548711989248,"isDelete":1,"poolDescription":"测试池1001","poolName":"池1001","updateTime":1748592195000,"updateUserId":1923195822141345792},"schedulingPolicies":1,"status":"pending","taskImageId":1930147594567159808,"taskName":"任务1001","taskPriority":10,"updateTime":1750350568000,"updateUserId":1921773426524033024},{"allowPreemption":1,"approveState":"approved","computeDeviceId":1932730176622301186,"cpuNeed":"2","createTime":1750336265000,"createUserId":1921773426524033024,"dataDeptId":1923195822141345795,"dataUserId":1921773426524033024,"dictId":1932726533772808192,"envConfig":"[{\"value\":\"1\",\"key\":\"test\"}]","graphicNeededMb":1,"id":1935676707801403392,"memoryNeededMb":1,"partitionId":218,"poolId":1932706048758517760,"releasePolicy":1,"resourceId":1932730175653416960,"runCommand":"yum","schComputeDevice":{"createTime":1749633757000,"createUserId":1923195822141345792,"dataDeptId":1787740798154969088,"deviceName":"910B-Ascend-V1","deviceNumber":7,"id":1932730176622301186,"isDelete":1,"memorySize":32768,"modelNumber":"910B-Ascend-V1","resourceId":1932730175653416960,"updateTime":1749633757000},"schResourceInfo":{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749633757000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932730175653416960,"isDelete":1,"loginName":"root","memoryCapacity":"773220.5","password":"Ascend12!@","port":40086,"resourceName":"test007","resourceType":"x86","status":"offline","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749633757000,"updateUserId":1923195822141345792,"usedMemory":"92160.0"},"schResourcePool":{"createTime":1749628005000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"id":1932706048758517760,"isDelete":1,"poolDescription":"资源池是算力调度平台的核心基础模块，汇聚多样算力资源（含CPU、内存、显存等），可动态分配、灵活调度。支持按任务需求快速切分、精准匹配，实时监控资源占用与状态，保障任务高效运行，为算力密集型业务（如模型训练、数据处理 ）提供稳定、弹性的资源供给，助力优化资源利用与业务流程。","poolName":"source-test","updateTime":1749628005000,"updateUserId":1923195822141345792},"schedulingPolicies":1,"status":"failed","taskImageId":1930147594567159808,"taskName":"001","taskPriority":0,"updateTime":1750336286000,"updateUserId":1921773426524033024},{"allowPreemption":1,"approveState":"approved","computeDeviceId":1932725194112110592,"cpuNeed":"1","createTime":1750335724000,"createUserId":1921773426524033024,"dataDeptId":1923195822141345795,"dataUserId":1921773426524033024,"dictId":1932726533772808192,"envConfig":"[]","graphicNeededMb":56,"id":1935674439718932480,"memoryNeededMb":345,"partitionId":103,"poolId":1932711144816906240,"releasePolicy":1,"resourceId":1932725193193558016,"runCommand":"yum install","schComputeDevice":{"createTime":1749632569000,"createUserId":1923195822141345792,"dataDeptId":1787740798154969088,"deviceName":"910B-Ascend-V1","deviceNumber":0,"id":1932725194112110592,"isDelete":1,"memorySize":32768,"modelNumber":"910B-Ascend-V1","resourceId":1932725193193558016,"updateTime":1749632569000},"schResourceInfo":{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749632569000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932725193193558016,"isDelete":1,"loginName":"root","memoryCapacity":"773220.5","password":"Ascend12!@","port":40086,"resourceName":"testHH2","resourceType":"huawei","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749632569000,"updateUserId":1923195822141345792,"usedMemory":"91710.6"},"schResourcePool":{"createTime":1749629220000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"id":1932711144816906240,"isDelete":1,"poolDescription":"wuhu","poolName":"testHH","updateTime":1749629220000,"updateUserId":1923195822141345792},"schedulingPolicies":1,"status":"failed","taskImage":{"createTime":1749917596000,"id":1933920684832985088,"imageName":"python-calculator:v1.0","isDelete":1,"updateTime":1749917596000},"taskImageId":1933920684832985088,"taskName":"任务监控","taskPriority":0,"updateTime":1750336213000,"updateUserId":1921773426524033024},{"allowPreemption":1,"approveState":"approved","computeDeviceId":1932705891413397506,"cpuNeed":"4","createTime":1749884849000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"dictId":1932726533772808192,"envConfig":"[{\"value\":\"1\",\"key\":\"test\"},{\"value\":\"1\",\"key\":\"test\"}]","failReason":"等待时间超过30分钟","graphicNeededMb":24,"id":1933783331619475456,"memoryNeededMb":24,"partition":{"computeDeviceId":1932705891413397506,"createTime":1749631105000,"createUserId":1923195822141345792,"id":1932719052900274176,"isDelete":1,"templateId":1928336030277046272,"updateTime":1749631105000,"updateUserId":1923195822141345792,"vccId":203},"partitionId":1932719052900274176,"poolId":1932711144816906240,"releasePolicy":1,"resourceId":1932705890410958848,"runCommand":"森岛帆高多发点三个地方会更大飞机上代发给","schComputeDevice":{"createTime":1749627967000,"createUserId":1923195822141345792,"dataDeptId":1787740798154969088,"deviceName":"910B-Ascend-V1","deviceNumber":6,"id":1932705891413397506,"isDelete":1,"memorySize":32768,"modelNumber":"910B-Ascend-V1","resourceId":1932705890410958848,"updateTime":1749627967000},"schResourceInfo":{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749627967000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932705890410958848,"isDelete":1,"loginName":"root","memoryCapacity":"773220.5","password":"Ascend12!@","port":40086,"resourceName":"testHH","resourceType":"huawei","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749627967000,"updateUserId":1923195822141345792,"usedMemory":"91517.6"},"schResourcePool":{"$ref":"$.data.dataList[2].schResourcePool"},"schedulingPolicies":1,"status":"failed","taskImageId":1930147594567159808,"taskName":"审核测试","taskPriority":0,"updateTime":1750326833000,"updateUserId":1923195822141345792}],"totalCount":4},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[INFO ] [2025-06-20 00:29:33] T:[cbe0682c05454cb7b3ae128f82bd99e7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> 开始请求，url=/admin/app/schTaskInfo/startTask, reqData={"taskId":"1935736621068455936"}
[DEBUG] [2025-06-20 00:29:33] T:[cbe0682c05454cb7b3ae128f82bd99e7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==>  Preparing: SELECT id,exit_code,fail_reason,scale_plan_id,dict_id,container_name,container_status,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,task_name,status,task_priority,graphic_needed_mb,memory_needed_mb,cpu_need,pool_id,resource_id,task_image_id,compute_device_id,partition_id,run_command,env_config,release_policy,start_time,end_tiem,estimat_time,scheduling_policies,approve_state,allow_preemption FROM sch_task_info WHERE id=? AND is_delete=1
[DEBUG] [2025-06-20 00:29:33] T:[cbe0682c05454cb7b3ae128f82bd99e7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==> Parameters: 1935736621068455936(Long)
[DEBUG] [2025-06-20 00:29:33] T:[cbe0682c05454cb7b3ae128f82bd99e7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:29:33] T:[cbe0682c05454cb7b3ae128f82bd99e7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==>  Preparing: UPDATE sch_task_info SET status=? WHERE is_delete=1 AND (id = ?)
[DEBUG] [2025-06-20 00:29:33] T:[cbe0682c05454cb7b3ae128f82bd99e7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==> Parameters: starting(String), 1935736621068455936(String)
[DEBUG] [2025-06-20 00:29:33] T:[cbe0682c05454cb7b3ae128f82bd99e7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> <==    Updates: 1
[DEBUG] [2025-06-20 00:29:33] T:[cbe0682c05454cb7b3ae128f82bd99e7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==>  Preparing: SELECT id,exit_code,fail_reason,scale_plan_id,dict_id,container_name,container_status,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,task_name,status,task_priority,graphic_needed_mb,memory_needed_mb,cpu_need,pool_id,resource_id,task_image_id,compute_device_id,partition_id,run_command,env_config,release_policy,start_time,end_tiem,estimat_time,scheduling_policies,approve_state,allow_preemption FROM sch_task_info WHERE id=? AND is_delete=1
[DEBUG] [2025-06-20 00:29:33] T:[cbe0682c05454cb7b3ae128f82bd99e7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> ==> Parameters: 1935736621068455936(Long)
[DEBUG] [2025-06-20 00:29:38] T:[cbe0682c05454cb7b3ae128f82bd99e7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> <==      Total: 1
[INFO ] [2025-06-20 00:29:38] T:[cbe0682c05454cb7b3ae128f82bd99e7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-7] ==> 请求完成, url=/admin/app/schTaskInfo/startTask，elapse=5489ms, respData={"data":{"allowPreemption":1,"approveState":"approved","computeDeviceId":1932704140606050304,"cpuNeed":"","createTime":1750350549000,"createUserId":1921773426524033024,"dataDeptId":1923195822141345795,"dataUserId":1921773426524033024,"dictId":1932726533772808192,"envConfig":"[{\"value\":\"1\",\"key\":\"test\"},{\"value\":\"1\",\"key\":\"test\"}]","graphicNeededMb":0,"id":1935736621068455936,"isDelete":1,"memoryNeededMb":0,"partitionId":212,"poolId":1928361548711989248,"releasePolicy":1,"resourceId":1932704139536502784,"runCommand":"run ","schedulingPolicies":1,"status":"starting","taskImageId":1930147594567159808,"taskName":"任务1001","taskPriority":10,"updateTime":1750350568000,"updateUserId":1921773426524033024},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[DEBUG] [2025-06-20 00:29:38] T:[] S:[] U:[] [task-processor-1] ==> ==>  Preparing: SELECT *FROM sch_task_info ti WHERE ti.id=? AND ti.approve_state='approved'
[DEBUG] [2025-06-20 00:29:38] T:[] S:[] U:[] [task-processor-1] ==> ==> Parameters: 1935736621068455936(Long)
[DEBUG] [2025-06-20 00:29:39] T:[] S:[] U:[] [task-processor-1] ==> <==      Total: 1
[INFO ] [2025-06-20 00:29:40] T:[4c6ec5e2d16c414fa761d98a41bfd0f7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> 开始请求，url=/admin/app/schTaskInfo/list, reqData={"orderParam":[{"asc":false,"dateAggregateBy":"","fieldName":"createTime"}],"schTaskInfoDtoFilter":{"dictId":1932726533772808192},"pageParam":{"count":true,"pageNum":1,"pageSize":10}}
[DEBUG] [2025-06-20 00:29:40] T:[] S:[] U:[] [flowable-task-Executor-8] ==> ==>  Preparing: INSERT INTO zz_sys_operation_log ( log_id, description, operation_type, service_name, api_class, api_method, session_id, trace_id, elapse, request_method, request_url, request_arguments, response_result, request_ip, success, operator_id, operator_name, operation_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[DEBUG] [2025-06-20 00:29:45] T:[] S:[] U:[] [flowable-task-Executor-8] ==> ==> Parameters: 1935736721941467136(Long), (String), 120(Integer), application-webadmin(String), supie.webadmin.app.controller.SchTaskInfoController(String), supie.webadmin.app.controller.SchTaskInfoController.startTask(String), Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39(String), cbe0682c05454cb7b3ae128f82bd99e7(String), 5490(Long), POST(String), /admin/app/schTaskInfo/startTask(String), {"taskId":"1935736621068455936"}(String), {"data":{"allowPreemption":1,"approveState":"approved","computeDeviceId":1932704140606050304,"cpuNeed":"","createTime":1750350549000,"createUserId":1921773426524033024,"dataDeptId":1923195822141345795,"dataUserId":1921773426524033024,"dictId":1932726533772808192,"envConfig":"[{\"value\":\"1\",\"key\":\"test\"},{\"value\":\"1\",\"key\":\"test\"}]","graphicNeededMb":0,"id":1935736621068455936,"isDelete":1,"memoryNeededMb":0,"partitionId":212,"poolId":1928361548711989248,"releasePolicy":1,"resourceId":1932704139536502784,"runCommand":"run ","schedulingPolicies":1,"status":"starting","taskImageId":1930147594567159808,"taskName":"任务1001","taskPriority":10,"updateTime":1750350568000,"updateUserId":1921773426524033024},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}(String), ************(String), true(Boolean), 1921773426524033024(Long), admin(String), 2025-06-20 00:29:33.418(Timestamp)
[DEBUG] [2025-06-20 00:29:45] T:[] S:[] U:[] [flowable-task-Executor-8] ==> <==    Updates: 1
[DEBUG] [2025-06-20 00:29:46] T:[4c6ec5e2d16c414fa761d98a41bfd0f7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT count(0) FROM sch_task_info WHERE sch_task_info.dict_id = ? AND sch_task_info.is_delete = 1
[DEBUG] [2025-06-20 00:29:46] T:[4c6ec5e2d16c414fa761d98a41bfd0f7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932726533772808192(Long)
[DEBUG] [2025-06-20 00:29:46] T:[4c6ec5e2d16c414fa761d98a41bfd0f7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:29:46] T:[4c6ec5e2d16c414fa761d98a41bfd0f7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT * FROM sch_task_info WHERE sch_task_info.dict_id = ? AND sch_task_info.is_delete = 1 ORDER BY sch_task_info.create_time DESC LIMIT ?
[DEBUG] [2025-06-20 00:29:46] T:[4c6ec5e2d16c414fa761d98a41bfd0f7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932726533772808192(Long), 10(Integer)
[DEBUG] [2025-06-20 00:29:46] T:[4c6ec5e2d16c414fa761d98a41bfd0f7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 4
[DEBUG] [2025-06-20 00:29:46] T:[4c6ec5e2d16c414fa761d98a41bfd0f7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,image_name,image_desc,image_url,image_version FROM sch_task_image WHERE is_delete=1 AND (id IN (?,?))
[DEBUG] [2025-06-20 00:29:46] T:[4c6ec5e2d16c414fa761d98a41bfd0f7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1930147594567159808(Long), 1933920684832985088(Long)
[DEBUG] [2025-06-20 00:29:46] T:[4c6ec5e2d16c414fa761d98a41bfd0f7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:29:46] T:[4c6ec5e2d16c414fa761d98a41bfd0f7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,pool_name,pool_description FROM sch_resource_pool WHERE is_delete=1 AND (id IN (?,?,?))
[DEBUG] [2025-06-20 00:29:46] T:[4c6ec5e2d16c414fa761d98a41bfd0f7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932711144816906240(Long), 1932706048758517760(Long), 1928361548711989248(Long)
[DEBUG] [2025-06-20 00:29:46] T:[4c6ec5e2d16c414fa761d98a41bfd0f7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 3
[DEBUG] [2025-06-20 00:29:46] T:[4c6ec5e2d16c414fa761d98a41bfd0f7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE is_delete=1 AND (id IN (?,?,?,?))
[DEBUG] [2025-06-20 00:29:46] T:[4c6ec5e2d16c414fa761d98a41bfd0f7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932725193193558016(Long), 1932705890410958848(Long), 1932730175653416960(Long), 1932704139536502784(Long)
[DEBUG] [2025-06-20 00:29:46] T:[4c6ec5e2d16c414fa761d98a41bfd0f7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 4
[DEBUG] [2025-06-20 00:29:46] T:[4c6ec5e2d16c414fa761d98a41bfd0f7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (id IN (?,?,?,?))
[DEBUG] [2025-06-20 00:29:46] T:[4c6ec5e2d16c414fa761d98a41bfd0f7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1932725194112110592(Long), 1932705891413397506(Long), 1932730176622301186(Long), 1932704140606050304(Long)
[DEBUG] [2025-06-20 00:29:46] T:[4c6ec5e2d16c414fa761d98a41bfd0f7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 4
[DEBUG] [2025-06-20 00:29:46] T:[4c6ec5e2d16c414fa761d98a41bfd0f7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT id,str_id,is_delete,create_user_id,create_time,update_user_id,update_time,data_user_id,data_dept_id,compute_device_id,template_id,vcc_id,vgroup_id FROM sch_virtual_compute_card_situation WHERE is_delete=1 AND (id IN (?,?,?,?))
[DEBUG] [2025-06-20 00:29:46] T:[4c6ec5e2d16c414fa761d98a41bfd0f7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 212(Long), 103(Long), 218(Long), 1932719052900274176(Long)
[DEBUG] [2025-06-20 00:29:46] T:[4c6ec5e2d16c414fa761d98a41bfd0f7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:29:46] T:[4c6ec5e2d16c414fa761d98a41bfd0f7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,task_id,status,decision_time FROM sch_task_approval WHERE is_delete=1 AND (task_id IN (?,?,?,?))
[DEBUG] [2025-06-20 00:29:46] T:[4c6ec5e2d16c414fa761d98a41bfd0f7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> ==> Parameters: 1933783331619475456(Long), 1935676707801403392(Long), 1935674439718932480(Long), 1935736621068455936(Long)
[DEBUG] [2025-06-20 00:29:46] T:[4c6ec5e2d16c414fa761d98a41bfd0f7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> <==      Total: 0
[INFO ] [2025-06-20 00:29:46] T:[4c6ec5e2d16c414fa761d98a41bfd0f7] S:[Authorization:login:token-session:d134552f-b98e-4ecd-ab0a-1cc64b737e39] U:[1921773426524033024] [http-nio-8084-exec-2] ==> 请求完成, url=/admin/app/schTaskInfo/list，elapse=5795ms, respData={"data":{"dataList":[{"allowPreemption":1,"approveState":"approved","computeDeviceId":1932704140606050304,"cpuNeed":"","createTime":1750350549000,"createUserId":1921773426524033024,"dataDeptId":1923195822141345795,"dataUserId":1921773426524033024,"dictId":1932726533772808192,"envConfig":"[{\"value\":\"1\",\"key\":\"test\"},{\"value\":\"1\",\"key\":\"test\"}]","graphicNeededMb":0,"id":1935736621068455936,"memoryNeededMb":0,"partitionId":212,"poolId":1928361548711989248,"releasePolicy":1,"resourceId":1932704139536502784,"runCommand":"run ","schComputeDevice":{"createTime":1749627550000,"createUserId":1923195822141345792,"dataDeptId":1787740798154969088,"deviceName":"910B-Ascend-V1","deviceNumber":7,"id":1932704140606050304,"isDelete":1,"memorySize":32768,"modelNumber":"910B-Ascend-V1","resourceId":1932704139536502784,"updateTime":1749627550000},"schResourceInfo":{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749627549000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932704139536502784,"isDelete":1,"loginName":"root","memoryCapacity":"773220.5","password":"Ascend12!@","port":40086,"resourceName":"test001","resourceType":"huawei","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749627549000,"updateUserId":1923195822141345792,"usedMemory":"90642.6"},"schResourcePool":{"createTime":1748592195000,"createUserId":1923195822141345792,"id":1928361548711989248,"isDelete":1,"poolDescription":"测试池1001","poolName":"池1001","updateTime":1748592195000,"updateUserId":1923195822141345792},"schedulingPolicies":1,"status":"starting","taskImageId":1930147594567159808,"taskName":"任务1001","taskPriority":10,"updateTime":1750350568000,"updateUserId":1921773426524033024},{"allowPreemption":1,"approveState":"approved","computeDeviceId":1932730176622301186,"cpuNeed":"2","createTime":1750336265000,"createUserId":1921773426524033024,"dataDeptId":1923195822141345795,"dataUserId":1921773426524033024,"dictId":1932726533772808192,"envConfig":"[{\"value\":\"1\",\"key\":\"test\"}]","graphicNeededMb":1,"id":1935676707801403392,"memoryNeededMb":1,"partitionId":218,"poolId":1932706048758517760,"releasePolicy":1,"resourceId":1932730175653416960,"runCommand":"yum","schComputeDevice":{"createTime":1749633757000,"createUserId":1923195822141345792,"dataDeptId":1787740798154969088,"deviceName":"910B-Ascend-V1","deviceNumber":7,"id":1932730176622301186,"isDelete":1,"memorySize":32768,"modelNumber":"910B-Ascend-V1","resourceId":1932730175653416960,"updateTime":1749633757000},"schResourceInfo":{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749633757000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932730175653416960,"isDelete":1,"loginName":"root","memoryCapacity":"773220.5","password":"Ascend12!@","port":40086,"resourceName":"test007","resourceType":"x86","status":"offline","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749633757000,"updateUserId":1923195822141345792,"usedMemory":"92160.0"},"schResourcePool":{"createTime":1749628005000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"id":1932706048758517760,"isDelete":1,"poolDescription":"资源池是算力调度平台的核心基础模块，汇聚多样算力资源（含CPU、内存、显存等），可动态分配、灵活调度。支持按任务需求快速切分、精准匹配，实时监控资源占用与状态，保障任务高效运行，为算力密集型业务（如模型训练、数据处理 ）提供稳定、弹性的资源供给，助力优化资源利用与业务流程。","poolName":"source-test","updateTime":1749628005000,"updateUserId":1923195822141345792},"schedulingPolicies":1,"status":"failed","taskImageId":1930147594567159808,"taskName":"001","taskPriority":0,"updateTime":1750336286000,"updateUserId":1921773426524033024},{"allowPreemption":1,"approveState":"approved","computeDeviceId":1932725194112110592,"cpuNeed":"1","createTime":1750335724000,"createUserId":1921773426524033024,"dataDeptId":1923195822141345795,"dataUserId":1921773426524033024,"dictId":1932726533772808192,"envConfig":"[]","graphicNeededMb":56,"id":1935674439718932480,"memoryNeededMb":345,"partitionId":103,"poolId":1932711144816906240,"releasePolicy":1,"resourceId":1932725193193558016,"runCommand":"yum install","schComputeDevice":{"createTime":1749632569000,"createUserId":1923195822141345792,"dataDeptId":1787740798154969088,"deviceName":"910B-Ascend-V1","deviceNumber":0,"id":1932725194112110592,"isDelete":1,"memorySize":32768,"modelNumber":"910B-Ascend-V1","resourceId":1932725193193558016,"updateTime":1749632569000},"schResourceInfo":{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749632569000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932725193193558016,"isDelete":1,"loginName":"root","memoryCapacity":"773220.5","password":"Ascend12!@","port":40086,"resourceName":"testHH2","resourceType":"huawei","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749632569000,"updateUserId":1923195822141345792,"usedMemory":"91710.6"},"schResourcePool":{"createTime":1749629220000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"id":1932711144816906240,"isDelete":1,"poolDescription":"wuhu","poolName":"testHH","updateTime":1749629220000,"updateUserId":1923195822141345792},"schedulingPolicies":1,"status":"failed","taskImage":{"createTime":1749917596000,"id":1933920684832985088,"imageName":"python-calculator:v1.0","isDelete":1,"updateTime":1749917596000},"taskImageId":1933920684832985088,"taskName":"任务监控","taskPriority":0,"updateTime":1750336213000,"updateUserId":1921773426524033024},{"allowPreemption":1,"approveState":"approved","computeDeviceId":1932705891413397506,"cpuNeed":"4","createTime":1749884849000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"dictId":1932726533772808192,"envConfig":"[{\"value\":\"1\",\"key\":\"test\"},{\"value\":\"1\",\"key\":\"test\"}]","failReason":"等待时间超过30分钟","graphicNeededMb":24,"id":1933783331619475456,"memoryNeededMb":24,"partition":{"computeDeviceId":1932705891413397506,"createTime":1749631105000,"createUserId":1923195822141345792,"id":1932719052900274176,"isDelete":1,"templateId":1928336030277046272,"updateTime":1749631105000,"updateUserId":1923195822141345792,"vccId":203},"partitionId":1932719052900274176,"poolId":1932711144816906240,"releasePolicy":1,"resourceId":1932705890410958848,"runCommand":"森岛帆高多发点三个地方会更大飞机上代发给","schComputeDevice":{"createTime":1749627967000,"createUserId":1923195822141345792,"dataDeptId":1787740798154969088,"deviceName":"910B-Ascend-V1","deviceNumber":6,"id":1932705891413397506,"isDelete":1,"memorySize":32768,"modelNumber":"910B-Ascend-V1","resourceId":1932705890410958848,"updateTime":1749627967000},"schResourceInfo":{"computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1749627967000,"createUserId":1923195822141345792,"dataDeptId":1923195822141345795,"dataUserId":1923195822141345792,"gpuCount":"8","graphicsMemory":"262144","hostIp":"*************","id":1932705890410958848,"isDelete":1,"loginName":"root","memoryCapacity":"773220.5","password":"Ascend12!@","port":40086,"resourceName":"testHH","resourceType":"huawei","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1749627967000,"updateUserId":1923195822141345792,"usedMemory":"91517.6"},"schResourcePool":{"$ref":"$.data.dataList[2].schResourcePool"},"schedulingPolicies":1,"status":"failed","taskImageId":1930147594567159808,"taskName":"审核测试","taskPriority":0,"updateTime":1750326833000,"updateUserId":1923195822141345792}],"totalCount":4},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[DEBUG] [2025-06-20 00:29:46] T:[] S:[] U:[] [task-processor-1] ==> ==>  Preparing: SELECT id,exit_code,fail_reason,scale_plan_id,dict_id,container_name,container_status,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,task_name,status,task_priority,graphic_needed_mb,memory_needed_mb,cpu_need,pool_id,resource_id,task_image_id,compute_device_id,partition_id,run_command,env_config,release_policy,start_time,end_tiem,estimat_time,scheduling_policies,approve_state,allow_preemption FROM sch_task_info WHERE is_delete=1 AND (status = ?)
[DEBUG] [2025-06-20 00:29:46] T:[] S:[] U:[] [task-processor-1] ==> ==> Parameters: running(String)
[DEBUG] [2025-06-20 00:29:47] T:[] S:[] U:[] [task-processor-1] ==> <==      Total: 20
[DEBUG] [2025-06-20 00:29:51] T:[] S:[] U:[] [task-processor-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-06-20 00:29:51] T:[] S:[] U:[] [task-processor-1] ==> ==> Parameters: 1932704139536502784(Long)
[DEBUG] [2025-06-20 00:29:51] T:[] S:[] U:[] [task-processor-1] ==> <==      Total: 1
[DEBUG] [2025-06-20 00:30:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-06-20 00:30:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-06-20 00:30:00.035(Timestamp)
[DEBUG] [2025-06-20 00:30:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[DEBUG] [2025-06-20 00:30:08] T:[] S:[] U:[] [task-processor-1] ==> ==>  Preparing: SELECT id,str_id,is_delete,create_user_id,create_time,update_user_id,update_time,data_user_id,data_dept_id,compute_device_id,template_id,vcc_id,vgroup_id FROM sch_virtual_compute_card_situation WHERE id=? AND is_delete=1
[DEBUG] [2025-06-20 00:30:08] T:[] S:[] U:[] [task-processor-1] ==> ==> Parameters: 212(Long)
[DEBUG] [2025-06-20 00:30:08] T:[] S:[] U:[] [task-processor-1] ==> <==      Total: 0
[ERROR] [2025-06-20 00:30:15] T:[] S:[] U:[] [task-processor-1] ==> 任务异常: taskId=1935736621068455936, error=java.lang.NullPointerException: Cannot invoke "supie.webadmin.app.model.SchVirtualComputeCardSituation.getVccId()" because "car" is null
java.util.concurrent.CompletionException: java.lang.NullPointerException: Cannot invoke "supie.webadmin.app.model.SchVirtualComputeCardSituation.getVccId()" because "car" is null
	at java.base/java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:315)
	at java.base/java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:320)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1770)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: java.lang.NullPointerException: Cannot invoke "supie.webadmin.app.model.SchVirtualComputeCardSituation.getVccId()" because "car" is null
	at supie.webadmin.app.service.impl.SchTaskInfoServiceImpl.startTask(SchTaskInfoServiceImpl.java:1304)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:343)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:178)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:178)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703)
	at supie.webadmin.app.service.impl.SchTaskInfoServiceImpl$$SpringCGLIB$$0.startTask(<generated>)
	at supie.webadmin.app.controller.SchTaskInfoController.lambda$startTask$0(SchTaskInfoController.java:150)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1768)
	... 4 common frames omitted
[DEBUG] [2025-06-20 00:30:15] T:[] S:[] U:[] [task-processor-1] ==> ==>  Preparing: UPDATE sch_task_info SET update_time=?,fail_reason=?,status=? WHERE is_delete=1 AND (id = ?)
[DEBUG] [2025-06-20 00:30:15] T:[] S:[] U:[] [task-processor-1] ==> ==> Parameters: 2025-06-20 00:30:15.646(Timestamp), java.lang.NullPointerException: Cannot invoke "supie.webadmin.app.model.SchVirtualComputeCardSituation.getVccId()" because "car" is null(String), failed(String), 1935736621068455936(String)
[DEBUG] [2025-06-20 00:30:15] T:[] S:[] U:[] [task-processor-1] ==> <==    Updates: 1
