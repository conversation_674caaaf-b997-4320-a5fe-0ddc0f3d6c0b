[ERROR] [2025-06-19 00:00:00] T:[] S:[] U:[] [scheduling-1] ==> Unexpected error occurred in scheduled task
java.lang.NoClassDefFoundError: supie/common/core/object/GlobalThreadLocal
	at supie.common.datafilter.interceptor.MybatisDataFilterInterceptor.intercept(MybatisDataFilterInterceptor.java:205)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy307.prepare(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:90)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:64)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:333)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:151)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy306.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	at jdk.proxy2/jdk.proxy2.$Proxy155.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy259.selectList(Unknown Source)
	at supie.common.flow.service.impl.FlowTaskTimeoutJobServiceImpl.getExecutableList(FlowTaskTimeoutJobServiceImpl.java:138)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:343)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at supie.common.core.aop.DataSourceResolveAspect.around(DataSourceResolveAspect.java:59)
	at jdk.internal.reflect.GeneratedMethodAccessor15.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:627)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:178)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703)
	at supie.common.flow.service.impl.FlowTaskTimeoutJobServiceImpl$$SpringCGLIB$$0.getExecutableList(<generated>)
	at supie.common.flow.timer.FlowTaskTimeoutTimer.execute(FlowTaskTimeoutTimer.java:54)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:96)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: java.lang.ClassNotFoundException: supie.common.core.object.GlobalThreadLocal
	... 67 common frames omitted
[ERROR] [2025-06-19 00:05:00] T:[] S:[] U:[] [scheduling-1] ==> Unexpected error occurred in scheduled task
java.lang.NoClassDefFoundError: supie/common/core/object/GlobalThreadLocal
	at supie.common.datafilter.interceptor.MybatisDataFilterInterceptor.intercept(MybatisDataFilterInterceptor.java:205)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy307.prepare(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:90)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:64)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:333)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:151)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy306.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	at jdk.proxy2/jdk.proxy2.$Proxy155.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy259.selectList(Unknown Source)
	at supie.common.flow.service.impl.FlowTaskTimeoutJobServiceImpl.getExecutableList(FlowTaskTimeoutJobServiceImpl.java:138)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:343)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at supie.common.core.aop.DataSourceResolveAspect.around(DataSourceResolveAspect.java:59)
	at jdk.internal.reflect.GeneratedMethodAccessor15.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:627)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:178)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703)
	at supie.common.flow.service.impl.FlowTaskTimeoutJobServiceImpl$$SpringCGLIB$$0.getExecutableList(<generated>)
	at supie.common.flow.timer.FlowTaskTimeoutTimer.execute(FlowTaskTimeoutTimer.java:54)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:96)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: java.lang.ClassNotFoundException: supie.common.core.object.GlobalThreadLocal
	... 67 common frames omitted
[ERROR] [2025-06-19 00:10:00] T:[] S:[] U:[] [scheduling-1] ==> Unexpected error occurred in scheduled task
java.lang.NoClassDefFoundError: supie/common/core/object/GlobalThreadLocal
	at supie.common.datafilter.interceptor.MybatisDataFilterInterceptor.intercept(MybatisDataFilterInterceptor.java:205)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy307.prepare(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:90)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:64)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:333)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:151)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy306.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	at jdk.proxy2/jdk.proxy2.$Proxy155.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy259.selectList(Unknown Source)
	at supie.common.flow.service.impl.FlowTaskTimeoutJobServiceImpl.getExecutableList(FlowTaskTimeoutJobServiceImpl.java:138)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:343)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at supie.common.core.aop.DataSourceResolveAspect.around(DataSourceResolveAspect.java:59)
	at jdk.internal.reflect.GeneratedMethodAccessor15.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:627)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:178)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703)
	at supie.common.flow.service.impl.FlowTaskTimeoutJobServiceImpl$$SpringCGLIB$$0.getExecutableList(<generated>)
	at supie.common.flow.timer.FlowTaskTimeoutTimer.execute(FlowTaskTimeoutTimer.java:54)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:96)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: java.lang.ClassNotFoundException: supie.common.core.object.GlobalThreadLocal
	... 67 common frames omitted
[ERROR] [2025-06-19 00:15:00] T:[] S:[] U:[] [scheduling-1] ==> Unexpected error occurred in scheduled task
java.lang.NoClassDefFoundError: supie/common/core/object/GlobalThreadLocal
	at supie.common.datafilter.interceptor.MybatisDataFilterInterceptor.intercept(MybatisDataFilterInterceptor.java:205)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy307.prepare(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:90)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:64)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:333)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:151)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy306.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	at jdk.proxy2/jdk.proxy2.$Proxy155.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy259.selectList(Unknown Source)
	at supie.common.flow.service.impl.FlowTaskTimeoutJobServiceImpl.getExecutableList(FlowTaskTimeoutJobServiceImpl.java:138)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:343)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at supie.common.core.aop.DataSourceResolveAspect.around(DataSourceResolveAspect.java:59)
	at jdk.internal.reflect.GeneratedMethodAccessor15.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:627)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:178)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703)
	at supie.common.flow.service.impl.FlowTaskTimeoutJobServiceImpl$$SpringCGLIB$$0.getExecutableList(<generated>)
	at supie.common.flow.timer.FlowTaskTimeoutTimer.execute(FlowTaskTimeoutTimer.java:54)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:96)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: java.lang.ClassNotFoundException: supie.common.core.object.GlobalThreadLocal
	... 67 common frames omitted
[ERROR] [2025-06-19 00:20:00] T:[] S:[] U:[] [scheduling-1] ==> Unexpected error occurred in scheduled task
java.lang.NoClassDefFoundError: supie/common/core/object/GlobalThreadLocal
	at supie.common.datafilter.interceptor.MybatisDataFilterInterceptor.intercept(MybatisDataFilterInterceptor.java:205)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy307.prepare(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:90)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:64)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:333)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:151)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy306.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	at jdk.proxy2/jdk.proxy2.$Proxy155.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy259.selectList(Unknown Source)
	at supie.common.flow.service.impl.FlowTaskTimeoutJobServiceImpl.getExecutableList(FlowTaskTimeoutJobServiceImpl.java:138)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:343)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at supie.common.core.aop.DataSourceResolveAspect.around(DataSourceResolveAspect.java:59)
	at jdk.internal.reflect.GeneratedMethodAccessor15.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:627)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:178)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703)
	at supie.common.flow.service.impl.FlowTaskTimeoutJobServiceImpl$$SpringCGLIB$$0.getExecutableList(<generated>)
	at supie.common.flow.timer.FlowTaskTimeoutTimer.execute(FlowTaskTimeoutTimer.java:54)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:96)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: java.lang.ClassNotFoundException: supie.common.core.object.GlobalThreadLocal
	... 67 common frames omitted
[ERROR] [2025-06-19 00:25:00] T:[] S:[] U:[] [scheduling-1] ==> Unexpected error occurred in scheduled task
java.lang.NoClassDefFoundError: supie/common/core/object/GlobalThreadLocal
	at supie.common.datafilter.interceptor.MybatisDataFilterInterceptor.intercept(MybatisDataFilterInterceptor.java:205)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy307.prepare(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:90)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:64)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:333)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:151)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy306.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	at jdk.proxy2/jdk.proxy2.$Proxy155.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy259.selectList(Unknown Source)
	at supie.common.flow.service.impl.FlowTaskTimeoutJobServiceImpl.getExecutableList(FlowTaskTimeoutJobServiceImpl.java:138)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:343)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at supie.common.core.aop.DataSourceResolveAspect.around(DataSourceResolveAspect.java:59)
	at jdk.internal.reflect.GeneratedMethodAccessor15.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:627)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:178)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703)
	at supie.common.flow.service.impl.FlowTaskTimeoutJobServiceImpl$$SpringCGLIB$$0.getExecutableList(<generated>)
	at supie.common.flow.timer.FlowTaskTimeoutTimer.execute(FlowTaskTimeoutTimer.java:54)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:96)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: java.lang.ClassNotFoundException: supie.common.core.object.GlobalThreadLocal
	... 67 common frames omitted
[INFO ] [2025-06-19 17:47:17] T:[] S:[] U:[] [background-preinit] ==> HV000001: Hibernate Validator 8.0.1.Final
[INFO ] [2025-06-19 17:47:17] T:[] S:[] U:[] [main] ==> Starting WebAdminApplication using Java 17.0.4 with PID 32612 (D:\gy_sch_be\application-webadmin\target\classes started by Superhero in D:\gy_sch_be)
[INFO ] [2025-06-19 17:47:17] T:[] S:[] U:[] [main] ==> The following 1 profile is active: "dev"
[INFO ] [2025-06-19 17:47:35] T:[] S:[] U:[] [main] ==> Multiple Spring Data modules found, entering strict repository configuration mode
[INFO ] [2025-06-19 17:47:35] T:[] S:[] U:[] [main] ==> Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[INFO ] [2025-06-19 17:47:35] T:[] S:[] U:[] [main] ==> Finished Spring Data repository scanning in 100 ms. Found 0 Redis repository interfaces.
[INFO ] [2025-06-19 17:47:37] T:[] S:[] U:[] [main] ==> Bean 'commonWebMvcConfig' of type [supie.common.core.config.CommonWebMvcConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO ] [2025-06-19 17:47:41] T:[] S:[] U:[] [main] ==> Tomcat initialized with port(s): 8082 (http)
[INFO ] [2025-06-19 17:47:41] T:[] S:[] U:[] [main] ==> Initializing ProtocolHandler ["http-nio-8082"]
[INFO ] [2025-06-19 17:47:41] T:[] S:[] U:[] [main] ==> Starting service [Tomcat]
[INFO ] [2025-06-19 17:47:41] T:[] S:[] U:[] [main] ==> Starting Servlet engine: [Apache Tomcat/10.1.16]
[INFO ] [2025-06-19 17:47:43] T:[] S:[] U:[] [main] ==> Initializing Spring embedded WebApplicationContext
[INFO ] [2025-06-19 17:47:43] T:[] S:[] U:[] [main] ==> Root WebApplicationContext: initialization completed in 25528 ms
[ERROR] [2025-06-19 17:47:47] T:[] S:[] U:[] [main] ==> For security constraints with URL pattern [/*] only the HTTP methods [TRACE HEAD DELETE SEARCH PROPFIND COPY PUT PATCH] are covered. All other methods are uncovered.
[INFO ] [2025-06-19 17:47:48] T:[] S:[] U:[] [main] ==> Redisson 3.15.4
[INFO ] [2025-06-19 17:47:49] T:[] S:[] U:[] [redisson-netty-2-8] ==> 1 connections initialized for /220.197.19.24:30051
[INFO ] [2025-06-19 17:47:49] T:[] S:[] U:[] [redisson-netty-2-13] ==> 5 connections initialized for /220.197.19.24:30051
[INFO ] [2025-06-19 17:47:55] T:[] S:[] U:[] [main] ==> {dataSource-1,application-webadmin} inited
[INFO ] [2025-06-19 17:48:01] T:[] S:[] U:[] [main] ==> {dataSource-2,application-webadmin} inited
[INFO ] [2025-06-19 17:48:06] T:[] S:[] U:[] [main] ==> {dataSource-3,application-webadmin} inited
[INFO ] [2025-06-19 17:48:11] T:[] S:[] U:[] [main] ==> {dataSource-4,application-webadmin} inited
[INFO ] [2025-06-19 17:48:17] T:[] S:[] U:[] [main] ==> {dataSource-5,application-webadmin} inited
[INFO ] [2025-06-19 17:48:18] T:[] S:[] U:[] [main] ==> {dataSource-6,application-webadmin} inited
[WARN ] [2025-06-19 17:48:23] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysDataPermDept".
[WARN ] [2025-06-19 17:48:23] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysDataPermDept ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-06-19 17:48:23] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysDataPermMenu".
[WARN ] [2025-06-19 17:48:23] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysDataPermMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-06-19 17:48:23] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysDataPermUser".
[WARN ] [2025-06-19 17:48:23] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysDataPermUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-06-19 17:48:23] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysDeptRelation".
[WARN ] [2025-06-19 17:48:23] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysDeptRelation ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-06-19 17:48:24] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysRoleMenu".
[WARN ] [2025-06-19 17:48:24] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysRoleMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-06-19 17:48:24] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysUserPost".
[WARN ] [2025-06-19 17:48:24] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysUserPost ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-06-19 17:48:24] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysUserRole".
[WARN ] [2025-06-19 17:48:24] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-06-19 17:48:24] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.mobile.model.MobileEntryDataPerm".
[WARN ] [2025-06-19 17:48:24] T:[] S:[] U:[] [main] ==> class supie.common.mobile.model.MobileEntryDataPerm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-06-19 17:48:24] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.mobile.model.MobileEntryRole".
[WARN ] [2025-06-19 17:48:24] T:[] S:[] U:[] [main] ==> class supie.common.mobile.model.MobileEntryRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-06-19 17:48:24] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.online.model.OnlineColumnRule".
[WARN ] [2025-06-19 17:48:24] T:[] S:[] U:[] [main] ==> class supie.common.online.model.OnlineColumnRule ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-06-19 17:48:26] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.flow.model.FlowTaskExt".
[WARN ] [2025-06-19 17:48:26] T:[] S:[] U:[] [main] ==> class supie.common.flow.model.FlowTaskExt ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-06-19 17:48:27] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.report.model.ReportTenantDataset".
[WARN ] [2025-06-19 17:48:27] T:[] S:[] U:[] [main] ==> class supie.common.report.model.ReportTenantDataset ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[INFO ] [2025-06-19 17:48:32] T:[] S:[] U:[] [main] ==> 创建任务线程池: 核心线程数=8, 最大线程数=16
[INFO ] [2025-06-19 17:48:49] T:[] S:[] U:[] [background-preinit] ==> HV000001: Hibernate Validator 8.0.1.Final
[INFO ] [2025-06-19 17:48:49] T:[] S:[] U:[] [main] ==> Starting WebAdminApplication using Java 17.0.4 with PID 32496 (D:\gy_sch_be\application-webadmin\target\classes started by Superhero in D:\gy_sch_be)
[INFO ] [2025-06-19 17:48:49] T:[] S:[] U:[] [main] ==> The following 1 profile is active: "test"
[INFO ] [2025-06-19 17:48:56] T:[] S:[] U:[] [main] ==> Multiple Spring Data modules found, entering strict repository configuration mode
[INFO ] [2025-06-19 17:48:56] T:[] S:[] U:[] [main] ==> Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[INFO ] [2025-06-19 17:48:56] T:[] S:[] U:[] [main] ==> Finished Spring Data repository scanning in 86 ms. Found 0 Redis repository interfaces.
[INFO ] [2025-06-19 17:48:57] T:[] S:[] U:[] [main] ==> Bean 'commonWebMvcConfig' of type [supie.common.core.config.CommonWebMvcConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO ] [2025-06-19 17:49:00] T:[] S:[] U:[] [main] ==> Tomcat initialized with port(s): 8082 (http)
[INFO ] [2025-06-19 17:49:00] T:[] S:[] U:[] [main] ==> Initializing ProtocolHandler ["http-nio-8082"]
[INFO ] [2025-06-19 17:49:00] T:[] S:[] U:[] [main] ==> Starting service [Tomcat]
[INFO ] [2025-06-19 17:49:00] T:[] S:[] U:[] [main] ==> Starting Servlet engine: [Apache Tomcat/10.1.16]
[INFO ] [2025-06-19 17:49:01] T:[] S:[] U:[] [main] ==> Initializing Spring embedded WebApplicationContext
[INFO ] [2025-06-19 17:49:01] T:[] S:[] U:[] [main] ==> Root WebApplicationContext: initialization completed in 10688 ms
[ERROR] [2025-06-19 17:49:02] T:[] S:[] U:[] [main] ==> For security constraints with URL pattern [/*] only the HTTP methods [TRACE HEAD DELETE SEARCH PROPFIND COPY PUT PATCH] are covered. All other methods are uncovered.
[INFO ] [2025-06-19 17:49:02] T:[] S:[] U:[] [main] ==> Redisson 3.15.4
[INFO ] [2025-06-19 17:49:03] T:[] S:[] U:[] [redisson-netty-2-8] ==> 1 connections initialized for /220.197.19.28:40051
[INFO ] [2025-06-19 17:49:03] T:[] S:[] U:[] [redisson-netty-2-13] ==> 5 connections initialized for /220.197.19.28:40051
[INFO ] [2025-06-19 17:49:07] T:[] S:[] U:[] [main] ==> {dataSource-1,application-webadmin} inited
[INFO ] [2025-06-19 17:49:10] T:[] S:[] U:[] [main] ==> {dataSource-2,application-webadmin} inited
[INFO ] [2025-06-19 17:49:14] T:[] S:[] U:[] [main] ==> {dataSource-3,application-webadmin} inited
[INFO ] [2025-06-19 17:49:16] T:[] S:[] U:[] [main] ==> {dataSource-4,application-webadmin} inited
[INFO ] [2025-06-19 17:49:19] T:[] S:[] U:[] [main] ==> {dataSource-5,application-webadmin} inited
[INFO ] [2025-06-19 17:49:20] T:[] S:[] U:[] [main] ==> {dataSource-6,application-webadmin} inited
[WARN ] [2025-06-19 17:49:26] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysDataPermDept".
[WARN ] [2025-06-19 17:49:26] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysDataPermDept ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-06-19 17:49:26] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysDataPermMenu".
[WARN ] [2025-06-19 17:49:26] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysDataPermMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-06-19 17:49:26] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysDataPermUser".
[WARN ] [2025-06-19 17:49:26] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysDataPermUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-06-19 17:49:26] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysDeptRelation".
[WARN ] [2025-06-19 17:49:26] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysDeptRelation ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-06-19 17:49:27] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysRoleMenu".
[WARN ] [2025-06-19 17:49:27] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysRoleMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-06-19 17:49:27] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysUserPost".
[WARN ] [2025-06-19 17:49:27] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysUserPost ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-06-19 17:49:27] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysUserRole".
[WARN ] [2025-06-19 17:49:27] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-06-19 17:49:27] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.mobile.model.MobileEntryDataPerm".
[WARN ] [2025-06-19 17:49:27] T:[] S:[] U:[] [main] ==> class supie.common.mobile.model.MobileEntryDataPerm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-06-19 17:49:27] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.mobile.model.MobileEntryRole".
[WARN ] [2025-06-19 17:49:27] T:[] S:[] U:[] [main] ==> class supie.common.mobile.model.MobileEntryRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-06-19 17:49:27] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.online.model.OnlineColumnRule".
[WARN ] [2025-06-19 17:49:27] T:[] S:[] U:[] [main] ==> class supie.common.online.model.OnlineColumnRule ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-06-19 17:49:29] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.flow.model.FlowTaskExt".
[WARN ] [2025-06-19 17:49:29] T:[] S:[] U:[] [main] ==> class supie.common.flow.model.FlowTaskExt ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-06-19 17:49:30] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.report.model.ReportTenantDataset".
[WARN ] [2025-06-19 17:49:30] T:[] S:[] U:[] [main] ==> class supie.common.report.model.ReportTenantDataset ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[INFO ] [2025-06-19 17:49:36] T:[] S:[] U:[] [main] ==> 创建任务线程池: 核心线程数=8, 最大线程数=16
[INFO ] [2025-06-19 17:50:08] T:[] S:[] U:[] [main] ==> No deployment resources were found for autodeployment
[INFO ] [2025-06-19 17:50:09] T:[] S:[] U:[] [main] ==> No deployment resources were found for autodeployment
[INFO ] [2025-06-19 17:50:11] T:[] S:[] U:[] [main] ==> Found 1 auto-discoverable Process Engine Configurator
[INFO ] [2025-06-19 17:50:11] T:[] S:[] U:[] [main] ==> Found 3 Engine Configurators in total:
[INFO ] [2025-06-19 17:50:11] T:[] S:[] U:[] [main] ==> class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-06-19 17:50:11] T:[] S:[] U:[] [main] ==> class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
[INFO ] [2025-06-19 17:50:11] T:[] S:[] U:[] [main] ==> class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
[INFO ] [2025-06-19 17:50:11] T:[] S:[] U:[] [main] ==> Executing beforeInit() of class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-06-19 17:50:11] T:[] S:[] U:[] [main] ==> Executing beforeInit() of class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
[INFO ] [2025-06-19 17:50:11] T:[] S:[] U:[] [main] ==> Executing beforeInit() of class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
[INFO ] [2025-06-19 17:50:14] T:[] S:[] U:[] [main] ==> Executing configure() of class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-06-19 17:50:14] T:[] S:[] U:[] [main] ==> Executing configure() of class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
[INFO ] [2025-06-19 17:50:14] T:[] S:[] U:[] [main] ==> Found 1 auto-discoverable Process Engine Configurator
[INFO ] [2025-06-19 17:50:14] T:[] S:[] U:[] [main] ==> Found 1 Engine Configurators in total:
[INFO ] [2025-06-19 17:50:14] T:[] S:[] U:[] [main] ==> class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-06-19 17:50:14] T:[] S:[] U:[] [main] ==> Executing beforeInit() of class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-06-19 17:50:14] T:[] S:[] U:[] [main] ==> Executing configure() of class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-06-19 17:50:15] T:[] S:[] U:[] [main] ==> INFO: An older version of the XSD is specified in one or more changelog's <databaseChangeLog> header. This can lead to unexpected outcomes. If a specific XSD is not required, please replace all XSD version references with "-latest". Learn more at https://docs.liquibase.com
[INFO ] [2025-06-19 17:50:17] T:[] S:[] U:[] [main] ==> Reading from gy_sch_dev.FLW_EV_DATABASECHANGELOG
[INFO ] [2025-06-19 17:50:17] T:[] S:[] U:[] [main] ==> Changelog query completed.
[INFO ] [2025-06-19 17:50:17] T:[] S:[] U:[] [main] ==> EventRegistryEngine default created
[INFO ] [2025-06-19 17:50:17] T:[] S:[] U:[] [main] ==> Executing configure() of class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
[INFO ] [2025-06-19 17:50:17] T:[] S:[] U:[] [main] ==> IdmEngine default created
[INFO ] [2025-06-19 17:50:18] T:[] S:[] U:[] [main] ==> ProcessEngine default created
[INFO ] [2025-06-19 17:50:18] T:[] S:[] U:[] [main] ==> Total of v5 deployments found: 0
[INFO ] [2025-06-19 17:50:54] T:[] S:[] U:[] [main] ==> Exposing 14 endpoint(s) beneath base path '/actuator'
[INFO ] [2025-06-19 17:50:55] T:[] S:[] U:[] [main] ==> Starting ProtocolHandler ["http-nio-8082"]
[WARN ] [2025-06-19 17:50:55] T:[] S:[] U:[] [main] ==> Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
[INFO ] [2025-06-19 17:50:55] T:[] S:[] U:[] [main] ==> 正在关闭线程池: taskPoolExecutor
[INFO ] [2025-06-19 17:50:55] T:[] S:[] U:[] [main] ==> 线程池已关闭: taskPoolExecutor
[INFO ] [2025-06-19 17:50:55] T:[] S:[] U:[] [main] ==> {dataSource-6} closing ...
[INFO ] [2025-06-19 17:50:55] T:[] S:[] U:[] [main] ==> {dataSource-6} closed
[INFO ] [2025-06-19 17:50:55] T:[] S:[] U:[] [main] ==> {dataSource-5} closing ...
[ERROR] [2025-06-19 17:50:55] T:[] S:[] U:[] [main] ==> unregister mbean error
javax.management.InstanceNotFoundException: com.alibaba.druid:type=DruidDataSource,id=application-webadmin
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.getMBean(DefaultMBeanServerInterceptor.java:1088)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.exclusiveUnregisterMBean(DefaultMBeanServerInterceptor.java:423)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.unregisterMBean(DefaultMBeanServerInterceptor.java:411)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.unregisterMBean(JmxMBeanServer.java:547)
	at com.alibaba.druid.stat.DruidDataSourceStatManager.removeDataSource(DruidDataSourceStatManager.java:191)
	at com.alibaba.druid.pool.DruidDataSource$2.run(DruidDataSource.java:2237)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:318)
	at com.alibaba.druid.pool.DruidDataSource.unregisterMbean(DruidDataSource.java:2234)
	at com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2191)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:221)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1189)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1182)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1118)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:629)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:738)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:440)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at supie.webadmin.WebAdminApplication.main(WebAdminApplication.java:24)
[INFO ] [2025-06-19 17:50:55] T:[] S:[] U:[] [main] ==> {dataSource-5} closed
[INFO ] [2025-06-19 17:50:55] T:[] S:[] U:[] [main] ==> {dataSource-4} closing ...
[ERROR] [2025-06-19 17:51:06] T:[] S:[] U:[] [main] ==> unregister mbean error
javax.management.InstanceNotFoundException: com.alibaba.druid:type=DruidDataSource,id=application-webadmin
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.getMBean(DefaultMBeanServerInterceptor.java:1088)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.exclusiveUnregisterMBean(DefaultMBeanServerInterceptor.java:423)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.unregisterMBean(DefaultMBeanServerInterceptor.java:411)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.unregisterMBean(JmxMBeanServer.java:547)
	at com.alibaba.druid.stat.DruidDataSourceStatManager.removeDataSource(DruidDataSourceStatManager.java:191)
	at com.alibaba.druid.pool.DruidDataSource$2.run(DruidDataSource.java:2237)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:318)
	at com.alibaba.druid.pool.DruidDataSource.unregisterMbean(DruidDataSource.java:2234)
	at com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2191)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:221)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1189)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1182)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1118)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:629)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:738)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:440)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at supie.webadmin.WebAdminApplication.main(WebAdminApplication.java:24)
[INFO ] [2025-06-19 17:51:06] T:[] S:[] U:[] [main] ==> {dataSource-4} closed
[INFO ] [2025-06-19 17:51:06] T:[] S:[] U:[] [main] ==> {dataSource-3} closing ...
[ERROR] [2025-06-19 17:51:06] T:[] S:[] U:[] [main] ==> unregister mbean error
javax.management.InstanceNotFoundException: com.alibaba.druid:type=DruidDataSource,id=application-webadmin
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.getMBean(DefaultMBeanServerInterceptor.java:1088)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.exclusiveUnregisterMBean(DefaultMBeanServerInterceptor.java:423)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.unregisterMBean(DefaultMBeanServerInterceptor.java:411)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.unregisterMBean(JmxMBeanServer.java:547)
	at com.alibaba.druid.stat.DruidDataSourceStatManager.removeDataSource(DruidDataSourceStatManager.java:191)
	at com.alibaba.druid.pool.DruidDataSource$2.run(DruidDataSource.java:2237)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:318)
	at com.alibaba.druid.pool.DruidDataSource.unregisterMbean(DruidDataSource.java:2234)
	at com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2191)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:221)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1189)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1182)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1118)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:629)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:738)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:440)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at supie.webadmin.WebAdminApplication.main(WebAdminApplication.java:24)
[INFO ] [2025-06-19 17:51:06] T:[] S:[] U:[] [main] ==> {dataSource-3} closed
[INFO ] [2025-06-19 17:51:06] T:[] S:[] U:[] [main] ==> {dataSource-2} closing ...
[ERROR] [2025-06-19 17:51:16] T:[] S:[] U:[] [main] ==> unregister mbean error
javax.management.InstanceNotFoundException: com.alibaba.druid:type=DruidDataSource,id=application-webadmin
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.getMBean(DefaultMBeanServerInterceptor.java:1088)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.exclusiveUnregisterMBean(DefaultMBeanServerInterceptor.java:423)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.unregisterMBean(DefaultMBeanServerInterceptor.java:411)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.unregisterMBean(JmxMBeanServer.java:547)
	at com.alibaba.druid.stat.DruidDataSourceStatManager.removeDataSource(DruidDataSourceStatManager.java:191)
	at com.alibaba.druid.pool.DruidDataSource$2.run(DruidDataSource.java:2237)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:318)
	at com.alibaba.druid.pool.DruidDataSource.unregisterMbean(DruidDataSource.java:2234)
	at com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2191)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:221)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1189)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1182)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1118)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:629)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:738)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:440)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at supie.webadmin.WebAdminApplication.main(WebAdminApplication.java:24)
[INFO ] [2025-06-19 17:51:16] T:[] S:[] U:[] [main] ==> {dataSource-2} closed
[INFO ] [2025-06-19 17:51:16] T:[] S:[] U:[] [main] ==> {dataSource-1} closing ...
[ERROR] [2025-06-19 17:51:47] T:[] S:[] U:[] [main] ==> unregister mbean error
javax.management.InstanceNotFoundException: com.alibaba.druid:type=DruidDataSource,id=application-webadmin
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.getMBean(DefaultMBeanServerInterceptor.java:1088)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.exclusiveUnregisterMBean(DefaultMBeanServerInterceptor.java:423)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.unregisterMBean(DefaultMBeanServerInterceptor.java:411)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.unregisterMBean(JmxMBeanServer.java:547)
	at com.alibaba.druid.stat.DruidDataSourceStatManager.removeDataSource(DruidDataSourceStatManager.java:191)
	at com.alibaba.druid.pool.DruidDataSource$2.run(DruidDataSource.java:2237)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:318)
	at com.alibaba.druid.pool.DruidDataSource.unregisterMbean(DruidDataSource.java:2234)
	at com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2191)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:221)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1189)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1182)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1118)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:629)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:738)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:440)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at supie.webadmin.WebAdminApplication.main(WebAdminApplication.java:24)
[INFO ] [2025-06-19 17:51:47] T:[] S:[] U:[] [main] ==> {dataSource-1} closed
[INFO ] [2025-06-19 17:51:47] T:[] S:[] U:[] [main] ==> Pausing ProtocolHandler ["http-nio-8082"]
[INFO ] [2025-06-19 17:51:47] T:[] S:[] U:[] [main] ==> Stopping service [Tomcat]
[INFO ] [2025-06-19 17:51:47] T:[] S:[] U:[] [main] ==> Stopping ProtocolHandler ["http-nio-8082"]
[INFO ] [2025-06-19 17:51:47] T:[] S:[] U:[] [main] ==> Destroying ProtocolHandler ["http-nio-8082"]
[INFO ] [2025-06-19 17:51:48] T:[] S:[] U:[] [main] ==> 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
[ERROR] [2025-06-19 17:51:48] T:[] S:[] U:[] [main] ==> 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8082 was already in use.

Action:

Identify and stop the process that's listening on port 8082 or configure this application to listen on another port.

[INFO ] [2025-06-19 17:52:46] T:[] S:[] U:[] [background-preinit] ==> HV000001: Hibernate Validator 8.0.1.Final
[INFO ] [2025-06-19 17:52:46] T:[] S:[] U:[] [main] ==> Starting WebAdminApplication using Java 17.0.4 with PID 24432 (D:\gy_sch_be\application-webadmin\target\classes started by Superhero in D:\gy_sch_be)
[INFO ] [2025-06-19 17:52:46] T:[] S:[] U:[] [main] ==> The following 1 profile is active: "test"
[INFO ] [2025-06-19 17:52:54] T:[] S:[] U:[] [main] ==> Multiple Spring Data modules found, entering strict repository configuration mode
[INFO ] [2025-06-19 17:52:54] T:[] S:[] U:[] [main] ==> Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[INFO ] [2025-06-19 17:52:54] T:[] S:[] U:[] [main] ==> Finished Spring Data repository scanning in 128 ms. Found 0 Redis repository interfaces.
[INFO ] [2025-06-19 17:52:57] T:[] S:[] U:[] [main] ==> Bean 'commonWebMvcConfig' of type [supie.common.core.config.CommonWebMvcConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO ] [2025-06-19 17:53:00] T:[] S:[] U:[] [main] ==> Tomcat initialized with port(s): 8084 (http)
[INFO ] [2025-06-19 17:53:00] T:[] S:[] U:[] [main] ==> Initializing ProtocolHandler ["http-nio-8084"]
[INFO ] [2025-06-19 17:53:00] T:[] S:[] U:[] [main] ==> Starting service [Tomcat]
[INFO ] [2025-06-19 17:53:00] T:[] S:[] U:[] [main] ==> Starting Servlet engine: [Apache Tomcat/10.1.16]
[INFO ] [2025-06-19 17:53:01] T:[] S:[] U:[] [main] ==> Initializing Spring embedded WebApplicationContext
[INFO ] [2025-06-19 17:53:01] T:[] S:[] U:[] [main] ==> Root WebApplicationContext: initialization completed in 13820 ms
[ERROR] [2025-06-19 17:53:02] T:[] S:[] U:[] [main] ==> For security constraints with URL pattern [/*] only the HTTP methods [TRACE HEAD DELETE SEARCH PROPFIND COPY PUT PATCH] are covered. All other methods are uncovered.
[INFO ] [2025-06-19 17:53:03] T:[] S:[] U:[] [main] ==> Redisson 3.15.4
[INFO ] [2025-06-19 17:53:06] T:[] S:[] U:[] [redisson-netty-2-10] ==> 1 connections initialized for /220.197.19.28:40051
[INFO ] [2025-06-19 17:53:06] T:[] S:[] U:[] [redisson-netty-2-13] ==> 5 connections initialized for /220.197.19.28:40051
[INFO ] [2025-06-19 17:53:13] T:[] S:[] U:[] [main] ==> {dataSource-1,application-webadmin} inited
[INFO ] [2025-06-19 17:53:17] T:[] S:[] U:[] [main] ==> {dataSource-2,application-webadmin} inited
[INFO ] [2025-06-19 17:53:22] T:[] S:[] U:[] [main] ==> {dataSource-3,application-webadmin} inited
[INFO ] [2025-06-19 17:53:28] T:[] S:[] U:[] [main] ==> {dataSource-4,application-webadmin} inited
[INFO ] [2025-06-19 17:53:33] T:[] S:[] U:[] [main] ==> {dataSource-5,application-webadmin} inited
[INFO ] [2025-06-19 17:53:36] T:[] S:[] U:[] [main] ==> {dataSource-6,application-webadmin} inited
[WARN ] [2025-06-19 17:53:49] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysDataPermDept".
[WARN ] [2025-06-19 17:53:50] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysDataPermDept ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-06-19 17:53:50] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysDataPermMenu".
[WARN ] [2025-06-19 17:53:50] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysDataPermMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-06-19 17:53:50] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysDataPermUser".
[WARN ] [2025-06-19 17:53:50] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysDataPermUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-06-19 17:53:53] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysDeptRelation".
[WARN ] [2025-06-19 17:53:53] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysDeptRelation ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-06-19 17:53:53] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysRoleMenu".
[WARN ] [2025-06-19 17:53:53] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysRoleMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-06-19 17:53:53] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysUserPost".
[WARN ] [2025-06-19 17:53:53] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysUserPost ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-06-19 17:53:53] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysUserRole".
[WARN ] [2025-06-19 17:53:53] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-06-19 17:53:54] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.mobile.model.MobileEntryDataPerm".
[WARN ] [2025-06-19 17:53:54] T:[] S:[] U:[] [main] ==> class supie.common.mobile.model.MobileEntryDataPerm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-06-19 17:53:54] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.mobile.model.MobileEntryRole".
[WARN ] [2025-06-19 17:53:54] T:[] S:[] U:[] [main] ==> class supie.common.mobile.model.MobileEntryRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-06-19 17:53:54] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.online.model.OnlineColumnRule".
[WARN ] [2025-06-19 17:53:54] T:[] S:[] U:[] [main] ==> class supie.common.online.model.OnlineColumnRule ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-06-19 17:53:55] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.flow.model.FlowTaskExt".
[WARN ] [2025-06-19 17:53:55] T:[] S:[] U:[] [main] ==> class supie.common.flow.model.FlowTaskExt ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-06-19 17:53:56] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.report.model.ReportTenantDataset".
[WARN ] [2025-06-19 17:53:56] T:[] S:[] U:[] [main] ==> class supie.common.report.model.ReportTenantDataset ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[INFO ] [2025-06-19 17:54:07] T:[] S:[] U:[] [main] ==> 创建任务线程池: 核心线程数=8, 最大线程数=16
[INFO ] [2025-06-19 17:54:36] T:[] S:[] U:[] [main] ==> No deployment resources were found for autodeployment
[INFO ] [2025-06-19 17:54:38] T:[] S:[] U:[] [main] ==> No deployment resources were found for autodeployment
[INFO ] [2025-06-19 17:54:39] T:[] S:[] U:[] [main] ==> Found 1 auto-discoverable Process Engine Configurator
[INFO ] [2025-06-19 17:54:39] T:[] S:[] U:[] [main] ==> Found 3 Engine Configurators in total:
[INFO ] [2025-06-19 17:54:39] T:[] S:[] U:[] [main] ==> class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-06-19 17:54:39] T:[] S:[] U:[] [main] ==> class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
[INFO ] [2025-06-19 17:54:39] T:[] S:[] U:[] [main] ==> class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
[INFO ] [2025-06-19 17:54:39] T:[] S:[] U:[] [main] ==> Executing beforeInit() of class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-06-19 17:54:39] T:[] S:[] U:[] [main] ==> Executing beforeInit() of class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
[INFO ] [2025-06-19 17:54:39] T:[] S:[] U:[] [main] ==> Executing beforeInit() of class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
[INFO ] [2025-06-19 17:54:42] T:[] S:[] U:[] [main] ==> Executing configure() of class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-06-19 17:54:42] T:[] S:[] U:[] [main] ==> Executing configure() of class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
[INFO ] [2025-06-19 17:54:42] T:[] S:[] U:[] [main] ==> Found 1 auto-discoverable Process Engine Configurator
[INFO ] [2025-06-19 17:54:42] T:[] S:[] U:[] [main] ==> Found 1 Engine Configurators in total:
[INFO ] [2025-06-19 17:54:42] T:[] S:[] U:[] [main] ==> class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-06-19 17:54:42] T:[] S:[] U:[] [main] ==> Executing beforeInit() of class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-06-19 17:54:42] T:[] S:[] U:[] [main] ==> Executing configure() of class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-06-19 17:54:45] T:[] S:[] U:[] [main] ==> INFO: An older version of the XSD is specified in one or more changelog's <databaseChangeLog> header. This can lead to unexpected outcomes. If a specific XSD is not required, please replace all XSD version references with "-latest". Learn more at https://docs.liquibase.com
[INFO ] [2025-06-19 17:54:50] T:[] S:[] U:[] [main] ==> Reading from gy_sch_dev.FLW_EV_DATABASECHANGELOG
[INFO ] [2025-06-19 17:54:50] T:[] S:[] U:[] [main] ==> Changelog query completed.
[INFO ] [2025-06-19 17:54:51] T:[] S:[] U:[] [main] ==> EventRegistryEngine default created
[INFO ] [2025-06-19 17:54:51] T:[] S:[] U:[] [main] ==> Executing configure() of class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
[INFO ] [2025-06-19 17:54:51] T:[] S:[] U:[] [main] ==> IdmEngine default created
[INFO ] [2025-06-19 17:54:51] T:[] S:[] U:[] [main] ==> ProcessEngine default created
[INFO ] [2025-06-19 17:54:52] T:[] S:[] U:[] [main] ==> Total of v5 deployments found: 0
[INFO ] [2025-06-19 17:56:46] T:[] S:[] U:[] [main] ==> Exposing 14 endpoint(s) beneath base path '/actuator'
[INFO ] [2025-06-19 17:56:52] T:[] S:[] U:[] [main] ==> Starting ProtocolHandler ["http-nio-8084"]
[INFO ] [2025-06-19 17:56:52] T:[] S:[] U:[] [main] ==> Tomcat started on port(s): 8084 (http) with context path ''
[INFO ] [2025-06-19 17:56:54] T:[] S:[] U:[] [main] ==> Started WebAdminApplication in 250.323 seconds (process running for 256.519)
