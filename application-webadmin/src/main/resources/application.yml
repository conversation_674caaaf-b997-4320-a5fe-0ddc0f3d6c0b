#logging:
#  level:
#    # 这里设置的日志级别优先于logback-spring.xml文件Loggers中的日志级别。
#    supie: debug
#    config: classpath:logback-spring.xml
server:
  port: 8082
  tomcat:
    uri-encoding: UTF-8
    threads:
      max: 100
      min-spare: 10
    max-http-form-post-size: 10GB
    max-swallow-size: 10GB
  servlet:
    encoding:
      force: true
      charset: UTF-8
      enabled: true

# spring相关配置
spring:
  application:
    name: application-webadmin
  profiles:
    active: dev
  servlet:
    multipart:
      max-file-size: 10GB
      max-request-size: 10GB # 默认为10GB，如果超过这个值，会报异常。
  mvc:
    converters:
      preferred-json-mapper: fastjson
  main:
    allow-circular-references: true
  groovy:
    template:
      check-template-location: false

flowable:
  async-executor-activate: false
  database-schema-update: false

mybatis-plus:
  mapper-locations: classpath:supie/webadmin/*/dao/mapper/*Mapper.xml,supie/common/log/dao/mapper/*Mapper.xml,supie/common/mobile/dao/mapper/*Mapper.xml,supie/common/online/dao/mapper/*Mapper.xml,supie/common/flow/dao/mapper/*Mapper.xml,supie/common/report/dao/mapper/*Mapper.xml
  type-aliases-package: supie.webadmin.*.model,supie.common.log.model,supie.common.mobile.model,supie.common.online.model,supie.common.flow.model,supie.common.report.model
  global-config:
    db-config:
      logic-delete-value: -1
      logic-not-delete-value: 1

# 自动分页的配置
pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: false
  params: count=countSql

common-core:
  # 可选值为 mysql / postgresql / oracle / dm8 / kingbase / opengauss / sqlserver
  databaseType: mysql

common-mobile:
  # 注意不要以反斜杠(/)结尾。
  urlPrefix: /admin/mobile
  # 移动源数据上传资源路径
  uploadFileBaseDir: ./zz-resource/upload-files/mobile

common-online:
  # 注意不要以反斜杠(/)结尾。
  urlPrefix: /admin/online
  # 打印接口的路径，不要以反斜杠(/)结尾。
  printUrlPath: /admin/report/reportPrint/print
  # 在线表单业务数据上传资源路径
  uploadFileBaseDir: ./zz-resource/upload-files/online
  # 如果为false，在线表单模块中所有Controller接口将不能使用。
  operationEnabled: true
  # 1: minio 2: aliyun-oss 3: qcloud-cos。
  distributeStoreType: 1
  # 调用render接口时候，是否打开一级缓存加速。
  enableRenderCache: false
  # 业务表和在线表单内置表是否跨库。
  enabledMultiDatabaseWrite: true
  # 脱敏字段的掩码字符，只能为单个字符。
  maskChar: '*'
  # 下面的url列表，请保持反斜杠(/)结尾。
  viewUrlList:
  - ${common-online.urlPrefix}/onlineOperation/viewByDatasourceId/
  - ${common-online.urlPrefix}/onlineOperation/viewByOneToManyRelationId/
  - ${common-online.urlPrefix}/onlineOperation/listByDatasourceId/
  - ${common-online.urlPrefix}/onlineOperation/listByOneToManyRelationId/
  - ${common-online.urlPrefix}/onlineOperation/exportByDatasourceId/
  - ${common-online.urlPrefix}/onlineOperation/exportByOneToManyRelationId/
  - ${common-online.urlPrefix}/onlineOperation/downloadDatasource/
  - ${common-online.urlPrefix}/onlineOperation/downloadOneToManyRelation/
  - ${common-online.urlPrefix}/onlineOperation/print/
  editUrlList:
  - ${common-online.urlPrefix}/onlineOperation/addDatasource/
  - ${common-online.urlPrefix}/onlineOperation/addOneToManyRelation/
  - ${common-online.urlPrefix}/onlineOperation/updateDatasource/
  - ${common-online.urlPrefix}/onlineOperation/updateDatasourceBatch/
  - ${common-online.urlPrefix}/onlineOperation/updateOneToManyRelation/
  - ${common-online.urlPrefix}/onlineOperation/updateOneToManyRelationBatch/
  - ${common-online.urlPrefix}/onlineOperation/deleteDatasource/
  - ${common-online.urlPrefix}/onlineOperation/deleteOneToManyRelation/
  - ${common-online.urlPrefix}/onlineOperation/deleteBatchDatasource/
  - ${common-online.urlPrefix}/onlineOperation/deleteBatchOneToManyRelation/
  - ${common-online.urlPrefix}/onlineOperation/uploadDatasource/
  - ${common-online.urlPrefix}/onlineOperation/uploadOneToManyRelation/
  - ${common-online.urlPrefix}/onlineOperation/importDatasource/
  - ${common-online.urlPrefix}/onlineOperation/importOneToManyRelation/

common-flow:
  # 请慎重修改urlPrefix的缺省配置，注意不要以反斜杠(/)结尾。如必须修改其他路径，请同步修改数据库脚本。
  urlPrefix: /admin/flow
  # 如果为false，流程模块的所有Controller中的接口将不能使用。
  operationEnabled: true
  # 打印接口的路径，不要以反斜杠(/)结尾。
  printUrlPath: /admin/report/reportPrint/print
  # 该值默认是false，如果自动化流程中不需要支持MQ发送的任务，就无需启动RocketMQ，该值保持false即可。
  enabledAutoFlowConsumer: false
  # 消费主题，该主题需要根据自身的实际需求进行修改，主要说明的是，使用前需要确保在RocketMQ中提前创建。
  autoFlowConsumerTopic: flow-consumer-topic
  # 消费者组，该主题需要根据自身的实际需求进行修改，主要说明的是，使用前需要确保在RocketMQ中提前创建。
  autoFlowConsumerGroup: flow-consumer
  # 流程数据上传资源路径
  uploadFileBaseDir: ./zz-resource/upload-files/flow

common-swagger:
  # 工程的基础包名。
  basePackage: supie
  # 工程服务的基础包名。
  serviceBasePackage: supie.webadmin
  title: 橙单单体服务工程
  description: 橙单单体服务工程详情
  version: 1.0
  
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    #operations-sorter: order
  api-docs:
    path: /v3/api-docs
    # 当enabled为false的时候，则可禁用swagger。
    enabled: true
  default-flat-param-object: false

common-datafilter:
  tenant:
    # 对于单体服务，该值始终为false。
    enabled: false
  dataperm:
    enabled: true
    # 在拼接数据权限过滤的SQL时，我们会用到sys_dept_relation表，该表的前缀由此配置项指定。
    # 如果没有前缀，请使用 "" 。
    deptRelationTablePrefix: 
    # 是否在每次执行数据权限查询过滤时，都要进行菜单Id和URL之间的越权验证。如果使用SaToken权限框架，该参数必须为false。
    enableMenuPermVerify: false

# 暴露监控端点
management:
  health:
    redis:
      enabled: false
  endpoints:
    web:
      exposure:
        include: '*'
    jmx:
      exposure:
        include: '*'
  endpoint:
    # 与中间件相关的健康详情也会被展示
    health:
      show-details: always
    configprops:
      # 在/actuator/configprops中，所有包含password的配置，将用 * 隐藏。
      # 如果不想隐藏任何配置项的值，可以直接使用如下被注释的空值。
      # keys-to-sanitize:
      keys-to-sanitize: password
  server:
    base-path: "/"

common-log:
  # 操作日志配置，对应配置文件common-log/OperationLogProperties.java
  operation-log:
    enabled: true
