spring:
  freemarker:
    enabled: true
    suffix: .ftl
    charset: UTF-8
    template-loader-path: classpath:/templates/
    cache: false
    check-template-location: false
  data:
    redis:
      host: *************
      port: 30051
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      # 数据库链接 [主数据源]
      main:
        url: ******************************************************************************************************************************************
        username: lmd
        password: TG13ettjNPfDNM4KoC
      # 默认生成的操作日志数据源配置。
      operation-log:
        url: ******************************************************************************************************************************************
        username: lmd
        password: TG13ettjNPfDNM4KoC
      # 默认生成的全局编码字典数据源配置。
      global-dict:
        url: ******************************************************************************************************************************************
        username: lmd
        password: TG13ettjNPfDNM4KoC
      # 默认生成的工作流及在线表单数据源配置。
      common-flow-online:
        url: ******************************************************************************************************************************************
        username: lmd
        password: TG13ettjNPfDNM4KoC
      # 默认生成的统计打印模块的数据源配置。
      common-report:
        url: ******************************************************************************************************************************************
        username: lmd
        password: TG13ettjNPfDNM4KoC
      clickhome:
        driver-class-name: com.clickhouse.jdbc.ClickHouseDriver
        url: jdbc:ch://*************:40054/gy_sch_dev
        username: root
        password: HGm3KxXS3dEJ66nQ5D3aEc2cySdHVK
      driverClassName: com.mysql.cj.jdbc.Driver
      name: application-webadmin
      initialSize: 10
      minIdle: 10
      maxActive: 50
      maxWait: 60000
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 20
      maxOpenPreparedStatements: 20
      validationQuery: SELECT 'x'
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      filters: stat,wall
      useGlobalDataSourceStat: true
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*,/actuator/*"
      stat-view-servlet:
        enabled: true
        urlPattern: /druid/*
        resetEnable: true


application:
  composeBasePath: /data/applications/task # 容器任务的上传根目录。
  # 初始化密码。
  defaultUserPassword: 123456
  # 缺省的文件上传根目录。
  uploadFileBaseDir: ./zz-resource/upload-files/app
  # 跨域的IP(http://*************:8086)白名单列表，多个IP之间逗号分隔(* 表示全部信任，空白表示禁用跨域信任)。
  credentialIpList: "*"
  # Session的用户和数据权限在Redis中的过期时间(秒)。一定要和sa-token.timeout
  sessionExpiredSeconds: 86400
  # 是否排他登录。
  excludeLogin: false

# 这里仅仅是一个第三方配置的示例，如果没有接入斯三方系统，
# 这里的配置项也不会影响到系统的行为，如果觉得多余，也可以手动删除。
third-party:
  # 第三方系统接入的用户鉴权配置。
  auth:
  - appCode: orange-forms-default
    # 访问第三方系统接口的URL前缀，橙单会根据功能添加接口路径的其余部分，
    # 比如获取用户Token的接口 http://localhost:8083/orangePluginTest/getTokenData
    baseUrl: http://localhost:8083/orangePlugin
    # 第三方应用鉴权的HTTP请求令牌头的KEY。
    tokenHeaderKey: Authorization
    # 第三方返回的用户Token数据的缓存过期时长，单位秒。
    # 如果为0，则不缓存，每次涉及第三方的请求，都会发出http请求，交由第三方验证，这样对系统性能会有影响。
    tokenExpiredSeconds: 60
    # 第三方返回的权限数据的缓存过期时长，单位秒。
    permExpiredSeconds: 86400

# 这里仅仅是一个第三方配置的示例，如果没有接入斯三方系统，
# 这里的配置项也不会影响到系统的行为，如果觉得多余，也可以手动删除。
common-ext:
  urlPrefix: /admin/commonext
  # 这里可以配置多个第三方应用，这里的应用数量，通常会和上面third-party.auth的配置数量一致。
  apps:
    # 应用唯一编码，尽量不要使用中文。
  - appCode: orange-forms-default
    # 业务组件的数据源配置。
    bizWidgetDatasources:
    # 组件的类型，多个类型之间可以逗号分隔。
    - types: upms_user,upms_dept
      # 组件获取列表数据的接口地址。
      listUrl: http://localhost:8083/orangePlugin/listBizWidgetData
      # 组件获取详情数据的接口地址。
      viewUrl: http://localhost:8083/orangePlugin/viewBizWidgetData

common-sequence:
  # Snowflake 分布式Id生成算法所需的WorkNode参数值。
  snowflakeWorkNode: 1

# 存储session数据的Redis，所有服务均需要，因此放到公共配置中。
# 根据实际情况，该Redis也可以用于存储其他数据。
common-redis:
  # redisson的配置。每个服务可以自己的配置文件中覆盖此选项。
  redisson:
    # mode的可用值为，single/cluster/sentinel/master-slave
    mode: single
    # single: 单机模式
    #   address: redis://localhost:6379
    # cluster: 集群模式
    #   每个节点逗号分隔，同时每个节点前必须以redis://开头。
    #   address: redis://localhost:6379,redis://localhost:6378,...
    # sentinel:
    #   每个节点逗号分隔，同时每个节点前必须以redis://开头。
    #   address: redis://localhost:6379,redis://localhost:6378,...
    # master-slave:
    #   每个节点逗号分隔，第一个为主节点，其余为从节点。同时每个节点前必须以redis://开头。
    #   address: redis://localhost:6379,redis://localhost:6378,...
    address: redis://*************:30051
    # 链接超时，单位毫秒。
    timeout: 6000
    # 单位毫秒。分布式锁的超时检测时长。
    # 如果一次锁内操作超该毫秒数，或在释放锁之前异常退出，Redis会在该时长之后主动删除该锁使用的key。
    lockWatchdogTimeout: 60000
    # redis 密码，空可以不填。
    password: dQrtU2SmycrWWf3zhpzQfw0tS
    database: 3
    pool:
      # 连接池数量。
      poolSize: 20
      # 连接池中最小空闲数量。
      minIdle: 5

common-report:
  # 注意不要以反斜杠(/)结尾。
  urlPrefix: /admin/report
  # 统计打印表单业务数据上传资源路径
  uploadFileBaseDir: ./zz-resource/upload-files/report
  # 如果为false，报表模块的所有Controller中的接口将不能使用。
  operationEnabled: true
  # 该配置项仅当打印模板中，打印图片字段时，才会需要。
  # 这里的url配置只是一个示例，并不能保证开箱即用，代码示例和说明可参考common-report模块
  # example包内的ReportExampleController中的代码和详细说明。
  imageDownloadUrl: "http://localhost:8082/admin/report/example/downloadDirectly"
  # 该配置用于报表部分的数据权限过滤功能。
  # 当前数据权限需要获取指定部门Ids的所有下级子部门Ids的时候，会调用该接口。
  dataPermAllChildrenDeptIdUrl: "http://localhost:8082/admin/upms/sysDept/listAllChildDeptIdByParentIds"
  # 业务表和统计打印内置表是否跨库。
  enabledMultiDatabaseWrite: true
  # 当前服务是否为可视化后台服务。
  isVisualization: false
  # 下面的url列表，请保持反斜杠(/)结尾。
  viewUrlList:
  - ${common-report.urlPrefix}/reportOperation/listData/

minio:
  enabled: true
  endpoint: http://*************:30052
  accessKey: minioadmin
  secretKey: eW2dZ9dM8pZ7jS9wB7qY8oI0m
  bucketName: gy-sch-be

sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: Authorization
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: ${application.sessionExpiredSeconds}
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: false
  # 同一账号最大登录数量，-1代表不限 （只有在 isConcurrent=true, isShare=false 时此配置项才有意义）
  max-login-count: -1
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: uuid
  # 是否输出操作日志
  is-log: true
  # 配置 Sa-Token 单独使用的 Redis 连接
  # 修改这里Sa-Token的Redis配置，同时也要修改当前配置文件顶部spring.data.redis的相关配置。
  alone-redis:
    # Redis数据库索引（默认为0）
    database: 0
    # Redis服务器地址
    host: *************
    # Redis服务器连接端口
    port: 30051
    # Redis服务器连接密码（默认为空）
    password: dQrtU2SmycrWWf3zhpzQfw0tS
  is-read-header: true
  is-read-cookie: false

rocketmq:
  name-server: 127.0.0.1:9876
  producer:
    group: OrangeFlowMessageQueue

# 定时器是否开启
scheduling:
  enabled: ture
# sso 客户端 配置
sso:
  server-url: http://***************:31910/admin/upms/sso/redirect
  server-user-url: http:// ***************:31910/admin/upms/sso/validateAndGetUser
  server-logout-url: http:// ***************:31910/admin/upms/login/doLogout
  appCode: app_zhai_3849250176348295
  appName: 大模型运维平台
  app-secret:  demECJr05T8XqD2zAbNOmeoWQW2iYt50
  timeout-seconds: 300
# nginx 配置
nginx:
  network-name:  code-network #  创建nginx 时候指定的网络名 docker create network code-network
  container: code_proxy_nginx # nginx 容器名称
  gateway-url: http://*************:40084/  # nginx 网关地址
  mount-path: /data/applications/lmd-formal/backend/nginx/nginx.conf  # nginx 配置文件路径
