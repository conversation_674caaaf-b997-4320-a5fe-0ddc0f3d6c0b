version: '3.8'
services:
  container0:
    image: ai-training:1.0.0
    container_name: container0
    deploy:
      resources:
        limits:
          cpus: 2.0
          memory: 8G
    restart: always
    devices:
      - "/dev/vdavinci100:/dev/davinci100"
      - "/dev/vdavinci101:/dev/davinci101"
      - "/dev/davinci_manager:/dev/davinci_manager"
      - "/dev/devmm_svm:/dev/devmm_svm"
      - "/dev/hisi_hdc:/dev/hisi_hdc"
    volumes:
      - /usr/local/dcmi:/usr/local/dcmi
      - /usr/local/bin/npu-smi:/usr/local/bin/npu-smi
      - /usr/local/sbin/npu-smi:/usr/local/sbin/npu-smi
      - /usr/local/Ascend/driver:/usr/local/Ascend/driver
      - /usr/local/Ascend/firmware:/usr/local/Ascend/firmware
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime:ro
      - /data/models:/models
      - /data/datasets:/datasets
      - /mnt/data/workspace:/workspace
    ports:
      - "8080:8080"
      - "8081:8081"
    environment:
      - DEVICE_TYPE=NPU
      - DATASET_PATH=/datasets
      - MODEL_PATH=/models
      - LOG_LEVEL=INFO
    command: ["python", "/workspace/train.py", "--epochs=100"]
#      - "python"
#      - "/workspace/train.py"
#      - "--epochs=100"
  container1:
    image: inference-service:2.1.0
    deploy:
      resources:
        limits:
          cpus: 1.0
          memory: 4G
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    restart: unless-stopped
    volumes:
      - /data/models:/models
      - /data/logs:/logs
    ports:
      - "9000:9000"
    environment:
      - DEVICE_TYPE=GPU
      - API_KEY=secret-key-123
      - MODEL_PATH=/models
      - LOG_PATH=/logs
    entrypoint:
      - "/bin/bash"
      - "-c"
      - "cd /app && ./start-service.sh"
  container2:
    image: monitoring-service:latest
    deploy:
      resources:
        limits:

          memory: 1G
    restart: always
    devices:
      - "/dev/npu0:/dev/npu0"
      - "/dev/npu1:/dev/npu1"
      - "/dev/davinci_manager:/dev/davinci_manager"
      - "/dev/devmm_svm:/dev/devmm_svm"
      - "/dev/hisi_hdc:/dev/hisi_hdc"
    volumes:
      - /usr/local/dcmi:/usr/local/dcmi
      - /usr/local/bin/npu-smi:/usr/local/bin/npu-smi
      - /usr/local/sbin/npu-smi:/usr/local/sbin/npu-smi
      - /usr/local/Ascend/driver:/usr/local/Ascend/driver
      - /usr/local/Ascend/firmware:/usr/local/Ascend/firmware
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime:ro
      - /var/log:/var/log
    ports:
      - "3000:3000"
    environment:
      - ALERT_THRESHOLD=90
      - MONITORING_INTERVAL=5s
    privileged: true