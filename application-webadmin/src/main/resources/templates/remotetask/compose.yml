version: '3'
services:
  remotetask:

    image: quay.io/supiedt/coder:ubuntu-python3.10-910-cann8.0.rc2.beta1
    volumes:
      - ./workspace:/workspace
    ports:
      - 43681:9000
    environment:
      - PASSWORD=your_password_or_token
      - SUDO_PASSWORD=your_sudo_password_if_needed
    command:
      - code-server
      - --bind-addr
      - 0.0.0.0:9000
      - /workspace
    networks:
        code-network:
        - aliases:
            - code-taskid # 指定当前容奇玩咯 code-taskid
networks:  # 已经创建好的网路
  code-network: # docker create network code-network
    external: true  #