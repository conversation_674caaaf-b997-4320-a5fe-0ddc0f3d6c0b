version: '3'
services:
  remotetask:
    image: quay.io/supiedt/coder:ubuntu-python3.10-910-cann8.0.rc2.beta1
    container_name: jupyter_remotetask
    volumes:
  
      - ./workspace:/workspace
    ports:
      - 43681:9000
    environment:
      - JUPYTER_ENABLE_LAB=yes
      - JUPYTER_TOKEN=48f38b3ed90f48dc8c275afb1968845d
      - JUPYTER_BASE_URL=/code1935332558959022080
    command: >
      bash -c "jupyter lab --ip=0.0.0.0 --port=8888 --no-browser 
      --NotebookApp.base_url=/code1935332558959022080
      --NotebookApp.allow_origin='*' 
      --NotebookApp.token=48f38b3ed90f48dc8c275afb1968845d"
    restart: unless-stopped
