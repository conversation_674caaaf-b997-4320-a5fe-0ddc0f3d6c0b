services:
  nginx-proxy:
    image: nginx
    container_name: code_proxy_nginx
    ports:
      - "40084:80"
    volumes:
      - /data/applications/lmd-formal/backend/nginx/nginx.conf:/etc/nginx/nginx.conf
      - /data/applications/lmd-formal/backend/nginx/logs:/var/log/nginx  # 挂载日志目录
    networks:
      code-network:
        aliases:
          - nginx-host
    logging:  # 添加容器日志配置
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
networks:
  code-network:
    external: true