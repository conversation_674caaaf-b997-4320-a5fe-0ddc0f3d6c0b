version: '3.8'
services:
<#if containers?? && containers?size gt 0>
    <#list containers as container>
    <#if container.serviceName?? && container.serviceName?has_content>
    ${container.serviceName}:
    <#else>
    container${container?index}:
    </#if>
        <#if container.imageName?? && container.imageVersion?? && container.imageVersion?has_content>
        image: ${container.imageName}:${container.imageVersion}
        <#elseif container.imageName??>
        image: ${container.imageName}
        </#if>
        <#if (container.cpuLimit?? && container.cpuLimit?has_content && container.cpuLimit != "null") || (container.memoryLimit?? && container.memoryLimit?has_content && container.memoryLimit != "M" && container.memoryLimit != "null") || (container.resourceType?? && container.resourceType == "gpu" && container.gpuDevices?? && container.gpuDevices?size gt 0)>
        deploy:
            resources:
                <#if (container.cpuLimit?? && container.cpuLimit?has_content && container.cpuLimit != "null") || (container.memoryLimit?? && container.memoryLimit?has_content && container.memoryLimit != "M" && container.memoryLimit != "null")>
                limits:
                    <#if container.cpuLimit?? && container.cpuLimit?has_content && container.cpuLimit != "null">
                    cpus: ${container.cpuLimit}
                    </#if>
                    <#if container.memoryLimit?? && container.memoryLimit?has_content && container.memoryLimit != "M" && container.memoryLimit != "null">
                    memory: ${container.memoryLimit} M
                    </#if>
                </#if>
                <#if container.resourceType?? && container.resourceType == "gpu" && container.gpuDevices?? && container.gpuDevices?size gt 0>
                reservations:
                    devices:
                        <#list container.gpuDevices as gpu>
                        - driver: nvidia
                          count: ${gpu.count!"1"}
                          capabilities: [${gpu.capabilities!"gpu"}]
                        </#list>
                </#if>
        </#if>
        <#if container.restart?? && container.restart?has_content && container.restart != "null">
        restart: ${container.restart}
        </#if>
        <#if container.resourceType?? && container.resourceType == "physical">
        devices:
            <#if container.devices?? && container.devices?size gt 0>
                <#list container.devices as device>
            - "${device}"
                </#list>
            </#if>
            - "/dev/davinci_manager:/dev/davinci_manager"
            - "/dev/devmm_svm:/dev/devmm_svm"
            - "/dev/hisi_hdc:/dev/hisi_hdc"
        <#elseif container.resourceType?? && container.resourceType == "vnpu">
        devices:
            <#if container.vnpuDevices?? && container.vnpuDevices?size gt 0>
                <#list container.vnpuDevices as vnpuDevice>
            - "${vnpuDevice}"
                </#list>
            </#if>
            - "/dev/davinci_manager:/dev/davinci_manager"
            - "/dev/devmm_svm:/dev/devmm_svm"
            - "/dev/hisi_hdc:/dev/hisi_hdc"
        </#if>
        volumes:
            <#if container.resourceType?? && (container.resourceType == "physical" || container.resourceType == "vnpu")>
            - /usr/local/dcmi:/usr/local/dcmi
            - /usr/local/bin/npu-smi:/usr/local/bin/npu-smi
            - /usr/local/sbin/npu-smi:/usr/local/sbin/npu-smi
            - /usr/local/Ascend/driver:/usr/local/Ascend/driver
            - /usr/local/Ascend/firmware:/usr/local/Ascend/firmware
            - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime:ro
            </#if>
            <#if container.volumes?? && container.volumes?size gt 0>
                <#list container.volumes as volume>
            - ${volume}
                </#list>
            </#if>
            <#if container.containerPath?? && container.containerPath != "null" && container.serverMountPath?? && container.serverMountPath != "null">
            - ${container.serverMountPath}:${container.containerPath}
            </#if>
        <#if container.ports?? && container.ports?size gt 0>
        ports:
            <#list container.ports as port>
            - "${port.host}:${port.container}"
            </#list>
        </#if>
        <#if container.environment?? && container.environment?size gt 0>
        environment:
            <#list container.environment as key, value>
            - ${key}=${value}
            </#list>
        </#if>
        <#if container.command?? && container.command != "null">
            <#if container.command?is_string>
        command: ${container.command}
            <#else>
        command:
                <#list container.command as cmd>
            - "${cmd}"
                </#list>
            </#if>
        </#if>
        <#if container.entrypoint?? && container.entrypoint?size gt 0>
        entrypoint:
            <#list container.entrypoint as entry>
            - "${entry}"
            </#list>
        </#if>
        <#if container.extraConfig?? && container.extraConfig?has_content && container.extraConfig != "null">
        ${container.extraConfig}
        </#if>
    </#list>
<#else>
    remotetask:
        <#if imageName?? && imageVersion?? && imageVersion?has_content && imageVersion != "null">
        image: ${imageName}:${imageVersion}
        <#elseif imageName?? && imageName != "null">
        image: ${imageName}
        </#if>
        <#if (cpuLimit?? && cpuLimit?has_content && cpuLimit != "null") || (memoryLimit?? && memoryLimit?has_content && memoryLimit != "null")>
        deploy:
            resources:
                limits:
                    <#if cpuLimit?? && cpuLimit?has_content && cpuLimit != "null">
                    cpus: ${cpuLimit}
                    </#if>
                    <#if memoryLimit?? && memoryLimit?has_content && memoryLimit != "null">
                    memory: ${memoryLimit}M
                    </#if>
        </#if>
        <#if resourceType?? && resourceType == "physical">
        devices:
            <#if devices?? && devices?size gt 0>
                <#list devices as device>
            - "${device}"
                </#list>
            </#if>
            - "/dev/davinci_manager:/dev/davinci_manager"
            - "/dev/devmm_svm:/dev/devmm_svm"
            - "/dev/hisi_hdc:/dev/hisi_hdc"
        <#elseif resourceType?? && resourceType == "vnpu">
        devices:
            <#if vnpuDevices?? && vnpuDevices?size gt 0>
                <#list vnpuDevices as vnpuDevice>
            - "${vnpuDevice}"
                </#list>
            </#if>
            - "/dev/davinci_manager:/dev/davinci_manager"
            - "/dev/devmm_svm:/dev/devmm_svm"
            - "/dev/hisi_hdc:/dev/hisi_hdc"
        </#if>
        volumes:
            - /usr/local/dcmi:/usr/local/dcmi
            - /usr/local/bin/npu-smi:/usr/local/bin/npu-smi
            - /usr/local/sbin/npu-smi:/usr/local/sbin/npu-smi
            - /usr/local/Ascend/driver:/usr/local/Ascend/driver
            - /usr/local/Ascend/firmware:/usr/local/Ascend/firmware
            - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime:ro
            <#if containerPath?? && containerPath != "null" && serverMountPath?? && serverMountPath != "null">
            - ${serverMountPath}:${containerPath}
            </#if>
        <#if ports?? && ports?size gt 0>
        ports:
            <#list ports as port>
            - "${port.host}:${port.container}"
            </#list>
        </#if>
        <#if environment?? && environment?size gt 0>
        environment:
            <#list environment as key, value>
            - ${key}=${value}
            </#list>
        </#if>
        <#if command?? && command != "null">
            <#if command?is_string>
        command: ${command}
            <#else>
        command:
                <#list command as cmd>
            - "${cmd}"
                </#list>
            </#if>
        </#if>
</#if>