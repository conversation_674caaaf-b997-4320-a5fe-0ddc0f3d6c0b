services:
<#if containerName?? && containerName?has_content>
  ${containerName}:
<#else>
  remotetask:
</#if>
<#if containerName?? && containerName?has_content>
    container_name: ${containerName}
</#if>
    image:<#if imageName??> ${imageName}</#if>
<#if restart?? && restart?has_content>
    restart: ${restart}
</#if>
<#if cpuLimit?? || memoryLimit??>
    deploy:
      resources:
        limits:
<#if cpuLimit??>
          cpus: ${cpuLimit}
</#if>
<#if memoryLimit??>
          memory: ${memoryLimit}
</#if>
</#if>
<#if resourceType?? && resourceType == "physical">
    devices:
<#if devices??>
<#list devices as device>
      - "${device}"
</#list>
</#if>
      - "/dev/davinci_manager:/dev/davinci_manager"
      - "/dev/devmm_svm:/dev/devmm_svm"
      - "/dev/hisi_hdc:/dev/hisi_hdc"
<#elseif resourceType?? && resourceType == "vnpu">
    devices:
<#if vnpuDevices??>
<#list vnpuDevices as vnpuDevice>
      - "${vnpuDevice}"
</#list>
</#if>
      - "/dev/davinci_manager:/dev/davinci_manager"
      - "/dev/devmm_svm:/dev/devmm_svm"
      - "/dev/hisi_hdc:/dev/hisi_hdc"
</#if>
    volumes:
      - /usr/local/dcmi:/usr/local/dcmi
      - /usr/local/bin/npu-smi:/usr/local/bin/npu-smi
      - /usr/local/sbin/npu-smi:/usr/local/sbin/npu-smi
      - /usr/local/Ascend/driver:/usr/local/Ascend/driver
      - /usr/local/Ascend/firmware:/usr/local/Ascend/firmware
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime:ro
<#if containerPath?? && serverMountPath??>
      - ${serverMountPath}:${containerPath}
</#if>
<#if containerPort?? && hostPort??>
    ports:
<#list hostPort as port>
      - ${port}:${containerPort}
</#list>
</#if>
<#if envVars??>
    environment:
<#list envVars as key, value>
      - ${key}=${value}
</#list>
</#if>
<#if startCommand??>
    command: "${startCommand}"
</#if>

