user  nginx;
worker_processes  auto;

error_log  /var/log/nginx/error.log notice;
pid        /run/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    keepalive_timeout  65;

    #gzip  on;
    include /etc/nginx/conf.d/*.conf;
    # 服务配置
    server {
          #listen       40084;
          #server_name  *************;
          # 实际访问 http://************:7890/code1233455/lab?token=visni0198 方向代理到内部 服务
          # 构建反向代理配置 路由规则code+任务id
          location /code1778389384949 {
             proxy_set_header Host $http_host;
             proxy_set_header Upgrade $http_upgrade;
             proxy_set_header Connection upgrade;
             proxy_set_header Accept-Encoding gzip;
             proxy_pass http://************:42587;
             rewrite ^/code1778389384949(/.*)$ /$1 break;
          }

#location /code1935323446900690944/ {
#proxy_set_header Host $http_host;
#proxy_set_header Upgrade $http_upgrade;
#proxy_set_header Connection upgrade;
#proxy_set_header Accept-Encoding gzip;
#proxy_pass http://************:41580;
#rewrite ^/codes1935323446900690944(/.*)$ /$1 break;
#}




#location /code1935332558959022080/ {
#proxy_set_header Host $http_host;
#proxy_set_header Upgrade $http_upgrade;
#proxy_set_header Connection upgrade;
#proxy_set_header Accept-Encoding gzip;
#proxy_pass http://host.docker.internal:43681;
#rewrite ^/code1935332558959022080(/.*)$ $1 break;
#}

location ~ ^/code1935332558959022080(/|$) {
    proxy_pass http://*************:43618$1$is_args$args;

    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;

    proxy_buffering off;
}

    }
}
