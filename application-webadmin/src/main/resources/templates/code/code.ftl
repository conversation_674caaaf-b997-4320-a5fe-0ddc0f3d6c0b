services:
  coder:
    image:<#if imageName ??> ${imageName}</#if>
    restart: always
    devices:
     <#if devices ??>
       <#list devices as device>
      - "${device}"
        </#list>
      </#if>
      - "/dev/davinci_manager:/dev/davinci_manager"
      - "/dev/devmm_svm:/dev/devmm_svm"
      - "/dev/hisi_hdc:/dev/hisi_hdc"
    volumes:
      - /usr/local/dcmi:/usr/local/dcmi
      - /usr/local/bin/npu-smi:/usr/local/bin/npu-smi
      - /usr/local/sbin/npu-smi:/usr/local/sbin/npu-smi
      - /usr/local/Ascend/driver:/usr/local/Ascend/driver
      - /usr/local/Ascend/firmware:/usr/local/Ascend/firmware
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime:ro
      <#if serverStorePath ??>
      - ./workspace/${serverStorePath}:/workspace
      </#if>
    ports:
    <#if outPort ??>
       - "${outPort}:9000"
    </#if>
