version: '3.8'
services:
  ascend-vnpu:
    ports:
       <#if outPort ??>
        - ${outPort}:9000
       </#if>
    image: <#if imageName ??> ${imageName}</#if>
    container_name: ascend-vnpu-task
    stdin_open: true
    tty: true
    privileged: true
    restart: unless-stopped
    devices:
      <#if devices ??>
        <#list devices as device>
      - "${device}"
        </#list>
      </#if>
      - "/dev/vdavinci102:/dev/davinci102"
      - "/dev/davinci_manager"
      - "/dev/devmm_svm"
      - "/dev/hisi_hdc"
    volumes:
      - "/usr/local/bin/npu-smi:/usr/local/bin/npu-smi:ro"
      - "/home:/home"
      - "/usr/local/Ascend/driver/lib64/common:/usr/local/Ascend/driver/lib64/common:ro"
      - "/usr/local/Ascend/driver/lib64/driver:/usr/local/Ascend/driver/lib64/driver:ro"
      - "/etc/ascend_install.info:/etc/ascend_install.info:ro"
      - "/usr/local/Ascend/driver/version.info:/usr/local/Ascend/driver/version.info:ro"
    environment:
      - LD_LIBRARY_PATH=/usr/local/Ascend/driver/lib64/common:/usr/local/Ascend/driver/lib64/driver
      - ASCEND_VERSION=ascend910
      - TZ=Asia/Shanghai
    command:
      - "/bin/bash"
