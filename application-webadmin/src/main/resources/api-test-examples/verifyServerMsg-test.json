{"description": "服务器连接测试API示例", "endpoint": "POST /admin/app/schResourceInfo/verifyServerMsg", "examples": [{"name": "使用密码认证的连接测试", "request": {"hostIp": "*************", "connectConfigJson": "{\"username\":\"root\",\"password\":\"your_password\",\"port\":22}"}, "expected_response": {"success": true, "data": {"connected": true, "systemInfo": "Linux", "systemVersion": "Ubuntu 20.04.3 LTS", "cpuCoreCount": "16", "totalMemory": "128.0GB", "gpuCount": "6", "message": "连接测试成功"}}}, {"name": "使用私钥认证的连接测试", "request": {"hostIp": "*********", "connectConfigJson": "{\"username\":\"ubuntu\",\"privateKey\":\"-----BEGIN RSA PRIVATE KEY-----\\nMIIEpAIBAAKCAQEA...\\n-----END RSA PRIVATE KEY-----\",\"port\":22}"}, "expected_response": {"success": true, "data": {"connected": true, "systemInfo": "Linux", "systemVersion": "CentOS Linux 7 (Core)", "cpuCoreCount": "32", "totalMemory": "256.0GB", "gpuCount": "8", "message": "连接测试成功"}}}, {"name": "连接失败的情况", "request": {"hostIp": "192.168.1.999", "connectConfigJson": "{\"username\":\"root\",\"password\":\"wrong_password\",\"port\":22}"}, "expected_response": {"success": false, "errorCode": "DATA_VALIDATED_FAILED", "errorMessage": "连接测试失败", "data": {"connected": false, "systemInfo": "连接失败: Auth fail", "message": "连接测试失败"}}}], "request_fields": {"hostIp": "服务器IP地址或域名", "connectConfigJson": "连接配置JSON字符串，包含username、password或privateKey、port等字段"}, "response_fields": {"connected": "是否连接成功", "systemInfo": "操作系统信息", "systemVersion": "系统版本", "cpuCoreCount": "CPU核心数", "totalMemory": "总内存", "gpuCount": "GPU数量", "message": "连接结果消息"}, "curl_example": "curl -X POST 'http://localhost:8080/admin/app/schResourceInfo/verifyServerMsg' \\\n  -H 'Content-Type: application/json' \\\n  -d '{\n    \"hostIp\": \"*************\",\n    \"connectConfigJson\": \"{\\\"username\\\":\\\"root\\\",\\\"password\\\":\\\"your_password\\\",\\\"port\\\":22}\"\n  }'"}