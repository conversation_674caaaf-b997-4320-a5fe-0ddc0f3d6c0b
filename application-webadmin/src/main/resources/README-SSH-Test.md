# SSH服务器连接测试功能说明

## 功能概述

本功能通过SSH连接远程服务器，自动检测服务器的系统信息，包括：
1. 服务器系统信息（操作系统类型）
2. 系统版本
3. CPU核心数
4. 总内存
5. 显卡数量

## API接口

**接口地址**: `POST /admin/app/schResourceInfo/verifyServerMsg`

**请求参数**:
```json
{
  "hostIp": "服务器IP地址",
  "connectConfigJson": "SSH连接配置的JSON字符串"
}
```

**connectConfigJson 格式**:
```json
{
  "username": "SSH用户名",
  "password": "SSH密码",           // 密码认证时使用
  "privateKey": "SSH私钥内容",    // 私钥认证时使用
  "port": 22                      // SSH端口，默认22
}
```

## 使用方式

### 方式1: 密码认证
```json
{
  "hostIp": "*************",
  "connectConfigJson": "{\"username\":\"root\",\"password\":\"your_password\",\"port\":22}"
}
```

### 方式2: 私钥认证
```json
{
  "hostIp": "*************", 
  "connectConfigJson": "{\"username\":\"ubuntu\",\"privateKey\":\"-----BEGIN RSA PRIVATE KEY-----\\nMIIEpAIBAAKCAQEA...\\n-----END RSA PRIVATE KEY-----\",\"port\":22}"
}
```

## 响应格式

### 成功响应
```json
{
  "success": true,
  "data": {
    "connected": true,
    "systemInfo": "Linux",
    "systemVersion": "Ubuntu 20.04.3 LTS",
    "cpuCoreCount": "16",
    "totalMemory": "128.0GB",
    "gpuCount": "6",
    "message": "连接测试成功"
  }
}
```

### 失败响应
```json
{
  "success": false,
  "errorCode": "DATA_VALIDATED_FAILED",
  "errorMessage": "连接测试失败",
  "data": {
    "connected": false,
    "systemInfo": "连接失败: Auth fail",
    "message": "连接测试失败"
  }
}
```

## 实现原理

### SSH连接方式
- 使用 JSch 库建立SSH连接
- 支持密码认证和私钥认证两种方式
- 自动处理连接超时和异常情况

### 系统信息获取
程序会在远程服务器上执行以下命令来获取系统信息：

1. **系统类型**: `uname -s`
2. **系统版本**: 优先读取 `/etc/os-release`，备选方案包括 `/etc/redhat-release` 和 `/etc/lsb-release`
3. **CPU核心数**: `nproc` 或解析 `/proc/cpuinfo`
4. **总内存**: 解析 `/proc/meminfo` 中的 `MemTotal`
5. **GPU数量**: 
   - 优先使用 `nvidia-smi --list-gpus`
   - 备选方案检查 `/proc/driver/nvidia/gpus/` 目录

### 备选脚本方案

项目还提供了一个bash脚本 (`system_info_check.sh`) 作为备选方案，可以直接在服务器上执行：

```bash
bash system_info_check.sh
```

脚本会返回JSON格式的系统信息。

## 安全注意事项

1. **连接配置安全**: 密码和私钥等敏感信息应妥善保护
2. **网络安全**: 建议在安全的网络环境中使用
3. **权限控制**: 确保SSH用户具有适当的系统查看权限
4. **连接超时**: 设置了10秒连接超时和30秒命令执行超时

## 错误处理

- **连接失败**: 网络不通、认证失败等
- **执行失败**: 命令执行权限不足、系统不兼容等
- **解析失败**: 系统信息解析异常

所有错误都会被捕获并返回适当的错误信息。

## 支持的操作系统

- **Linux发行版**: Ubuntu, CentOS, RHEL, Debian, SUSE等
- **其他类Unix系统**: 部分命令可能需要调整

对于不支持的系统或命令，会返回 "Unknown" 作为默认值。 