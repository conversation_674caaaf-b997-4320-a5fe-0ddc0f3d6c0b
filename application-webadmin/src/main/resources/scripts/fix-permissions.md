# 脚本权限问题解决方案

## 问题描述
当执行 `./system_info_check.sh` 时遇到 "Permission denied" 错误。

## 解决方法

### 方法1: 添加执行权限（推荐）
```bash
chmod +x system_info_check.sh.sh
./system_info_check.sh.sh
```

### 方法2: 直接用bash执行
```bash
bash system_info_check.sh.sh
```

### 方法3: 使用sh执行
```bash
sh system_info_check.sh.sh
```

## 一键解决脚本
```bash
# 进入脚本目录
cd gy_sch_be/application-webadmin/src/main/resources/scripts/

# 添加执行权限
chmod +x system_info_check.sh.sh

# 测试执行
./system_info_check.sh.sh
```

## 批量处理多个脚本
```bash
# 给目录下所有.sh脚本添加执行权限
chmod +x *.sh

# 或者递归处理
find . -name "*.sh" -exec chmod +x {} \;
```

## 权限说明

- `chmod +x` 给文件添加执行权限
- `755` 权限：拥有者可读写执行，其他用户可读执行
- `644` 权限：拥有者可读写，其他用户只读

```bash
# 查看文件权限
ls -la system_info_check.sh.sh

# 设置具体权限
chmod 755 system_info_check.sh.sh
```

## 在SSH连接中避免权限问题

Java代码已经更新为使用内联脚本执行，这样可以避免文件权限问题：

- `SshUtil.getServerInfo()` - 使用单独命令执行
- `SshUtil.getServerInfoWithInlineScript()` - 使用内联bash脚本（推荐）
- `SshUtil.getServerInfoWithScript()` - 使用脚本文件执行

推荐使用 `getServerInfoWithInlineScript()` 方法，因为它不依赖文件权限。 