version: '3.8'  # 使用较新的Compose版本以支持设备映射
services:
  ascend-vnpu:
    ports:
      -  ${outPort}:9000
    image: ${imageId}  # 替换为实际 动态闯入
    container_name: ascend-vnpu-task  # 自定义容器名称
    stdin_open: true       # 相当于 -i 参数
    tty: true              # 相当于 -t 参数
    privileged: true       # 需要特权模式访问NPU设备（可选，根据实际需求）
    restart: unless-stopped  # 自动重启策略
    # 关键设备映射 (华为NPU专用)  vdavinci100是申请的虚拟设备
    devices:
      - "/dev/${vdavinci100}:/dev/${davinci100}"   # vNPU虚拟设备映射（容器内重命名）
      - "/dev/${vdavinci101}:/dev/${davinci101}"   # vNPU虚拟设备映射（容器内重命名）
      - "/dev/vdavinci102:/dev/davinci102"   # vNPU虚拟设备映射（容器内重命名）
      - "/dev/davinci_manager"               # NPU管理接口
      - "/dev/devmm_svm"                     # SVM内存管理设备
      - "/dev/hisi_hdc"                      # 华为设备控制接口
    # NPU驱动和工具的必要挂载
    volumes:
      - "/usr/local/bin/npu-smi:/usr/local/bin/npu-smi:ro"  # npu-smi工具 挂载到容器
      - "/home:/home"                          # 挂载宿主机Home目录
      - "/usr/local/Ascend/driver/lib64/common:/usr/local/Ascend/driver/lib64/common:ro"
      - "/usr/local/Ascend/driver/lib64/driver:/usr/local/Ascend/driver/lib64/driver:ro" # 驱动挂载
      - "/etc/ascend_install.info:/etc/ascend_install.info:ro"  # 宿主机安装信息文件挂载到容器
      - "/usr/local/Ascend/driver/version.info:/usr/local/Ascend/driver/version.info:ro" # 宿主机版本信息文件挂载到容器
    # 环境变量配置（根据实际NPU应用需求添加）
    environment:
      - LD_LIBRARY_PATH=/usr/local/Ascend/driver/lib64/common:/usr/local/Ascend/driver/lib64/driver
      - ASCEND_VERSION=ascend910  # 指定Ascend版本
      - TZ=Asia/Shanghai          # 时区设置
    # 执行任务的入口命令（替换为实际任务命令）
    command:
      - "/bin/bash"

