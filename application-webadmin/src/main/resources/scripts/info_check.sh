#!/bin/bash

# 服务器系统信息检测脚本
# 用于获取系统信息、版本、CPU核心数、内存使用情况和NPU信息
# 支持华为昇腾910A/910B等NPU设备，基于npu-smi命令
# 参考: https://support.huawei.com/enterprise/zh/doc/EDOC1100333074/3b20e264

# 设置输出格式为JSON
echo "{"

# 1. 获取系统信息
echo "  \"systemInfo\": \"$(uname -s 2>/dev/null || echo 'Unknown')\","

# 2. 获取系统版本
echo -n "  \"systemVersion\": \""
if [ -f /etc/os-release ]; then
    grep '^PRETTY_NAME=' /etc/os-release | cut -d'=' -f2 | tr -d '"' | tr -d '\n'
elif [ -f /etc/redhat-release ]; then
    cat /etc/redhat-release | tr -d '\n'
elif [ -f /etc/lsb-release ]; then
    grep '^DISTRIB_DESCRIPTION=' /etc/lsb-release | cut -d'=' -f2 | tr -d '"' | tr -d '\n'
else
    uname -sr | tr -d '\n'
fi 2>/dev/null || echo -n 'Unknown'
echo "\","

# 3. 获取CPU核心数
echo "  \"cpuCoreCount\": \"$(nproc 2>/dev/null || grep -c '^processor' /proc/cpuinfo 2>/dev/null || echo 'Unknown')\","

# 4. 获取内存信息（总内存、已使用内存、剩余内存）
echo -n "  \"totalMemory\": \""
if [ -f /proc/meminfo ]; then
    awk '/MemTotal/ {printf "%.1fMB", $2/1024}' /proc/meminfo
else
    echo -n 'Unknown'
fi 2>/dev/null || echo -n 'Unknown'
echo "\","

echo -n "  \"usedMemory\": \""
if [ -f /proc/meminfo ]; then
    awk '
    /MemTotal/ { total = $2 }
    /MemAvailable/ { available = $2 }
    END {
        if (total && available) {
            used = total - available
            printf "%.1fMB", used/1024
        } else {
            print "Unknown"
        }
    }' /proc/meminfo | tr -d '\n'
else
    echo -n 'Unknown'
fi 2>/dev/null || echo -n 'Unknown'
echo "\","

echo -n "  \"availableMemory\": \""
if [ -f /proc/meminfo ]; then
    awk '/MemAvailable/ {printf "%.1fMB", $2/1024}' /proc/meminfo
else
    echo -n 'Unknown'
fi 2>/dev/null || echo -n 'Unknown'
echo "\","

# 5. 获取NPU设备数量（基于npu-smi info -m命令统计NPU ID数量）
echo -n "  \"npuDeviceCount\": \""
if command -v npu-smi >/dev/null 2>&1; then
    # 使用npu-smi info -m获取设备列表，统计唯一的NPU ID数量
    npu-smi info -m 2>/dev/null | awk '
    BEGIN { started = 0 }
    /NPU.*ID.*Chip.*ID/ { started = 1; next }
    started && /^[[:space:]]*[0-9]+/ {
        print $1  # 输出NPU ID
    }' | sort -u | wc -l | tr -d '\n'
elif [ -d /sys/class/accel ]; then
    # 通过系统文件检测NPU设备
    ls /sys/class/accel/ 2>/dev/null | wc -l | tr -d '\n'
else
    echo -n '0'
fi 2>/dev/null || echo -n '0'
echo "\","

# 6. 获取NPU芯片总数（基于npu-smi info -m命令）
echo -n "  \"npuChipCount\": \""
if command -v npu-smi >/dev/null 2>&1; then
    # 使用npu-smi info -m获取芯片信息，格式: NPU ID Chip ID Chip Logic ID Chip Name
    npu-smi info -m 2>/dev/null | awk '
    BEGIN { count = 0; started = 0 }
    /NPU.*ID.*Chip.*ID/ { started = 1; next }
    started && /^[[:space:]]*[0-9]+/ { count++ }
    END { print count }' | tr -d '\n'
else
    echo -n '0'
fi 2>/dev/null || echo -n '0'
echo "\","

# 7. 获取vNPU总数（虚拟NPU数量）
#echo -n "  \"vnpuCount\": \""
#if command -v npu-smi >/dev/null 2>&1; then
#    # 检查是否有vNPU相关信息
#    vnpu_count=$(npu-smi info 2>/dev/null | grep -i "vnpu\|virtual" | wc -l)
#    echo -n "$vnpu_count"
#else
#    echo -n '0'
#fi 2>/dev/null || echo -n '0'
#echo "\","

# 8. 获取NPU总内存（DDR + HBM）
#echo -n "  \"npuTotalMemory\": \""
#if command -v npu-smi >/dev/null 2>&1; then
#    total_memory=0
#
#    # 使用npu-smi info -m获取NPU设备列表
#    npu-smi info -m 2>/dev/null | awk '
#    BEGIN { started = 0 }
#    /NPU.*ID.*Chip.*ID/ { started = 1; next }
#    started && /^[[:space:]]*[0-9]+/ {
#        print $1  # 只输出NPU ID
#    }' | sort -u | while read npu_id; do
#        if [ -n "$npu_id" ]; then
#            # 使用npu-smi info -t memory -i id获取NPU内存信息
#            memory_info=$(npu-smi info -t memory -i "$npu_id" 2>/dev/null)
#
#            # 提取DDR Capacity
#            ddr_capacity=$(echo "$memory_info" | awk '
#            /DDR Capacity\(MB\)/ {
#                if (match($0, /([0-9.]+)/, arr)) {
#                    print arr[1]
#                }
#            }' | head -1)
#
#            # 提取HBM Capacity
#            hbm_capacity=$(echo "$memory_info" | awk '
#            /HBM Capacity\(MB\)/ {
#                if (match($0, /([0-9.]+)/, arr)) {
#                    print arr[1]
#                }
#            }' | head -1)
#
#            # 计算单个NPU的总内存 (DDR + HBM)
#            if [ -n "$ddr_capacity" ] && [ -n "$hbm_capacity" ]; then
#                npu_total=$(echo "$ddr_capacity + $hbm_capacity" | bc 2>/dev/null || echo "0")
#                total_memory=$(echo "$total_memory + $npu_total" | bc 2>/dev/null || echo "$total_memory")
#            fi
#        fi
#    done
#
#    if [ "$total_memory" -gt 0 ] 2>/dev/null; then
#        printf "%.1fMB" "$total_memory"
#    else
#        echo -n "0MB"
#    fi
#else
#    echo -n '0MB'
#fi 2>/dev/null || echo -n '0MB'
#echo "\","

# 9. 获取NPU DDR总容量
#echo -n "  \"npuDdrTotalMemory\": \""
#if command -v npu-smi >/dev/null 2>&1; then
#    ddr_total=0
#
#    # 使用npu-smi info -m获取NPU设备列表
#    npu-smi info -m 2>/dev/null | awk '
#    BEGIN { started = 0 }
#    /NPU.*ID.*Chip.*ID/ { started = 1; next }
#    started && /^[[:space:]]*[0-9]+/ {
#        print $1  # 只输出NPU ID
#    }' | sort -u | while read npu_id; do
#        if [ -n "$npu_id" ]; then
#            # 获取DDR容量
#            ddr_capacity=$(npu-smi info -t memory -i "$npu_id" 2>/dev/null | awk '
#            /DDR Capacity\(MB\)/ {
#                if (match($0, /([0-9.]+)/, arr)) {
#                    print arr[1]
#                }
#            }' | head -1)
#
#            if [ -n "$ddr_capacity" ] && [ "$ddr_capacity" != "0" ]; then
#                ddr_total=$(echo "$ddr_total + $ddr_capacity" | bc 2>/dev/null || echo "$ddr_total")
#            fi
#        fi
#    done
#
#    if [ "$ddr_total" -gt 0 ] 2>/dev/null; then
#        printf "%.1fMB" "$ddr_total"
#    else
#        echo -n "0MB"
#    fi
#else
#    echo -n '0MB'
#fi 2>/dev/null || echo -n '0MB'
#echo "\","

# 10. 获取NPU HBM总容量
#echo -n "  \"npuHbmTotalMemory\": \""
#if command -v npu-smi >/dev/null 2>&1; then
#    hbm_total=0
#
#    # 使用npu-smi info -m获取NPU设备列表
#    npu-smi info -m 2>/dev/null | awk '
#    BEGIN { started = 0 }
#    /NPU.*ID.*Chip.*ID/ { started = 1; next }
#    started && /^[[:space:]]*[0-9]+/ {
#        print $1  # 只输出NPU ID
#    }' | sort -u | while read npu_id; do
#        if [ -n "$npu_id" ]; then
#            # 获取HBM容量
#            hbm_capacity=$(npu-smi info -t memory -i "$npu_id" 2>/dev/null | awk '
#            /HBM Capacity\(MB\)/ {
#                if (match($0, /([0-9.]+)/, arr)) {
#                    print arr[1]
#                }
#            }' | head -1)
#
#            if [ -n "$hbm_capacity" ] && [ "$hbm_capacity" != "0" ]; then
#                hbm_total=$(echo "$hbm_total + $hbm_capacity" | bc 2>/dev/null || echo "$hbm_total")
#            fi
#        fi
#    done
#
#    if [ "$hbm_total" -gt 0 ] 2>/dev/null; then
#        printf "%.1fMB" "$hbm_total"
#    else
#        echo -n "0MB"
#    fi
#else
#    echo -n '0MB'
#fi 2>/dev/null || echo -n '0MB'
#echo "\","

# 11. 获取NPU已使用内存总量（暂时设为0，需要其他命令获取使用率）
#echo -n "  \"npuUsedMemory\": \""
#echo -n "0MB"
#echo "\","

# 12. 获取NPU空闲内存总量（暂时设为总内存，需要其他命令获取使用率）
#echo -n "  \"npuFreeMemory\": \""
#if command -v npu-smi >/dev/null 2>&1; then
#    # 由于当前命令只能获取容量信息，空闲内存暂时等于总内存
#    total_memory=0
#
#    # 使用npu-smi info -m获取NPU设备列表
#    npu-smi info -m 2>/dev/null | awk '
#    BEGIN { started = 0 }
#    /NPU.*ID.*Chip.*ID/ { started = 1; next }
#    started && /^[[:space:]]*[0-9]+/ {
#        print $1  # 只输出NPU ID
#    }' | sort -u | while read npu_id; do
#        if [ -n "$npu_id" ]; then
#            # 使用npu-smi info -t memory -i id获取NPU内存信息
#            memory_info=$(npu-smi info -t memory -i "$npu_id" 2>/dev/null)
#
#            # 提取DDR Capacity
#            ddr_capacity=$(echo "$memory_info" | awk '
#            /DDR Capacity\(MB\)/ {
#                if (match($0, /([0-9.]+)/, arr)) {
#                    print arr[1]
#                }
#            }' | head -1)
#
#            # 提取HBM Capacity
#            hbm_capacity=$(echo "$memory_info" | awk '
#            /HBM Capacity\(MB\)/ {
#                if (match($0, /([0-9.]+)/, arr)) {
#                    print arr[1]
#                }
#            }' | head -1)
#
#            # 计算单个NPU的总内存 (DDR + HBM)
#            if [ -n "$ddr_capacity" ] && [ -n "$hbm_capacity" ]; then
#                npu_total=$(echo "$ddr_capacity + $hbm_capacity" | bc 2>/dev/null || echo "0")
#                total_memory=$(echo "$total_memory + $npu_total" | bc 2>/dev/null || echo "$total_memory")
#            fi
#        fi
#    done
#
#    if [ "$total_memory" -gt 0 ] 2>/dev/null; then
#        printf "%.1fMB" "$total_memory"
#    else
#        echo -n "0MB"
#    fi
#else
#    echo -n '0MB'
#fi 2>/dev/null || echo -n '0MB'
#echo "\","

# 13. 获取NPU设备详细列表（基于npu-smi info -l）
#echo -n "  \"npuDeviceList\": ["
#if command -v npu-smi >/dev/null 2>&1; then
#    npu-smi info -l 2>/dev/null | awk '
#    BEGIN { first = 1; started = 0 }
#    /NPU.*ID/ { started = 1; next }
#    started && /^[[:space:]]*[0-9]+/ {
#        if (!first) printf ","
#        first = 0
#
#        # 提取NPU ID（第一列）
#        npu_id = $1
#
#        # 提取设备名称（剩余部分）
#        device_name = ""
#        for (i = 2; i <= NF; i++) {
#            if (device_name == "") {
#                device_name = $i
#            } else {
#                device_name = device_name " " $i
#            }
#        }
#        if (device_name == "") device_name = "Ascend NPU"
#
#        printf "{\"npuId\": \"%s\", \"deviceName\": \"%s\"}", npu_id, device_name
#    }' | tr -d '\n'
#fi 2>/dev/null
#echo "],"


# 16. 获取服务器健康状态（基于NPU整体健康状态）
#echo -n "  \"serverHealthStatus\": \""
#if command -v npu-smi >/dev/null 2>&1; then
#    # 获取设备数量
#    device_count=$(npu-smi info -l 2>/dev/null | awk '
#    BEGIN { count = 0; started = 0 }
#    /NPU.*ID/ { started = 1; next }
#    started && /^[[:space:]]*[0-9]+/ { count++ }
#    END { print count }')
#
#    if [ "$device_count" -gt 0 ]; then
#        # 检查所有设备的健康状态
#        overall_status="active"
#        error_count=0
#        offline_count=0
#        maintenance_count=0
#
#        # 使用npu-smi info -t health -i id命令查询所有芯片健康状态
#        for i in $(seq 0 $((device_count-1))); do
#            device_health=$(npu-smi info -t health -i "$i" 2>/dev/null | awk '
#            /Health.*Status|Status.*Health/ {
#                if (match($0, /(Error|Fault|Failed)/i)) {
#                    print "error"
#                } else if (match($0, /(Offline|Down)/i)) {
#                    print "offline"
#                } else if (match($0, /(Maintenance|Maintain)/i)) {
#                    print "maintenance"
#                } else if (match($0, /(OK|Normal|Healthy)/i)) {
#                    print "active"
#                } else {
#                    print "unknown"
#                }
#            }' | head -1)
#
#            case "$device_health" in
#                "error") error_count=$((error_count + 1)) ;;
#                "offline") offline_count=$((offline_count + 1)) ;;
#                "maintenance") maintenance_count=$((maintenance_count + 1)) ;;
#            esac
#        done
#
#        # 确定整体状态
#        if [ "$error_count" -gt 0 ]; then
#            overall_status="error"
#        elif [ "$offline_count" -gt 0 ]; then
#            overall_status="offline"
#        elif [ "$maintenance_count" -gt 0 ]; then
#            overall_status="maintenance"
#        else
#            overall_status="active"
#        fi
#
#        echo -n "$overall_status"
#    else
#        echo -n "offline"
#    fi
#else
#    # 如果没有npu-smi命令，检查系统基本状态
#    if [ -f /proc/loadavg ]; then
#        echo -n "active"
#    else
#        echo -n "unknown"
#    fi
#fi 2>/dev/null || echo -n 'unknown'
#echo "\","

# 17. NPU驱动版本信息
#echo -n "  \"npuDriverVersion\": \""
#if command -v npu-smi >/dev/null 2>&1; then
#    npu-smi info 2>/dev/null | grep -i "driver.*version\|version.*driver" | head -1 | awk -F: '{gsub(/^[ \t]+|[ \t]+$/, "", $2); print $2}' | tr -d '\n'
#else
#    echo -n 'Unknown'
#fi 2>/dev/null || echo -n 'Unknown'
#echo "\","

# 18. 兼容性：GPU信息（保留原有逻辑用于兼容）
#echo -n "  \"gpuCount\": \""
#if command -v nvidia-smi >/dev/null 2>&1; then
#    nvidia-smi --list-gpus 2>/dev/null | wc -l | tr -d '\n'
#else
#    echo -n '0'
#fi 2>/dev/null || echo -n '0'
#echo "\","

# 19. 添加时间戳
echo "  \"timestamp\": \"$(date '+%Y-%m-%d %H:%M:%S')\","

# 20. 添加检测状态和NPU工具可用性
echo -n "  \"npuSmiAvailable\": "
if command -v npu-smi >/dev/null 2>&1; then
    echo -n "true"
else
    echo -n "false"
fi
echo ","

echo "  \"success\": true"

echo "}"
