package supie.webadmin.app.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import supie.common.log.annotation.OperationLog;
import supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;
import supie.webadmin.app.dao.SchTaskImageMapper;
import supie.webadmin.app.vo.*;
import supie.webadmin.app.dto.*;
import supie.webadmin.app.model.*;
import supie.webadmin.app.service.*;
import supie.common.core.object.*;
import supie.common.core.util.*;
import supie.common.core.constant.*;
import supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 镜像表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Tag(name = "镜像表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/schTaskImage")
public class SchTaskImageController {

    @Autowired
    private SchTaskImageService schTaskImageService;

    @Resource
    private SchTaskImageMapper schTaskImageMapper;



    @Operation(summary = "imageCount",description = "镜像总数总计、最近镜像更新数、在运行镜像数")
    @GetMapping("/imageCount")
    public ResponseResult<ImageCountVo> imageCount(){
        return ResponseResult.success( schTaskImageMapper.imageCount());
    }

    @Operation(summary = "remoteImageQuery",description = "远程镜像查询(只用于远程调用)")
    @SaIgnore
    @PostMapping("/remoteImageQuery")
    public ResponseResult<MyPageData<SchTaskImageVo>> remoteImageQuery(
            @MyRequestBody SchTaskImageDto schTaskImageDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchTaskImage schTaskImageFilter = MyModelUtil.copyTo(schTaskImageDtoFilter, SchTaskImage.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchTaskImage.class);
        List<SchTaskImage> schTaskImageList = schTaskImageService.getSchTaskImageListWithRelation(schTaskImageFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(schTaskImageList, SchTaskImageVo.class));

    }
        /**
         * 新增镜像表数据。
         *
         * @param schTaskImageDto 新增对象。
         * @return 应答结果对象，包含新增对象主键Id。
         */
    @ApiOperationSupport(ignoreParameters = {
            "schTaskImageDto.id",
            "schTaskImageDto.searchString",
            "schTaskImageDto.updateTimeStart",
            "schTaskImageDto.updateTimeEnd",
            "schTaskImageDto.createTimeStart",
            "schTaskImageDto.createTimeEnd"})
    @SaCheckPermission("schTaskImage.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody SchTaskImageDto schTaskImageDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schTaskImageDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchTaskImage schTaskImage = MyModelUtil.copyTo(schTaskImageDto, SchTaskImage.class);
        schTaskImage = schTaskImageService.saveNew(schTaskImage);
        return ResponseResult.success(schTaskImage.getId());
    }

    /**
     * 更新镜像表数据。
     *
     * @param schTaskImageDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schTaskImageDto.searchString",
            "schTaskImageDto.updateTimeStart",
            "schTaskImageDto.updateTimeEnd",
            "schTaskImageDto.createTimeStart",
            "schTaskImageDto.createTimeEnd"})
    @SaCheckPermission("schTaskImage.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody SchTaskImageDto schTaskImageDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schTaskImageDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchTaskImage schTaskImage = MyModelUtil.copyTo(schTaskImageDto, SchTaskImage.class);
        SchTaskImage originalSchTaskImage = schTaskImageService.getById(schTaskImage.getId());
        if (originalSchTaskImage == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schTaskImageService.update(schTaskImage, originalSchTaskImage)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除镜像表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schTaskImage.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除镜像表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schTaskImage.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的镜像表列表。
     *
     * @param schTaskImageDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schTaskImage.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<SchTaskImageVo>> list(
            @MyRequestBody SchTaskImageDto schTaskImageDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchTaskImage schTaskImageFilter = MyModelUtil.copyTo(schTaskImageDtoFilter, SchTaskImage.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchTaskImage.class);
        List<SchTaskImage> schTaskImageList = schTaskImageService.getSchTaskImageListWithRelation(schTaskImageFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(schTaskImageList, SchTaskImageVo.class));
    }

    /**
     * 分组列出符合过滤条件的镜像表列表。
     *
     * @param schTaskImageDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schTaskImage.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<SchTaskImageVo>> listWithGroup(
            @MyRequestBody SchTaskImageDto schTaskImageDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchTaskImage.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, SchTaskImage.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchTaskImage filter = MyModelUtil.copyTo(schTaskImageDtoFilter, SchTaskImage.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<SchTaskImage> resultList = schTaskImageService.getGroupedSchTaskImageListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, SchTaskImageVo.class));
    }

    /**
     * 查看指定镜像表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @SaCheckPermission("schTaskImage.view")
    @GetMapping("/view")
    public ResponseResult<SchTaskImageVo> view(@RequestParam Long id) {
        SchTaskImage schTaskImage = schTaskImageService.getByIdWithRelation(id, MyRelationParam.full());
        if (schTaskImage == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        SchTaskImageVo schTaskImageVo = MyModelUtil.copyTo(schTaskImage, SchTaskImageVo.class);
        return ResponseResult.success(schTaskImageVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        SchTaskImage originalSchTaskImage = schTaskImageService.getById(id);
        if (originalSchTaskImage == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schTaskImageService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
