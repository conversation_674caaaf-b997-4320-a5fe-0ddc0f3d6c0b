package supie.webadmin.app.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import supie.common.log.annotation.OperationLog;
import supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;
import supie.webadmin.app.vo.*;
import supie.webadmin.app.dto.*;
import supie.webadmin.app.model.*;
import supie.webadmin.app.service.*;
import supie.common.core.object.*;
import supie.common.core.util.*;
import supie.common.core.constant.*;
import supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 当前调度策略表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Tag(name = "当前调度策略表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/schCurrentSchedulingPolicy")
public class SchCurrentSchedulingPolicyController {

    @Autowired
    private SchCurrentSchedulingPolicyService schCurrentSchedulingPolicyService;

    /**
     * 新增当前调度策略表数据。
     *
     * @param schCurrentSchedulingPolicyDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schCurrentSchedulingPolicyDto.id",
            "schCurrentSchedulingPolicyDto.searchString",
            "schCurrentSchedulingPolicyDto.updateTimeStart",
            "schCurrentSchedulingPolicyDto.updateTimeEnd",
            "schCurrentSchedulingPolicyDto.createTimeStart",
            "schCurrentSchedulingPolicyDto.createTimeEnd"})
    @SaCheckPermission("schCurrentSchedulingPolicy.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody SchCurrentSchedulingPolicyDto schCurrentSchedulingPolicyDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schCurrentSchedulingPolicyDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchCurrentSchedulingPolicy schCurrentSchedulingPolicy = MyModelUtil.copyTo(schCurrentSchedulingPolicyDto, SchCurrentSchedulingPolicy.class);
        schCurrentSchedulingPolicy = schCurrentSchedulingPolicyService.saveNew(schCurrentSchedulingPolicy);
        return ResponseResult.success(schCurrentSchedulingPolicy.getId());
    }

    /**
     * 更新当前调度策略表数据。
     *
     * @param schCurrentSchedulingPolicyDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schCurrentSchedulingPolicyDto.searchString",
            "schCurrentSchedulingPolicyDto.updateTimeStart",
            "schCurrentSchedulingPolicyDto.updateTimeEnd",
            "schCurrentSchedulingPolicyDto.createTimeStart",
            "schCurrentSchedulingPolicyDto.createTimeEnd"})
    @SaCheckPermission("schCurrentSchedulingPolicy.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody SchCurrentSchedulingPolicyDto schCurrentSchedulingPolicyDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schCurrentSchedulingPolicyDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchCurrentSchedulingPolicy schCurrentSchedulingPolicy = MyModelUtil.copyTo(schCurrentSchedulingPolicyDto, SchCurrentSchedulingPolicy.class);
        SchCurrentSchedulingPolicy originalSchCurrentSchedulingPolicy = schCurrentSchedulingPolicyService.getById(schCurrentSchedulingPolicy.getId());
        if (originalSchCurrentSchedulingPolicy == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schCurrentSchedulingPolicyService.update(schCurrentSchedulingPolicy, originalSchCurrentSchedulingPolicy)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除当前调度策略表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schCurrentSchedulingPolicy.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除当前调度策略表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schCurrentSchedulingPolicy.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的当前调度策略表列表。
     *
     * @param schCurrentSchedulingPolicyDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schCurrentSchedulingPolicy.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<SchCurrentSchedulingPolicyVo>> list(
            @MyRequestBody SchCurrentSchedulingPolicyDto schCurrentSchedulingPolicyDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchCurrentSchedulingPolicy schCurrentSchedulingPolicyFilter = MyModelUtil.copyTo(schCurrentSchedulingPolicyDtoFilter, SchCurrentSchedulingPolicy.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchCurrentSchedulingPolicy.class);
        List<SchCurrentSchedulingPolicy> schCurrentSchedulingPolicyList =
                schCurrentSchedulingPolicyService.getSchCurrentSchedulingPolicyListWithRelation(schCurrentSchedulingPolicyFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(schCurrentSchedulingPolicyList, SchCurrentSchedulingPolicyVo.class));
    }

    /**
     * 分组列出符合过滤条件的当前调度策略表列表。
     *
     * @param schCurrentSchedulingPolicyDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schCurrentSchedulingPolicy.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<SchCurrentSchedulingPolicyVo>> listWithGroup(
            @MyRequestBody SchCurrentSchedulingPolicyDto schCurrentSchedulingPolicyDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchCurrentSchedulingPolicy.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, SchCurrentSchedulingPolicy.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchCurrentSchedulingPolicy filter = MyModelUtil.copyTo(schCurrentSchedulingPolicyDtoFilter, SchCurrentSchedulingPolicy.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<SchCurrentSchedulingPolicy> resultList = schCurrentSchedulingPolicyService.getGroupedSchCurrentSchedulingPolicyListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, SchCurrentSchedulingPolicyVo.class));
    }

    /**
     * 查看指定当前调度策略表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @SaCheckPermission("schCurrentSchedulingPolicy.view")
    @GetMapping("/view")
    public ResponseResult<SchCurrentSchedulingPolicyVo> view(@RequestParam Long id) {
        SchCurrentSchedulingPolicy schCurrentSchedulingPolicy = schCurrentSchedulingPolicyService.getByIdWithRelation(id, MyRelationParam.full());
        if (schCurrentSchedulingPolicy == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        SchCurrentSchedulingPolicyVo schCurrentSchedulingPolicyVo = MyModelUtil.copyTo(schCurrentSchedulingPolicy, SchCurrentSchedulingPolicyVo.class);
        return ResponseResult.success(schCurrentSchedulingPolicyVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        SchCurrentSchedulingPolicy originalSchCurrentSchedulingPolicy = schCurrentSchedulingPolicyService.getById(id);
        if (originalSchCurrentSchedulingPolicy == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schCurrentSchedulingPolicyService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
