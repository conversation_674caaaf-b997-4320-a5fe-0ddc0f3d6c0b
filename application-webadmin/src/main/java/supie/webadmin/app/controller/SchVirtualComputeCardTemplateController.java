package supie.webadmin.app.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import supie.common.log.annotation.OperationLog;
import supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;
import supie.webadmin.app.vo.*;
import supie.webadmin.app.dto.*;
import supie.webadmin.app.model.*;
import supie.webadmin.app.service.*;
import supie.common.core.object.*;
import supie.common.core.util.*;
import supie.common.core.constant.*;
import supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * vpu模板表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Tag(name = "vpu模板表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/schVirtualComputeCardTemplate")
public class SchVirtualComputeCardTemplateController {

    @Autowired
    private SchVirtualComputeCardTemplateService schVirtualComputeCardTemplateService;

    /**
     * 新增vpu模板表数据。
     *
     * @param schVirtualComputeCardTemplateDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schVirtualComputeCardTemplateDto.id",
            "schVirtualComputeCardTemplateDto.searchString",
            "schVirtualComputeCardTemplateDto.createTimeStart",
            "schVirtualComputeCardTemplateDto.createTimeEnd",
            "schVirtualComputeCardTemplateDto.updateTimeStart",
            "schVirtualComputeCardTemplateDto.updateTimeEnd"})
    @SaCheckPermission("schVirtualComputeCardTemplate.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody SchVirtualComputeCardTemplateDto schVirtualComputeCardTemplateDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schVirtualComputeCardTemplateDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchVirtualComputeCardTemplate schVirtualComputeCardTemplate = MyModelUtil.copyTo(schVirtualComputeCardTemplateDto, SchVirtualComputeCardTemplate.class);
        schVirtualComputeCardTemplate = schVirtualComputeCardTemplateService.saveNew(schVirtualComputeCardTemplate);
        return ResponseResult.success(schVirtualComputeCardTemplate.getId());
    }

    /**
     * 更新vpu模板表数据。
     *
     * @param schVirtualComputeCardTemplateDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schVirtualComputeCardTemplateDto.searchString",
            "schVirtualComputeCardTemplateDto.createTimeStart",
            "schVirtualComputeCardTemplateDto.createTimeEnd",
            "schVirtualComputeCardTemplateDto.updateTimeStart",
            "schVirtualComputeCardTemplateDto.updateTimeEnd"})
    @SaCheckPermission("schVirtualComputeCardTemplate.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody SchVirtualComputeCardTemplateDto schVirtualComputeCardTemplateDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schVirtualComputeCardTemplateDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchVirtualComputeCardTemplate schVirtualComputeCardTemplate = MyModelUtil.copyTo(schVirtualComputeCardTemplateDto, SchVirtualComputeCardTemplate.class);
        SchVirtualComputeCardTemplate originalSchVirtualComputeCardTemplate = schVirtualComputeCardTemplateService.getById(schVirtualComputeCardTemplate.getId());
        if (originalSchVirtualComputeCardTemplate == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schVirtualComputeCardTemplateService.update(schVirtualComputeCardTemplate, originalSchVirtualComputeCardTemplate)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除vpu模板表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schVirtualComputeCardTemplate.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除vpu模板表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schVirtualComputeCardTemplate.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的vpu模板表列表。
     *
     * @param schVirtualComputeCardTemplateDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schVirtualComputeCardTemplate.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<SchVirtualComputeCardTemplateVo>> list(
            @MyRequestBody SchVirtualComputeCardTemplateDto schVirtualComputeCardTemplateDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchVirtualComputeCardTemplate schVirtualComputeCardTemplateFilter = MyModelUtil.copyTo(schVirtualComputeCardTemplateDtoFilter, SchVirtualComputeCardTemplate.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchVirtualComputeCardTemplate.class);
        List<SchVirtualComputeCardTemplate> schVirtualComputeCardTemplateList =
                schVirtualComputeCardTemplateService.getSchVirtualComputeCardTemplateListWithRelation(schVirtualComputeCardTemplateFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(schVirtualComputeCardTemplateList, SchVirtualComputeCardTemplateVo.class));
    }

    /**
     * 分组列出符合过滤条件的vpu模板表列表。
     *
     * @param schVirtualComputeCardTemplateDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schVirtualComputeCardTemplate.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<SchVirtualComputeCardTemplateVo>> listWithGroup(
            @MyRequestBody SchVirtualComputeCardTemplateDto schVirtualComputeCardTemplateDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchVirtualComputeCardTemplate.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, SchVirtualComputeCardTemplate.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchVirtualComputeCardTemplate filter = MyModelUtil.copyTo(schVirtualComputeCardTemplateDtoFilter, SchVirtualComputeCardTemplate.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<SchVirtualComputeCardTemplate> resultList = schVirtualComputeCardTemplateService.getGroupedSchVirtualComputeCardTemplateListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, SchVirtualComputeCardTemplateVo.class));
    }

    /**
     * 查看指定vpu模板表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @SaCheckPermission("schVirtualComputeCardTemplate.view")
    @GetMapping("/view")
    public ResponseResult<SchVirtualComputeCardTemplateVo> view(@RequestParam Long id) {
        SchVirtualComputeCardTemplate schVirtualComputeCardTemplate = schVirtualComputeCardTemplateService.getByIdWithRelation(id, MyRelationParam.full());
        if (schVirtualComputeCardTemplate == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        SchVirtualComputeCardTemplateVo schVirtualComputeCardTemplateVo = MyModelUtil.copyTo(schVirtualComputeCardTemplate, SchVirtualComputeCardTemplateVo.class);
        return ResponseResult.success(schVirtualComputeCardTemplateVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        SchVirtualComputeCardTemplate originalSchVirtualComputeCardTemplate = schVirtualComputeCardTemplateService.getById(id);
        if (originalSchVirtualComputeCardTemplate == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schVirtualComputeCardTemplateService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
