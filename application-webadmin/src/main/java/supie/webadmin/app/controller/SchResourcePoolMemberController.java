package supie.webadmin.app.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import supie.common.log.annotation.OperationLog;
import supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;
import supie.webadmin.app.vo.*;
import supie.webadmin.app.dto.*;
import supie.webadmin.app.model.*;
import supie.webadmin.app.service.*;
import supie.common.core.object.*;
import supie.common.core.util.*;
import supie.common.core.constant.*;
import supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 资源池成员表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Tag(name = "资源池成员表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/schResourcePoolMember")
public class SchResourcePoolMemberController {

    @Autowired
    private SchResourcePoolMemberService schResourcePoolMemberService;

    /**
     * 新增资源池成员表数据。
     *
     * @param schResourcePoolMemberDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schResourcePoolMemberDto.id",
            "schResourcePoolMemberDto.searchString",
            "schResourcePoolMemberDto.updateTimeStart",
            "schResourcePoolMemberDto.updateTimeEnd",
            "schResourcePoolMemberDto.createTimeStart",
            "schResourcePoolMemberDto.createTimeEnd"})
    @SaCheckPermission("schResourcePoolMember.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody SchResourcePoolMemberDto schResourcePoolMemberDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schResourcePoolMemberDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchResourcePoolMember schResourcePoolMember = MyModelUtil.copyTo(schResourcePoolMemberDto, SchResourcePoolMember.class);
        schResourcePoolMember = schResourcePoolMemberService.saveNew(schResourcePoolMember);
        return ResponseResult.success(schResourcePoolMember.getId());
    }

    /**
     * 更新资源池成员表数据。
     *
     * @param schResourcePoolMemberDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schResourcePoolMemberDto.searchString",
            "schResourcePoolMemberDto.updateTimeStart",
            "schResourcePoolMemberDto.updateTimeEnd",
            "schResourcePoolMemberDto.createTimeStart",
            "schResourcePoolMemberDto.createTimeEnd"})
    @SaCheckPermission("schResourcePoolMember.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody SchResourcePoolMemberDto schResourcePoolMemberDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schResourcePoolMemberDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchResourcePoolMember schResourcePoolMember = MyModelUtil.copyTo(schResourcePoolMemberDto, SchResourcePoolMember.class);
        SchResourcePoolMember originalSchResourcePoolMember = schResourcePoolMemberService.getById(schResourcePoolMember.getId());
        if (originalSchResourcePoolMember == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schResourcePoolMemberService.update(schResourcePoolMember, originalSchResourcePoolMember)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除资源池成员表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schResourcePoolMember.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除资源池成员表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schResourcePoolMember.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的资源池成员表列表。
     *
     * @param schResourcePoolMemberDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schResourcePoolMember.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<SchResourcePoolMemberVo>> list(
            @MyRequestBody SchResourcePoolMemberDto schResourcePoolMemberDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchResourcePoolMember schResourcePoolMemberFilter = MyModelUtil.copyTo(schResourcePoolMemberDtoFilter, SchResourcePoolMember.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchResourcePoolMember.class);
        List<SchResourcePoolMember> schResourcePoolMemberList =
                schResourcePoolMemberService.getSchResourcePoolMemberListWithRelation(schResourcePoolMemberFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(schResourcePoolMemberList, SchResourcePoolMemberVo.class));
    }

    /**
     * 分组列出符合过滤条件的资源池成员表列表。
     *
     * @param schResourcePoolMemberDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schResourcePoolMember.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<SchResourcePoolMemberVo>> listWithGroup(
            @MyRequestBody SchResourcePoolMemberDto schResourcePoolMemberDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchResourcePoolMember.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, SchResourcePoolMember.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchResourcePoolMember filter = MyModelUtil.copyTo(schResourcePoolMemberDtoFilter, SchResourcePoolMember.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<SchResourcePoolMember> resultList = schResourcePoolMemberService.getGroupedSchResourcePoolMemberListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, SchResourcePoolMemberVo.class));
    }

    /**
     * 查看指定资源池成员表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @SaCheckPermission("schResourcePoolMember.view")
    @GetMapping("/view")
    public ResponseResult<SchResourcePoolMemberVo> view(@RequestParam Long id) {
        SchResourcePoolMember schResourcePoolMember = schResourcePoolMemberService.getByIdWithRelation(id, MyRelationParam.full());
        if (schResourcePoolMember == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        SchResourcePoolMemberVo schResourcePoolMemberVo = MyModelUtil.copyTo(schResourcePoolMember, SchResourcePoolMemberVo.class);
        return ResponseResult.success(schResourcePoolMemberVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        SchResourcePoolMember originalSchResourcePoolMember = schResourcePoolMemberService.getById(id);
        if (originalSchResourcePoolMember == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schResourcePoolMemberService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
