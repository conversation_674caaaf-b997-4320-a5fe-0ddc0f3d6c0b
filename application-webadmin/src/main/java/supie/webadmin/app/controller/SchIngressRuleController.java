package supie.webadmin.app.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import supie.common.log.annotation.OperationLog;
import supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;
import supie.webadmin.app.vo.*;
import supie.webadmin.app.dto.*;
import supie.webadmin.app.model.*;
import supie.webadmin.app.service.*;
import supie.common.core.object.*;
import supie.common.core.util.*;
import supie.common.core.constant.*;
import supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * K8S Ingress路由规则表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Tag(name = "K8S Ingress路由规则表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/schIngressRule")
public class SchIngressRuleController {

    @Autowired
    private SchIngressRuleService schIngressRuleService;

    /**
     * 新增K8S Ingress路由规则表数据。
     *
     * @param schIngressRuleDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schIngressRuleDto.id",
            "schIngressRuleDto.searchString",
            "schIngressRuleDto.updateTimeStart",
            "schIngressRuleDto.updateTimeEnd",
            "schIngressRuleDto.createTimeStart",
            "schIngressRuleDto.createTimeEnd"})
    @SaCheckPermission("schIngressRule.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody SchIngressRuleDto schIngressRuleDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schIngressRuleDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchIngressRule schIngressRule = MyModelUtil.copyTo(schIngressRuleDto, SchIngressRule.class);
        schIngressRule = schIngressRuleService.saveNew(schIngressRule);
        return ResponseResult.success(schIngressRule.getId());
    }

    /**
     * 更新K8S Ingress路由规则表数据。
     *
     * @param schIngressRuleDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schIngressRuleDto.searchString",
            "schIngressRuleDto.updateTimeStart",
            "schIngressRuleDto.updateTimeEnd",
            "schIngressRuleDto.createTimeStart",
            "schIngressRuleDto.createTimeEnd"})
    @SaCheckPermission("schIngressRule.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody SchIngressRuleDto schIngressRuleDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schIngressRuleDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchIngressRule schIngressRule = MyModelUtil.copyTo(schIngressRuleDto, SchIngressRule.class);
        SchIngressRule originalSchIngressRule = schIngressRuleService.getById(schIngressRule.getId());
        if (originalSchIngressRule == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schIngressRuleService.update(schIngressRule, originalSchIngressRule)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除K8S Ingress路由规则表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schIngressRule.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除K8S Ingress路由规则表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schIngressRule.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的K8S Ingress路由规则表列表。
     *
     * @param schIngressRuleDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schIngressRule.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<SchIngressRuleVo>> list(
            @MyRequestBody SchIngressRuleDto schIngressRuleDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchIngressRule schIngressRuleFilter = MyModelUtil.copyTo(schIngressRuleDtoFilter, SchIngressRule.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchIngressRule.class);
        List<SchIngressRule> schIngressRuleList =
                schIngressRuleService.getSchIngressRuleListWithRelation(schIngressRuleFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(schIngressRuleList, SchIngressRuleVo.class));
    }

    /**
     * 分组列出符合过滤条件的K8S Ingress路由规则表列表。
     *
     * @param schIngressRuleDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schIngressRule.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<SchIngressRuleVo>> listWithGroup(
            @MyRequestBody SchIngressRuleDto schIngressRuleDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchIngressRule.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, SchIngressRule.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchIngressRule filter = MyModelUtil.copyTo(schIngressRuleDtoFilter, SchIngressRule.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<SchIngressRule> resultList = schIngressRuleService.getGroupedSchIngressRuleListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, SchIngressRuleVo.class));
    }

    /**
     * 查看指定K8S Ingress路由规则表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @SaCheckPermission("schIngressRule.view")
    @GetMapping("/view")
    public ResponseResult<SchIngressRuleVo> view(@RequestParam Long id) {
        SchIngressRule schIngressRule = schIngressRuleService.getByIdWithRelation(id, MyRelationParam.full());
        if (schIngressRule == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        SchIngressRuleVo schIngressRuleVo = MyModelUtil.copyTo(schIngressRule, SchIngressRuleVo.class);
        return ResponseResult.success(schIngressRuleVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        SchIngressRule originalSchIngressRule = schIngressRuleService.getById(id);
        if (originalSchIngressRule == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schIngressRuleService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
