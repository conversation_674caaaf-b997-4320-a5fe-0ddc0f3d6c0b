package supie.webadmin.app.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import supie.common.log.annotation.OperationLog;
import supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;
import supie.webadmin.app.vo.*;
import supie.webadmin.app.dto.*;
import supie.webadmin.app.model.*;
import supie.webadmin.app.service.*;
import supie.common.core.object.*;
import supie.common.core.util.*;
import supie.common.core.constant.*;
import supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 服务卡监控(NPU,GPU监控)操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Tag(name = "服务卡监控(NPU,GPU监控)管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/schCardMonitor")
public class SchCardMonitorController {

    @Autowired
    private SchCardMonitorService schCardMonitorService;

    /**
     * 新增服务卡监控(NPU,GPU监控)数据。
     *
     * @param schCardMonitorDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schCardMonitorDto.id",
            "schCardMonitorDto.searchString",
            "schCardMonitorDto.updateTimeStart",
            "schCardMonitorDto.updateTimeEnd",
            "schCardMonitorDto.createTimeStart",
            "schCardMonitorDto.createTimeEnd"})
    @SaCheckPermission("schCardMonitor.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody SchCardMonitorDto schCardMonitorDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schCardMonitorDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchCardMonitor schCardMonitor = MyModelUtil.copyTo(schCardMonitorDto, SchCardMonitor.class);
        schCardMonitor = schCardMonitorService.saveNew(schCardMonitor);
        return ResponseResult.success(schCardMonitor.getId());
    }

    /**
     * 更新服务卡监控(NPU,GPU监控)数据。
     *
     * @param schCardMonitorDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schCardMonitorDto.searchString",
            "schCardMonitorDto.updateTimeStart",
            "schCardMonitorDto.updateTimeEnd",
            "schCardMonitorDto.createTimeStart",
            "schCardMonitorDto.createTimeEnd"})
    @SaCheckPermission("schCardMonitor.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody SchCardMonitorDto schCardMonitorDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schCardMonitorDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchCardMonitor schCardMonitor = MyModelUtil.copyTo(schCardMonitorDto, SchCardMonitor.class);
        SchCardMonitor originalSchCardMonitor = schCardMonitorService.getById(schCardMonitor.getId());
        if (originalSchCardMonitor == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schCardMonitorService.update(schCardMonitor, originalSchCardMonitor)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除服务卡监控(NPU,GPU监控)数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schCardMonitor.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除服务卡监控(NPU,GPU监控)数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schCardMonitor.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的服务卡监控(NPU,GPU监控)列表。
     *
     * @param schCardMonitorDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schCardMonitor.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<SchCardMonitorVo>> list(
            @MyRequestBody SchCardMonitorDto schCardMonitorDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchCardMonitor schCardMonitorFilter = MyModelUtil.copyTo(schCardMonitorDtoFilter, SchCardMonitor.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchCardMonitor.class);
        List<SchCardMonitor> schCardMonitorList =
                schCardMonitorService.getSchCardMonitorListWithRelation(schCardMonitorFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(schCardMonitorList, SchCardMonitorVo.class));
    }

    /**
     * 分组列出符合过滤条件的服务卡监控(NPU,GPU监控)列表。
     *
     * @param schCardMonitorDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schCardMonitor.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<SchCardMonitorVo>> listWithGroup(
            @MyRequestBody SchCardMonitorDto schCardMonitorDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchCardMonitor.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, SchCardMonitor.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchCardMonitor filter = MyModelUtil.copyTo(schCardMonitorDtoFilter, SchCardMonitor.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<SchCardMonitor> resultList = schCardMonitorService.getGroupedSchCardMonitorListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, SchCardMonitorVo.class));
    }

    /**
     * 查看指定服务卡监控(NPU,GPU监控)对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @SaCheckPermission("schCardMonitor.view")
    @GetMapping("/view")
    public ResponseResult<SchCardMonitorVo> view(@RequestParam Long id) {
        SchCardMonitor schCardMonitor = schCardMonitorService.getByIdWithRelation(id, MyRelationParam.full());
        if (schCardMonitor == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        SchCardMonitorVo schCardMonitorVo = MyModelUtil.copyTo(schCardMonitor, SchCardMonitorVo.class);
        return ResponseResult.success(schCardMonitorVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        SchCardMonitor originalSchCardMonitor = schCardMonitorService.getById(id);
        if (originalSchCardMonitor == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schCardMonitorService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
