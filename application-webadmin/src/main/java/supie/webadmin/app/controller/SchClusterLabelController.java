package supie.webadmin.app.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import supie.common.log.annotation.OperationLog;
import supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;
import supie.webadmin.app.vo.*;
import supie.webadmin.app.dto.*;
import supie.webadmin.app.model.*;
import supie.webadmin.app.service.*;
import supie.common.core.object.*;
import supie.common.core.util.*;
import supie.common.core.constant.*;
import supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 集群标签表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Tag(name = "k8s集群标签表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/schClusterLabel")
public class SchClusterLabelController {

    @Autowired
    private SchClusterLabelService schClusterLabelService;

    /**
     * 新增集群标签表数据。
     *
     * @param schClusterLabelDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schClusterLabelDto.id",
            "schClusterLabelDto.searchString",
            "schClusterLabelDto.updateTimeStart",
            "schClusterLabelDto.updateTimeEnd",
            "schClusterLabelDto.createTimeStart",
            "schClusterLabelDto.createTimeEnd"})
    @SaCheckPermission("schClusterLabel.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody SchClusterLabelDto schClusterLabelDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schClusterLabelDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchClusterLabel schClusterLabel = MyModelUtil.copyTo(schClusterLabelDto, SchClusterLabel.class);
        schClusterLabel = schClusterLabelService.saveNew(schClusterLabel);
        return ResponseResult.success(schClusterLabel.getId());
    }

    /**
     * 更新集群标签表数据。
     *
     * @param schClusterLabelDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schClusterLabelDto.searchString",
            "schClusterLabelDto.updateTimeStart",
            "schClusterLabelDto.updateTimeEnd",
            "schClusterLabelDto.createTimeStart",
            "schClusterLabelDto.createTimeEnd"})
    @SaCheckPermission("schClusterLabel.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody SchClusterLabelDto schClusterLabelDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schClusterLabelDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchClusterLabel schClusterLabel = MyModelUtil.copyTo(schClusterLabelDto, SchClusterLabel.class);
        SchClusterLabel originalSchClusterLabel = schClusterLabelService.getById(schClusterLabel.getId());
        if (originalSchClusterLabel == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schClusterLabelService.update(schClusterLabel, originalSchClusterLabel)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除集群标签表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schClusterLabel.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除集群标签表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schClusterLabel.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的集群标签表列表。
     *
     * @param schClusterLabelDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schClusterLabel.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<SchClusterLabelVo>> list(
            @MyRequestBody SchClusterLabelDto schClusterLabelDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchClusterLabel schClusterLabelFilter = MyModelUtil.copyTo(schClusterLabelDtoFilter, SchClusterLabel.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchClusterLabel.class);
        List<SchClusterLabel> schClusterLabelList =
                schClusterLabelService.getSchClusterLabelListWithRelation(schClusterLabelFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(schClusterLabelList, SchClusterLabelVo.class));
    }

    /**
     * 分组列出符合过滤条件的集群标签表列表。
     *
     * @param schClusterLabelDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schClusterLabel.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<SchClusterLabelVo>> listWithGroup(
            @MyRequestBody SchClusterLabelDto schClusterLabelDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchClusterLabel.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, SchClusterLabel.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchClusterLabel filter = MyModelUtil.copyTo(schClusterLabelDtoFilter, SchClusterLabel.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<SchClusterLabel> resultList = schClusterLabelService.getGroupedSchClusterLabelListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, SchClusterLabelVo.class));
    }

    /**
     * 查看指定集群标签表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @SaCheckPermission("schClusterLabel.view")
    @GetMapping("/view")
    public ResponseResult<SchClusterLabelVo> view(@RequestParam Long id) {
        SchClusterLabel schClusterLabel = schClusterLabelService.getByIdWithRelation(id, MyRelationParam.full());
        if (schClusterLabel == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        SchClusterLabelVo schClusterLabelVo = MyModelUtil.copyTo(schClusterLabel, SchClusterLabelVo.class);
        return ResponseResult.success(schClusterLabelVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        SchClusterLabel originalSchClusterLabel = schClusterLabelService.getById(id);
        if (originalSchClusterLabel == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schClusterLabelService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
