package supie.webadmin.app.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import supie.common.log.annotation.OperationLog;
import supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;
import supie.webadmin.app.vo.*;
import supie.webadmin.app.dto.*;
import supie.webadmin.app.model.*;
import supie.webadmin.app.service.*;
import supie.common.core.object.*;
import supie.common.core.util.*;
import supie.common.core.constant.*;
import supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 业务字典表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Tag(name = "业务字典表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/schBusinessDict")
public class SchBusinessDictController {

    @Autowired
    private SchBusinessDictService schBusinessDictService;

    /**
     * 新增业务字典表数据。
     *
     * @param schBusinessDictDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schBusinessDictDto.id",
            "schBusinessDictDto.searchString",
            "schBusinessDictDto.updateTimeStart",
            "schBusinessDictDto.updateTimeEnd",
            "schBusinessDictDto.createTimeStart",
            "schBusinessDictDto.createTimeEnd"})
    @SaCheckPermission("schBusinessDict.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody SchBusinessDictDto schBusinessDictDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schBusinessDictDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchBusinessDict schBusinessDict = MyModelUtil.copyTo(schBusinessDictDto, SchBusinessDict.class);
        schBusinessDict = schBusinessDictService.saveNew(schBusinessDict);
        return ResponseResult.success(schBusinessDict.getId());
    }

    /**
     * 更新业务字典表数据。
     *
     * @param schBusinessDictDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schBusinessDictDto.searchString",
            "schBusinessDictDto.updateTimeStart",
            "schBusinessDictDto.updateTimeEnd",
            "schBusinessDictDto.createTimeStart",
            "schBusinessDictDto.createTimeEnd"})
    @SaCheckPermission("schBusinessDict.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody SchBusinessDictDto schBusinessDictDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schBusinessDictDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchBusinessDict schBusinessDict = MyModelUtil.copyTo(schBusinessDictDto, SchBusinessDict.class);
        SchBusinessDict originalSchBusinessDict = schBusinessDictService.getById(schBusinessDict.getId());
        if (originalSchBusinessDict == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schBusinessDictService.update(schBusinessDict, originalSchBusinessDict)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除业务字典表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schBusinessDict.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除业务字典表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schBusinessDict.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的业务字典表列表。
     *
     * @param schBusinessDictDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schBusinessDict.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<SchBusinessDictVo>> list(
            @MyRequestBody SchBusinessDictDto schBusinessDictDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchBusinessDict schBusinessDictFilter = MyModelUtil.copyTo(schBusinessDictDtoFilter, SchBusinessDict.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchBusinessDict.class);
        List<SchBusinessDict> schBusinessDictList =
                schBusinessDictService.getSchBusinessDictListWithRelation(schBusinessDictFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(schBusinessDictList, SchBusinessDictVo.class));
    }

    /**
     * 列出符合过滤条件的业务字典表列表。
     *
     * @param schBusinessDictDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schBusinessDict.view")
    @PostMapping("/listWithTask")
    public ResponseResult<MyPageData<SchBusinessDictVo>> listWithTask(
            @MyRequestBody SchBusinessDictDto schBusinessDictDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchBusinessDict schBusinessDictFilter = MyModelUtil.copyTo(schBusinessDictDtoFilter, SchBusinessDict.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchBusinessDict.class);
        List<SchBusinessDict> schBusinessDictList =
                schBusinessDictService.getSchBusinessDictListWithTask(schBusinessDictFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(schBusinessDictList, SchBusinessDictVo.class));
    }

    /**
     * 分组列出符合过滤条件的业务字典表列表。
     *
     * @param schBusinessDictDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schBusinessDict.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<SchBusinessDictVo>> listWithGroup(
            @MyRequestBody SchBusinessDictDto schBusinessDictDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchBusinessDict.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, SchBusinessDict.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchBusinessDict filter = MyModelUtil.copyTo(schBusinessDictDtoFilter, SchBusinessDict.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<SchBusinessDict> resultList = schBusinessDictService.getGroupedSchBusinessDictListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, SchBusinessDictVo.class));
    }

    /**
     * 查看指定业务字典表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @SaCheckPermission("schBusinessDict.view")
    @GetMapping("/view")
    public ResponseResult<SchBusinessDictVo> view(@RequestParam Long id) {
        SchBusinessDict schBusinessDict = schBusinessDictService.getByIdWithRelation(id, MyRelationParam.full());
        if (schBusinessDict == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        SchBusinessDictVo schBusinessDictVo = MyModelUtil.copyTo(schBusinessDict, SchBusinessDictVo.class);
        return ResponseResult.success(schBusinessDictVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        SchBusinessDict originalSchBusinessDict = schBusinessDictService.getById(id);
        if (originalSchBusinessDict == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schBusinessDictService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
