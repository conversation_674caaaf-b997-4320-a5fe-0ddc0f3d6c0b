package supie.webadmin.app.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import supie.common.log.annotation.OperationLog;
import supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;
import supie.webadmin.app.vo.*;
import supie.webadmin.app.dto.*;
import supie.webadmin.app.model.*;
import supie.webadmin.app.service.*;
import supie.common.core.object.*;
import supie.common.core.util.*;
import supie.common.core.constant.*;
import supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * K8S容器组件监控表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Tag(name = "K8S容器组件监控表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/schPodMonitor")
public class SchPodMonitorController {

    @Autowired
    private SchPodMonitorService schPodMonitorService;

    /**
     * 新增K8S容器组件监控表数据。
     *
     * @param schPodMonitorDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schPodMonitorDto.id",
            "schPodMonitorDto.searchString",
            "schPodMonitorDto.updateTimeStart",
            "schPodMonitorDto.updateTimeEnd",
            "schPodMonitorDto.createTimeStart",
            "schPodMonitorDto.createTimeEnd"})
    @SaCheckPermission("schPodMonitor.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody SchPodMonitorDto schPodMonitorDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schPodMonitorDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchPodMonitor schPodMonitor = MyModelUtil.copyTo(schPodMonitorDto, SchPodMonitor.class);
        schPodMonitor = schPodMonitorService.saveNew(schPodMonitor);
        return ResponseResult.success(schPodMonitor.getId());
    }

    /**
     * 更新K8S容器组件监控表数据。
     *
     * @param schPodMonitorDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schPodMonitorDto.searchString",
            "schPodMonitorDto.updateTimeStart",
            "schPodMonitorDto.updateTimeEnd",
            "schPodMonitorDto.createTimeStart",
            "schPodMonitorDto.createTimeEnd"})
    @SaCheckPermission("schPodMonitor.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody SchPodMonitorDto schPodMonitorDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schPodMonitorDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchPodMonitor schPodMonitor = MyModelUtil.copyTo(schPodMonitorDto, SchPodMonitor.class);
        SchPodMonitor originalSchPodMonitor = schPodMonitorService.getById(schPodMonitor.getId());
        if (originalSchPodMonitor == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schPodMonitorService.update(schPodMonitor, originalSchPodMonitor)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除K8S容器组件监控表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schPodMonitor.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除K8S容器组件监控表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schPodMonitor.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的K8S容器组件监控表列表。
     *
     * @param schPodMonitorDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schPodMonitor.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<SchPodMonitorVo>> list(
            @MyRequestBody SchPodMonitorDto schPodMonitorDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchPodMonitor schPodMonitorFilter = MyModelUtil.copyTo(schPodMonitorDtoFilter, SchPodMonitor.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchPodMonitor.class);
        List<SchPodMonitor> schPodMonitorList =
                schPodMonitorService.getSchPodMonitorListWithRelation(schPodMonitorFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(schPodMonitorList, SchPodMonitorVo.class));
    }

    /**
     * 分组列出符合过滤条件的K8S容器组件监控表列表。
     *
     * @param schPodMonitorDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schPodMonitor.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<SchPodMonitorVo>> listWithGroup(
            @MyRequestBody SchPodMonitorDto schPodMonitorDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchPodMonitor.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, SchPodMonitor.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchPodMonitor filter = MyModelUtil.copyTo(schPodMonitorDtoFilter, SchPodMonitor.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<SchPodMonitor> resultList = schPodMonitorService.getGroupedSchPodMonitorListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, SchPodMonitorVo.class));
    }

    /**
     * 查看指定K8S容器组件监控表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @SaCheckPermission("schPodMonitor.view")
    @GetMapping("/view")
    public ResponseResult<SchPodMonitorVo> view(@RequestParam Long id) {
        SchPodMonitor schPodMonitor = schPodMonitorService.getByIdWithRelation(id, MyRelationParam.full());
        if (schPodMonitor == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        SchPodMonitorVo schPodMonitorVo = MyModelUtil.copyTo(schPodMonitor, SchPodMonitorVo.class);
        return ResponseResult.success(schPodMonitorVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        SchPodMonitor originalSchPodMonitor = schPodMonitorService.getById(id);
        if (originalSchPodMonitor == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schPodMonitorService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
