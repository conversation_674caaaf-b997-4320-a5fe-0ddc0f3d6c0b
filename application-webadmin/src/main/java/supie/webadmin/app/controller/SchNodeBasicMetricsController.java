package supie.webadmin.app.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import supie.common.log.annotation.OperationLog;
import supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;
import supie.webadmin.app.vo.*;
import supie.webadmin.app.dto.*;
import supie.webadmin.app.model.*;
import supie.webadmin.app.service.*;
import supie.common.core.object.*;
import supie.common.core.util.*;
import supie.common.core.constant.*;
import supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 节点基础监控表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Tag(name = "节点基础监控表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/schNodeBasicMetrics")
public class SchNodeBasicMetricsController {

    @Autowired
    private SchNodeBasicMetricsService schNodeBasicMetricsService;

    /**
     * 新增节点基础监控表数据。
     *
     * @param schNodeBasicMetricsDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schNodeBasicMetricsDto.id",
            "schNodeBasicMetricsDto.searchString",
            "schNodeBasicMetricsDto.createTimeStart",
            "schNodeBasicMetricsDto.createTimeEnd",
            "schNodeBasicMetricsDto.tsStartStart",
            "schNodeBasicMetricsDto.tsStartEnd",
            "schNodeBasicMetricsDto.tsEndStart",
            "schNodeBasicMetricsDto.tsEndEnd"})
    @SaCheckPermission("schNodeBasicMetrics.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody SchNodeBasicMetricsDto schNodeBasicMetricsDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schNodeBasicMetricsDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchNodeBasicMetrics schNodeBasicMetrics = MyModelUtil.copyTo(schNodeBasicMetricsDto, SchNodeBasicMetrics.class);
        schNodeBasicMetrics = schNodeBasicMetricsService.saveNew(schNodeBasicMetrics);
        return ResponseResult.success(schNodeBasicMetrics.getId());
    }

    /**
     * 更新节点基础监控表数据。
     *
     * @param schNodeBasicMetricsDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schNodeBasicMetricsDto.searchString",
            "schNodeBasicMetricsDto.createTimeStart",
            "schNodeBasicMetricsDto.createTimeEnd",
            "schNodeBasicMetricsDto.tsStartStart",
            "schNodeBasicMetricsDto.tsStartEnd",
            "schNodeBasicMetricsDto.tsEndStart",
            "schNodeBasicMetricsDto.tsEndEnd"})
    @SaCheckPermission("schNodeBasicMetrics.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody SchNodeBasicMetricsDto schNodeBasicMetricsDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schNodeBasicMetricsDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchNodeBasicMetrics schNodeBasicMetrics = MyModelUtil.copyTo(schNodeBasicMetricsDto, SchNodeBasicMetrics.class);
        SchNodeBasicMetrics originalSchNodeBasicMetrics = schNodeBasicMetricsService.getById(schNodeBasicMetrics.getId());
        if (originalSchNodeBasicMetrics == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schNodeBasicMetricsService.update(schNodeBasicMetrics, originalSchNodeBasicMetrics)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除节点基础监控表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schNodeBasicMetrics.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除节点基础监控表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schNodeBasicMetrics.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的节点基础监控表列表。
     *
     * @param schNodeBasicMetricsDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schNodeBasicMetrics.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<SchNodeBasicMetricsVo>> list(
            @MyRequestBody SchNodeBasicMetricsDto schNodeBasicMetricsDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchNodeBasicMetrics schNodeBasicMetricsFilter = MyModelUtil.copyTo(schNodeBasicMetricsDtoFilter, SchNodeBasicMetrics.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchNodeBasicMetrics.class);
        List<SchNodeBasicMetrics> schNodeBasicMetricsList =
                schNodeBasicMetricsService.getSchNodeBasicMetricsListWithRelation(schNodeBasicMetricsFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(schNodeBasicMetricsList, SchNodeBasicMetricsVo.class));
    }

    /**
     * 分组列出符合过滤条件的节点基础监控表列表。
     *
     * @param schNodeBasicMetricsDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schNodeBasicMetrics.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<SchNodeBasicMetricsVo>> listWithGroup(
            @MyRequestBody SchNodeBasicMetricsDto schNodeBasicMetricsDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchNodeBasicMetrics.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, SchNodeBasicMetrics.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchNodeBasicMetrics filter = MyModelUtil.copyTo(schNodeBasicMetricsDtoFilter, SchNodeBasicMetrics.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<SchNodeBasicMetrics> resultList = schNodeBasicMetricsService.getGroupedSchNodeBasicMetricsListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, SchNodeBasicMetricsVo.class));
    }

    /**
     * 查看指定节点基础监控表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @SaCheckPermission("schNodeBasicMetrics.view")
    @GetMapping("/view")
    public ResponseResult<SchNodeBasicMetricsVo> view(@RequestParam Long id) {
        SchNodeBasicMetrics schNodeBasicMetrics = schNodeBasicMetricsService.getByIdWithRelation(id, MyRelationParam.full());
        if (schNodeBasicMetrics == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        SchNodeBasicMetricsVo schNodeBasicMetricsVo = MyModelUtil.copyTo(schNodeBasicMetrics, SchNodeBasicMetricsVo.class);
        return ResponseResult.success(schNodeBasicMetricsVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        SchNodeBasicMetrics originalSchNodeBasicMetrics = schNodeBasicMetricsService.getById(id);
        if (originalSchNodeBasicMetrics == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schNodeBasicMetricsService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }

    /**
     * 指标统计接口：统计资源总个数，全部cpu总核心数量，全部计算卡内存总数，以及资源的分配情况：空闲，占用，异常。
     *
     * @param
     * @return 应答结果map，包含统计结果详情。
     */
    @Operation(summary = "服务资源占用排名")
    @GetMapping("/resourceSort")
    public ResponseResult<List<Map<String, Object>>> resourceSort() {
        List<Map<String, Object>> schNodeBasicMetrics = schNodeBasicMetricsService.getSchNodeBasicMetrics();
//        if (schNodeBasicMetrics == null || schNodeBasicMetrics.isEmpty()) {
//            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
//        }
        return ResponseResult.success(schNodeBasicMetrics);
    }
}
