package supie.webadmin.app.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import supie.common.log.annotation.OperationLog;
import supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;
import supie.webadmin.app.vo.*;
import supie.webadmin.app.dto.*;
import supie.webadmin.app.model.*;
import supie.webadmin.app.service.*;
import supie.common.core.object.*;
import supie.common.core.util.*;
import supie.common.core.constant.*;
import supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 计算卡表（GPU、NPU）操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Tag(name = "计算卡表（GPU、NPU）管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/schComputeDevice")
public class SchComputeDeviceController {

    @Autowired
    private SchComputeDeviceService schComputeDeviceService;

    /**
     * 新增计算卡表（GPU、NPU）数据。
     *
     * @param schComputeDeviceDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schComputeDeviceDto.id",
            "schComputeDeviceDto.searchString",
            "schComputeDeviceDto.updateTimeStart",
            "schComputeDeviceDto.updateTimeEnd",
            "schComputeDeviceDto.createTimeStart",
            "schComputeDeviceDto.createTimeEnd"})
    @SaCheckPermission("schComputeDevice.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody SchComputeDeviceDto schComputeDeviceDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schComputeDeviceDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchComputeDevice schComputeDevice = MyModelUtil.copyTo(schComputeDeviceDto, SchComputeDevice.class);
        schComputeDevice = schComputeDeviceService.saveNew(schComputeDevice);
        return ResponseResult.success(schComputeDevice.getId());
    }

    /**
     * 更新计算卡表（GPU、NPU）数据。
     *
     * @param schComputeDeviceDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schComputeDeviceDto.searchString",
            "schComputeDeviceDto.updateTimeStart",
            "schComputeDeviceDto.updateTimeEnd",
            "schComputeDeviceDto.createTimeStart",
            "schComputeDeviceDto.createTimeEnd"})
    @SaCheckPermission("schComputeDevice.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody SchComputeDeviceDto schComputeDeviceDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schComputeDeviceDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchComputeDevice schComputeDevice = MyModelUtil.copyTo(schComputeDeviceDto, SchComputeDevice.class);
        SchComputeDevice originalSchComputeDevice = schComputeDeviceService.getById(schComputeDevice.getId());
        if (originalSchComputeDevice == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schComputeDeviceService.update(schComputeDevice, originalSchComputeDevice)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除计算卡表（GPU、NPU）数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schComputeDevice.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除计算卡表（GPU、NPU）数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schComputeDevice.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的计算卡表（GPU、NPU）列表。
     *
     * @param schComputeDeviceDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schComputeDevice.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<SchComputeDeviceVo>> list(
            @MyRequestBody SchComputeDeviceDto schComputeDeviceDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchComputeDevice schComputeDeviceFilter = MyModelUtil.copyTo(schComputeDeviceDtoFilter, SchComputeDevice.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchComputeDevice.class);
        List<SchComputeDevice> schComputeDeviceList =
                schComputeDeviceService.getSchComputeDeviceListWithRelation(schComputeDeviceFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(schComputeDeviceList, SchComputeDeviceVo.class));
    }

    /**
     * 分组列出符合过滤条件的计算卡表（GPU、NPU）列表。
     *
     * @param schComputeDeviceDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schComputeDevice.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<SchComputeDeviceVo>> listWithGroup(
            @MyRequestBody SchComputeDeviceDto schComputeDeviceDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchComputeDevice.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, SchComputeDevice.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchComputeDevice filter = MyModelUtil.copyTo(schComputeDeviceDtoFilter, SchComputeDevice.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<SchComputeDevice> resultList = schComputeDeviceService.getGroupedSchComputeDeviceListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, SchComputeDeviceVo.class));
    }

    /**
     * 查看指定计算卡表（GPU、NPU）对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @SaCheckPermission("schComputeDevice.view")
    @GetMapping("/view")
    public ResponseResult<SchComputeDeviceVo> view(@RequestParam Long id) {
        SchComputeDevice schComputeDevice = schComputeDeviceService.getByIdWithRelation(id, MyRelationParam.full());
        if (schComputeDevice == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        SchComputeDeviceVo schComputeDeviceVo = MyModelUtil.copyTo(schComputeDevice, SchComputeDeviceVo.class);
        return ResponseResult.success(schComputeDeviceVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        SchComputeDevice originalSchComputeDevice = schComputeDeviceService.getById(id);
        if (originalSchComputeDevice == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schComputeDeviceService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
