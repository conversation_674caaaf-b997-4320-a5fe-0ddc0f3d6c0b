package supie.webadmin.app.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import supie.common.log.annotation.OperationLog;
import supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;
import supie.webadmin.app.dao.SchTaskMonitoringMapper;
import supie.webadmin.app.vo.*;
import supie.webadmin.app.dto.*;
import supie.webadmin.app.model.*;
import supie.webadmin.app.service.*;
import supie.common.core.object.*;
import supie.common.core.util.*;
import supie.common.core.constant.*;
import supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import supie.webadmin.upms.vo.TaskCount;

import java.util.*;

/**
 * 任务监控表（踩点）操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Tag(name = "任务监控表（踩点）管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/schTaskMonitoring")
public class SchTaskMonitoringController {

    @Autowired
    private SchTaskMonitoringService schTaskMonitoringService;

    @Resource
    private SchTaskMonitoringMapper schTaskMonitoringMapper;

    @Operation(summary = "taskCount",description = "运行任务指标统计")
    @GetMapping("/taskCount")
    public ResponseResult<TaskCount> taskCount() {
        TaskCount taskCount = schTaskMonitoringMapper.taskCount();
        return ResponseResult.success(taskCount);
    }

    /**
     * 新增任务监控表（踩点）数据。
     *
     * @param schTaskMonitoringDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schTaskMonitoringDto.id",
            "schTaskMonitoringDto.searchString",
            "schTaskMonitoringDto.updateTimeStart",
            "schTaskMonitoringDto.updateTimeEnd",
            "schTaskMonitoringDto.createTimeStart",
            "schTaskMonitoringDto.createTimeEnd",
            "schTaskMonitoringDto.tsStartStart",
            "schTaskMonitoringDto.tsStartEnd",
            "schTaskMonitoringDto.tsEndStart",
            "schTaskMonitoringDto.tsEndEnd"})
    @SaCheckPermission("schTaskMonitoring.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody SchTaskMonitoringDto schTaskMonitoringDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schTaskMonitoringDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchTaskMonitoring schTaskMonitoring = MyModelUtil.copyTo(schTaskMonitoringDto, SchTaskMonitoring.class);
        schTaskMonitoring = schTaskMonitoringService.saveNew(schTaskMonitoring);
        return ResponseResult.success(schTaskMonitoring.getId());
    }

    /**
     * 更新任务监控表（踩点）数据。
     *
     * @param schTaskMonitoringDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schTaskMonitoringDto.searchString",
            "schTaskMonitoringDto.updateTimeStart",
            "schTaskMonitoringDto.updateTimeEnd",
            "schTaskMonitoringDto.createTimeStart",
            "schTaskMonitoringDto.createTimeEnd",
            "schTaskMonitoringDto.tsStartStart",
            "schTaskMonitoringDto.tsStartEnd",
            "schTaskMonitoringDto.tsEndStart",
            "schTaskMonitoringDto.tsEndEnd"})
    @SaCheckPermission("schTaskMonitoring.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody SchTaskMonitoringDto schTaskMonitoringDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schTaskMonitoringDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchTaskMonitoring schTaskMonitoring = MyModelUtil.copyTo(schTaskMonitoringDto, SchTaskMonitoring.class);
        SchTaskMonitoring originalSchTaskMonitoring = schTaskMonitoringService.getById(schTaskMonitoring.getId());
        if (originalSchTaskMonitoring == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schTaskMonitoringService.update(schTaskMonitoring, originalSchTaskMonitoring)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除任务监控表（踩点）数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schTaskMonitoring.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除任务监控表（踩点）数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schTaskMonitoring.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的任务监控表（踩点）列表。
     *
     * @param schTaskMonitoringDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schTaskMonitoring.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<SchTaskMonitoringVo>> list(
            @MyRequestBody SchTaskMonitoringDto schTaskMonitoringDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchTaskMonitoring schTaskMonitoringFilter = MyModelUtil.copyTo(schTaskMonitoringDtoFilter, SchTaskMonitoring.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchTaskMonitoring.class);
        List<SchTaskMonitoring> schTaskMonitoringList =
                schTaskMonitoringService.getSchTaskMonitoringListWithRelation(schTaskMonitoringFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(schTaskMonitoringList, SchTaskMonitoringVo.class));
    }

    /**
     * 分组列出符合过滤条件的任务监控表（踩点）列表。
     *
     * @param schTaskMonitoringDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schTaskMonitoring.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<SchTaskMonitoringVo>> listWithGroup(
            @MyRequestBody SchTaskMonitoringDto schTaskMonitoringDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchTaskMonitoring.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, SchTaskMonitoring.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchTaskMonitoring filter = MyModelUtil.copyTo(schTaskMonitoringDtoFilter, SchTaskMonitoring.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<SchTaskMonitoring> resultList = schTaskMonitoringService.getGroupedSchTaskMonitoringListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, SchTaskMonitoringVo.class));
    }

    /**
     * 查看指定任务监控表（踩点）对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @SaCheckPermission("schTaskMonitoring.view")
    @GetMapping("/view")
    public ResponseResult<SchTaskMonitoringVo> view(@RequestParam Long id) {
        SchTaskMonitoring schTaskMonitoring = schTaskMonitoringService.getByIdWithRelation(id, MyRelationParam.full());
        if (schTaskMonitoring == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        SchTaskMonitoringVo schTaskMonitoringVo = MyModelUtil.copyTo(schTaskMonitoring, SchTaskMonitoringVo.class);
        return ResponseResult.success(schTaskMonitoringVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        SchTaskMonitoring originalSchTaskMonitoring = schTaskMonitoringService.getById(id);
        if (originalSchTaskMonitoring == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schTaskMonitoringService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
