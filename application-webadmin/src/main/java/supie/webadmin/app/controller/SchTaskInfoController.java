package supie.webadmin.app.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

import io.swagger.v3.oas.annotations.Parameters;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotNull;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import supie.common.log.annotation.OperationLog;
import supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;

import supie.webadmin.app.dao.SchTaskInfoMapper;
import supie.webadmin.app.util.ComposeGenerate;
import supie.webadmin.app.util.DockerComposeGenerator;
import supie.webadmin.app.util.docker.ContainerConfig;
import supie.webadmin.app.vo.*;
import supie.webadmin.app.dto.*;
import supie.webadmin.app.model.*;
import supie.webadmin.app.service.*;
import supie.common.core.object.*;
import supie.common.core.util.*;
import supie.common.core.constant.*;
import supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;

/**
 * 任务表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Tag(name = "任务表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/schTaskInfo")
public class SchTaskInfoController {

    @Autowired
    private SchTaskInfoService schTaskInfoService;
    @Autowired
    private SchTaskInfoMapper schTaskInfoMapper;

    @Resource
    private ComposeGenerate composeGenerate;

    @Resource(name = "poolExecutor")
    private ExecutorService poolExecutor;

    @Operation(summary = "audit",description = "审核接口")
    @Parameters({

    @Parameter(name = "taskId", required = true, description = "任务表主键id", example = "19384749584028484"),
    @Parameter(name = "status", required = true, description = "审核状态", example = "approved 批准 rejected 拒绝")
}
    )
    @PostMapping("/audit")
    public ResponseResult<SchTaskApprovalVo> audit(@RequestBody Map<String,String> params){
        SchTaskApproval schTaskApproval = schTaskInfoService.auditTask(params.get("taskId"),params.get("status"));
        return ResponseResult.success(MyModelUtil.copyTo(schTaskApproval,SchTaskApprovalVo.class));
    }



    @SaIgnore
    @PostMapping("/buildTemplate")
    public String buildTemplate(@MyRequestBody RemoteTask remoteTask) {
        DockerComposeGenerator generator = new DockerComposeGenerator();
        // 创建容器配置列表
        List<ContainerConfig> containers = new ArrayList<>();

        
        ContainerConfig.ContainerConfigBuilder command = ContainerConfig.builder()
                .imageName(remoteTask.getImageName())
                .imageVersion(remoteTask.getImageVersion())
                .cpuLimit(String.valueOf(remoteTask.getCpuCore()))
                .memoryLimit(remoteTask.getMemorySize())
                .resourceType(remoteTask.getNeedResource())
                .containerPath(remoteTask.getContainerPath())
                .containerName(remoteTask.getContainerName())
                .serverMountPath(remoteTask.getContainerMapPath())
                .environment(remoteTask.getEnvironment())
                .volumes(remoteTask.getVolumeMounts())
                .command(remoteTask.getCommand()); // 使用转换后的列表
        containers.add(command.build());
        
        // 生成Docker Compose配置
        byte[] composeConfig = generator.generateComposeConfig(containers);
        return new String(composeConfig, StandardCharsets.UTF_8);
    }

    @SaIgnore
    @PostMapping("/remoteTaskStatus")
    @Operation(summary = "remoteTaskStatus",description = "远程任务状态接口(只用于远程任务状态查询)")
    public ResponseResult<List<SchTaskInfo>> remoteTaskStatus(@MyRequestBody List<String> taskIdList) {
        List<SchTaskInfo> schTaskInfos = schTaskInfoMapper.remoteTaskStatus(taskIdList);
        return ResponseResult.success(schTaskInfos);
    }

    @PostMapping("/remoteTask")
    @Operation(summary = "remoteTask",description = "远程任务接口用于任务启动")
    @SaIgnore
    public ResponseResult<RemoteTaskVo> remoteTask(@MyRequestBody RemoteTask remoteTask){
        return  ResponseResult.success(schTaskInfoService.remoteTask(remoteTask));
    }

    @Operation(summary = "relationResource",description = "资源池,资源，计算卡，资源切分")
    @GetMapping("/relationResource")
    @SaIgnore
    public ResponseEntity< ResponseResult<List<RootNode>>> relationResource() {
        List<RootNode> stringListMap = schTaskInfoService.relationResource();
        ResponseResult<List<RootNode>> result = ResponseResult.success(stringListMap);
        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_JSON)
                .body(result);
    }

    @Operation(summary = "startTask",description = "启动任务（排队调度)")
    @Parameter(name = "taskId",required = true,description = "任务表主键id",example = "19384749584028484")
    @OperationLog(type = SysOperationLogType.SUBMIT_TASK)
    @PostMapping("/startTask")
    public ResponseResult< SchTaskInfo> startTask(@MyRequestBody String  taskId){
        SchTaskInfo schTaskInfo = schTaskInfoMapper.selectById(Long.valueOf(taskId));
        //执行修改状态返回给前端
        schTaskInfoService.update(new LambdaUpdateWrapper<SchTaskInfo>()
                .eq(SchTaskInfo::getId, taskId)
                .set(SchTaskInfo::getStatus, "starting"));
        CompletableFuture.supplyAsync(()->{
            try {
               return   schTaskInfoService.startTask(Long.valueOf(taskId));
            } catch (NumberFormatException e) {
                log.error("任务重启异常: taskId={}, error={}", taskId, e.getMessage(), e);
                throw e;
            }
        },poolExecutor).exceptionally(ex->{
            log.error("任务异常: taskId={}, error={}", taskId, ex.getMessage(), ex);
            // 对任务状态还原执行失败
            schTaskInfoService.update(new LambdaUpdateWrapper<SchTaskInfo>()
                    .eq(SchTaskInfo::getId, taskId)
                            .set(SchTaskInfo::getUpdateTime,new Date())
                    .set(SchTaskInfo::getStatus, "failed"));
            return null;
        });

        return ResponseResult.success(schTaskInfoMapper.selectById(Long.valueOf(taskId)));
    }

    @SaIgnore
    @Operation(summary = "startTask",description = "远程任务启动（排队调度)")
    @Parameter(name = "taskId",required = true,description = "任务表主键id",example = "19384749584028484")
    @OperationLog(type = SysOperationLogType.SUBMIT_TASK)
    @PostMapping("/remoteStartTask")
    public ResponseResult< SchTaskInfoVo> remoteStartTask(@MyRequestBody String  taskId){
        SchTaskInfo schTaskInfo = schTaskInfoMapper.selectById(Long.valueOf(taskId));
        //执行修改状态返回给前端
        schTaskInfoService.update(new LambdaUpdateWrapper<SchTaskInfo>()
                .eq(SchTaskInfo::getId, taskId)
                .set(SchTaskInfo::getStatus, "starting"));
        //String status = schTaskInfo.getStatus();
        CompletableFuture.supplyAsync(()->{
            try {
                return   schTaskInfoService.startTask(Long.valueOf(taskId));
            } catch (NumberFormatException e) {
                log.error("任务重启异常: taskId={}, error={}", taskId, e.getMessage(), e);
                throw e;
            }
        },poolExecutor).exceptionally(ex->{
            log.error("任务异常: taskId={}, error={}", taskId, ex.getMessage(), ex);
            // 对任务状态还原执行失败
            schTaskInfoService.update(new LambdaUpdateWrapper<SchTaskInfo>()
                    .eq(SchTaskInfo::getId, taskId)
                    .set(SchTaskInfo::getUpdateTime,new Date()));

            return null;
        });
        //schTaskInfoMapper.queryStatus(Long.valueOf(taskId));
        return ResponseResult.success(schTaskInfoMapper.queryStatus(Long.valueOf(taskId)));
    }

    @Operation(summary = "restartTask",description = "重启任务")
    @Parameter(name = "taskId",required = true,description = "任务表主键id",example = "19384749584028484")
    @PostMapping("/restartTask")
    public ResponseResult< SchTaskInfo> restartTask(@MyRequestBody String taskId){
        SchTaskInfo schTaskInfo = schTaskInfoMapper.selectById(Long.valueOf(taskId));
        if (schTaskInfo == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, "任务不存在");
        }
        String originalStatus = schTaskInfo.getStatus();
        schTaskInfoService.update(new LambdaUpdateWrapper<SchTaskInfo>()
                .eq(SchTaskInfo::getId, taskId)
                .set(SchTaskInfo::getStatus, "restarting")
                .set(SchTaskInfo::getUpdateTime, new Date()));
        CompletableFuture.supplyAsync(() -> {
            try {
                return schTaskInfoService.restartTask(Long.valueOf(taskId));
            } catch (Exception e) {
                log.error("任务重启异常: taskId={}, error={}", taskId, e.getMessage(), e);
                throw e;
            }
        },poolExecutor).exceptionally(ex -> {
            log.error("任务重启异常: taskId={}, error={}", taskId, ex.getMessage(), ex);
            schTaskInfoService.update(new LambdaUpdateWrapper<SchTaskInfo>()
                    .eq(SchTaskInfo::getId, taskId)
                    .set(SchTaskInfo::getStatus, originalStatus));
                    //.set(SchTaskInfo::getUpdateTime, new Date()));
            return null;
        });
        return ResponseResult.success(schTaskInfoMapper.selectById(Long.valueOf(taskId)));
    }

    @SaIgnore
    @Operation(summary = "remoteRestartTask",description = "远程重启任务")
    @Parameter(name = "taskId",required = true,description = "任务表主键id",example = "19384749584028484")
    @PostMapping("/remoteRestartTask")
    public ResponseResult< SchTaskInfoVo> remoteRestartTask(@MyRequestBody String taskId){
        SchTaskInfo schTaskInfo = schTaskInfoMapper.selectById(Long.valueOf(taskId));
        if (schTaskInfo == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, "任务不存在");
        }
        String originalStatus = schTaskInfo.getStatus();
        schTaskInfoService.update(new LambdaUpdateWrapper<SchTaskInfo>()
                .eq(SchTaskInfo::getId, taskId)
                .set(SchTaskInfo::getStatus, "restarting")
                .set(SchTaskInfo::getUpdateTime, new Date()));
        CompletableFuture.supplyAsync(() -> {
            try {
                return schTaskInfoService.restartTask(Long.valueOf(taskId));
            } catch (Exception e) {
                log.error("任务重启异常: taskId={}, error={}", taskId, e.getMessage(), e);
                throw e;
            }
        },poolExecutor).exceptionally(ex -> {
            log.error("任务重启异常: taskId={}, error={}", taskId, ex.getMessage(), ex);
            schTaskInfoService.update(new LambdaUpdateWrapper<SchTaskInfo>()
                    .eq(SchTaskInfo::getId, taskId)
                    .set(SchTaskInfo::getStatus, originalStatus)
                    .set(SchTaskInfo::getUpdateTime, new Date()));
            return null;
        });
        return ResponseResult.success(schTaskInfoMapper.queryStatus(Long.valueOf(taskId)));
    }

    @Operation(summary = "stopTask", description = "停止任务")
    @Parameter(name = "taskId", required = true, description = "任务表主键id", example = "19384749584028484")
    @PostMapping("/stop")
    public ResponseResult<SchTaskInfo> stopTask(@MyRequestBody String taskId) {
        // 参数校验
        if (taskId == null || taskId.trim().isEmpty()) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST, "任务ID不能为空");
        }
        // 查询任务信息
        SchTaskInfo schTaskInfo = schTaskInfoMapper.selectById(Long.valueOf(taskId));
        if (schTaskInfo == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, "任务不存在");
        }
        String originalStatus = schTaskInfo.getStatus();
        schTaskInfoService.update(new LambdaUpdateWrapper<SchTaskInfo>()
                .eq(SchTaskInfo::getId, taskId)
                .set(SchTaskInfo::getStatus, "stopping")
                .set(SchTaskInfo::getUpdateTime, new Date()));
        CompletableFuture.supplyAsync(() -> {
            try {
                return schTaskInfoService.stopTask(Long.valueOf(taskId));
            } catch (Exception e) {
                log.error("任务停止异常: taskId={}, error={}", taskId, e.getMessage(), e);
                throw e;
            }
        },poolExecutor).exceptionally(ex -> {
            log.error("任务停止异常: taskId={}, error={}", taskId, ex.getMessage(), ex);
            // 发生异常时回滚状态
            schTaskInfoService.update(new LambdaUpdateWrapper<SchTaskInfo>()
                    .eq(SchTaskInfo::getId, taskId)
                    .set(SchTaskInfo::getStatus, originalStatus)
                    .set(SchTaskInfo::getUpdateTime, new Date()));
            return null;
        });
        return ResponseResult.success(schTaskInfoMapper.selectById(Long.valueOf(taskId)));
    }
    @SaIgnore
    @Operation(summary = "remoteStopTask", description = "远程停止任务")
    @Parameter(name = "taskId", required = true, description = "任务表主键id", example = "19384749584028484")
    @PostMapping("/remoteStopTask")
    public ResponseResult<SchTaskInfo> remoteStopTask(@MyRequestBody String taskId) {
        // 参数校验
        if (taskId == null || taskId.trim().isEmpty()) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST, "任务ID不能为空");
        }
        // 查询任务信息
        SchTaskInfo schTaskInfo = schTaskInfoMapper.selectById(Long.valueOf(taskId));
        if (schTaskInfo == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, "任务不存在");
        }
        String originalStatus = schTaskInfo.getStatus();
        schTaskInfoService.update(new LambdaUpdateWrapper<SchTaskInfo>()
                .eq(SchTaskInfo::getId, taskId)
                .set(SchTaskInfo::getStatus, "stopping")
                .set(SchTaskInfo::getUpdateTime, new Date()));
        CompletableFuture.supplyAsync(() -> {
            try {
                return schTaskInfoService.stopTask(Long.valueOf(taskId));
            } catch (Exception e) {
                log.error("任务停止异常: taskId={}, error={}", taskId, e.getMessage(), e);
                throw e;
            }
        },poolExecutor).exceptionally(ex -> {
            log.error("任务停止异常: taskId={}, error={}", taskId, ex.getMessage(), ex);
            // 发生异常时回滚状态
            schTaskInfoService.update(new LambdaUpdateWrapper<SchTaskInfo>()
                    .eq(SchTaskInfo::getId, taskId)
                    .set(SchTaskInfo::getStatus, originalStatus)
                    .set(SchTaskInfo::getUpdateTime, new Date()));
            return null;
        });
        return ResponseResult.success(schTaskInfoMapper.selectById(Long.valueOf(taskId)));
    }

    /**
     * 批量启动任务
     * @return 启动结果数据
     */
    @PostMapping("/batchStart")
    @Operation(summary = "batchStart",description = "批量启动任务")
    @Parameter(name = "idList",required = true,description = "任务表主键id列表",example = "[19384749584028484,19384749584028485]")
    public ResponseResult<List<SchTaskInfo>> batchStart( @MyRequestBody  List<String>  idList) {
        List<SchTaskInfo> schTaskInfos = schTaskInfoService.batchStart(idList);
        return ResponseResult.success(schTaskInfos);
    }


    /**
     * 批量启动任务
     * @return 启动结果数据
     */
    @PostMapping("/batchStop")
    @Operation(summary = "batchStop",description = "批量停止")
    @Parameter(name = "idList",required = true,description = "任务表主键id列表",example = "[19384749584028484,19384749584028485]")
    public ResponseResult<List<SchTaskInfo>> batchStop( @MyRequestBody  List<String>  idList) {
        List<SchTaskInfo> schTaskInfos = schTaskInfoService.batchStop(idList);
        return ResponseResult.success(schTaskInfos);
    }



    @Operation(summary = "priorityDispatch",description = "抢占调度")
    @Parameter(name = "taskId",required = true,description = "任务表主键id",example = "19384749584028484")
    @PostMapping("/priorityDispatch")
    public ResponseResult<SchTaskInfoVo> priorityDispatch( @MyRequestBody SchTaskInfoDto schTaskInfoDto ) {
        SchTaskInfo schTaskInfo = schTaskInfoService.priorityDispatch(schTaskInfoDto);
        return ResponseResult.success( MyModelUtil.copyTo(schTaskInfo,SchTaskInfoVo.class));
    }
    /**
     * 新增任务表数据。
     *
     * @param schTaskInfoDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schTaskInfoDto.id",
            "schTaskInfoDto.searchString",
            "schTaskInfoDto.updateTimeStart",
            "schTaskInfoDto.updateTimeEnd",
            "schTaskInfoDto.createTimeStart",
            "schTaskInfoDto.createTimeEnd",
            "schTaskInfoDto.startTimeStart",
            "schTaskInfoDto.startTimeEnd",
            "schTaskInfoDto.endTiemStart",
            "schTaskInfoDto.endTiemEnd",
            "schTaskInfoDto.estimatTimeStart",
            "schTaskInfoDto.estimatTimeEnd"})
    @SaCheckPermission("schTaskInfo.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody SchTaskInfoDto schTaskInfoDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schTaskInfoDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchTaskInfo schTaskInfo = MyModelUtil.copyTo(schTaskInfoDto, SchTaskInfo.class);
        schTaskInfo = schTaskInfoService.saveNew(schTaskInfo);
        return ResponseResult.success(schTaskInfo.getId());
    }

    /**
     * 更新任务表数据。
     *
     * @param schTaskInfoDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schTaskInfoDto.searchString",
            "schTaskInfoDto.updateTimeStart",
            "schTaskInfoDto.updateTimeEnd",
            "schTaskInfoDto.createTimeStart",
            "schTaskInfoDto.createTimeEnd",
            "schTaskInfoDto.startTimeStart",
            "schTaskInfoDto.startTimeEnd",
            "schTaskInfoDto.endTiemStart",
            "schTaskInfoDto.endTiemEnd",
            "schTaskInfoDto.estimatTimeStart",
            "schTaskInfoDto.estimatTimeEnd"})
    @SaCheckPermission("schTaskInfo.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody SchTaskInfoDto schTaskInfoDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schTaskInfoDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchTaskInfo schTaskInfo = MyModelUtil.copyTo(schTaskInfoDto, SchTaskInfo.class);
        SchTaskInfo originalSchTaskInfo = schTaskInfoService.getById(schTaskInfo.getId());
        if (originalSchTaskInfo == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schTaskInfoService.update(schTaskInfo, originalSchTaskInfo)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除任务表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schTaskInfo.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除任务表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schTaskInfo.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的任务表列表。
     *
     * @param schTaskInfoDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schTaskInfo.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<SchTaskInfoVo>> list(
            @MyRequestBody SchTaskInfoDto schTaskInfoDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchTaskInfo schTaskInfoFilter = MyModelUtil.copyTo(schTaskInfoDtoFilter, SchTaskInfo.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchTaskInfo.class);
        List<SchTaskInfo> schTaskInfoList =
                schTaskInfoService.getSchTaskInfoListWithRelation(schTaskInfoFilter, orderBy);
        // 手动构件多对多关联
        return ResponseResult.success(MyPageUtil.makeResponseData(schTaskInfoList, SchTaskInfoVo.class));
    }

    /**
     * 分组列出符合过滤条件的任务表列表。
     *
     * @param schTaskInfoDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schTaskInfo.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<SchTaskInfoVo>> listWithGroup(
            @MyRequestBody SchTaskInfoDto schTaskInfoDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchTaskInfo.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, SchTaskInfo.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchTaskInfo filter = MyModelUtil.copyTo(schTaskInfoDtoFilter, SchTaskInfo.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<SchTaskInfo> resultList = schTaskInfoService.getGroupedSchTaskInfoListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, SchTaskInfoVo.class));
    }

    /**
     * 查看指定任务表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @SaCheckPermission("schTaskInfo.view")
    @GetMapping("/view")
    public ResponseResult<SchTaskInfoVo> view(@RequestParam Long id) {
        SchTaskInfo schTaskInfo = schTaskInfoService.getByIdWithRelation(id, MyRelationParam.full());
        if (schTaskInfo == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        SchTaskInfoVo schTaskInfoVo = MyModelUtil.copyTo(schTaskInfo, SchTaskInfoVo.class);
        return ResponseResult.success(schTaskInfoVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        SchTaskInfo originalSchTaskInfo = schTaskInfoService.getById(id);
        if (originalSchTaskInfo == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schTaskInfoService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }

    /**
     * 任务资源占用
     *
     */
    @PostMapping("/taskOccupyResource")
    public ResponseResult<List<SchTaskInfo>> taskOccupyResource(@MyRequestBody Long resourceInfoId, @MyRequestBody Long resourcePoolId) {
        return ResponseResult.success(schTaskInfoService.taskOccupyResource(resourceInfoId,resourcePoolId));
    }

    /**
     * 获取任务的VNPU内存使用情况
     *
     * @param taskId 任务ID
     * @return 应答结果对象，包含VNPU内存使用情况
     */
    /*@GetMapping("/getTaskVnpuMemoryUsage")
    public ResponseResult<Map<String, Object>> getTaskVnpuMemoryUsage(@RequestParam Long taskId) {
       // return ResponseResult.success(schTaskInfoService.getTaskVnpuMemoryUsage(taskId));
    }*/
}
