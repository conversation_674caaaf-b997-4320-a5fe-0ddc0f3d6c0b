package supie.webadmin.app.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import supie.common.log.annotation.OperationLog;
import supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;
import supie.webadmin.app.vo.*;
import supie.webadmin.app.dto.*;
import supie.webadmin.app.model.*;
import supie.webadmin.app.service.*;
import supie.common.core.object.*;
import supie.common.core.util.*;
import supie.common.core.constant.*;
import supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 任务日志表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Tag(name = "任务日志表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/schTaskLogs")
public class SchTaskLogsController {

    @Autowired
    private SchTaskLogsService schTaskLogsService;

    /**
     * 新增任务日志表数据。
     *
     * @param schTaskLogsDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schTaskLogsDto.id",
            "schTaskLogsDto.searchString",
            "schTaskLogsDto.updateTimeStart",
            "schTaskLogsDto.updateTimeEnd",
            "schTaskLogsDto.createTimeStart",
            "schTaskLogsDto.createTimeEnd"})
    @SaCheckPermission("schTaskLogs.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody SchTaskLogsDto schTaskLogsDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schTaskLogsDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchTaskLogs schTaskLogs = MyModelUtil.copyTo(schTaskLogsDto, SchTaskLogs.class);
        schTaskLogs = schTaskLogsService.saveNew(schTaskLogs);
        return ResponseResult.success(schTaskLogs.getId());
    }

    /**
     * 更新任务日志表数据。
     *
     * @param schTaskLogsDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schTaskLogsDto.searchString",
            "schTaskLogsDto.updateTimeStart",
            "schTaskLogsDto.updateTimeEnd",
            "schTaskLogsDto.createTimeStart",
            "schTaskLogsDto.createTimeEnd"})
    @SaCheckPermission("schTaskLogs.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody SchTaskLogsDto schTaskLogsDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schTaskLogsDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchTaskLogs schTaskLogs = MyModelUtil.copyTo(schTaskLogsDto, SchTaskLogs.class);
        SchTaskLogs originalSchTaskLogs = schTaskLogsService.getById(schTaskLogs.getId());
        if (originalSchTaskLogs == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schTaskLogsService.update(schTaskLogs, originalSchTaskLogs)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除任务日志表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schTaskLogs.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除任务日志表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schTaskLogs.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的任务日志表列表。
     *
     * @param schTaskLogsDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schTaskLogs.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<SchTaskLogsVo>> list(
            @MyRequestBody SchTaskLogsDto schTaskLogsDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchTaskLogs schTaskLogsFilter = MyModelUtil.copyTo(schTaskLogsDtoFilter, SchTaskLogs.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchTaskLogs.class);
        List<SchTaskLogs> schTaskLogsList =
                schTaskLogsService.getSchTaskLogsListWithRelation(schTaskLogsFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(schTaskLogsList, SchTaskLogsVo.class));
    }

    /**
     * 分组列出符合过滤条件的任务日志表列表。
     *
     * @param schTaskLogsDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schTaskLogs.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<SchTaskLogsVo>> listWithGroup(
            @MyRequestBody SchTaskLogsDto schTaskLogsDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchTaskLogs.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, SchTaskLogs.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchTaskLogs filter = MyModelUtil.copyTo(schTaskLogsDtoFilter, SchTaskLogs.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<SchTaskLogs> resultList = schTaskLogsService.getGroupedSchTaskLogsListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, SchTaskLogsVo.class));
    }

    /**
     * 查看指定任务日志表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @SaCheckPermission("schTaskLogs.view")
    @GetMapping("/view")
    public ResponseResult<SchTaskLogsVo> view(@RequestParam Long id) {
        SchTaskLogs schTaskLogs = schTaskLogsService.getByIdWithRelation(id, MyRelationParam.full());
        if (schTaskLogs == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        SchTaskLogsVo schTaskLogsVo = MyModelUtil.copyTo(schTaskLogs, SchTaskLogsVo.class);
        return ResponseResult.success(schTaskLogsVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        SchTaskLogs originalSchTaskLogs = schTaskLogsService.getById(id);
        if (originalSchTaskLogs == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schTaskLogsService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
