package supie.webadmin.app.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import supie.common.log.annotation.OperationLog;
import supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;
import supie.webadmin.app.vo.*;
import supie.webadmin.app.dto.*;
import supie.webadmin.app.model.*;
import supie.webadmin.app.service.*;
import supie.common.core.object.*;
import supie.common.core.util.*;
import supie.common.core.constant.*;
import supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 资源切分管理表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Tag(name = "资源切分管理表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/schResourceSlice")
public class SchResourceSliceController {

    @Autowired
    private SchResourceSliceService schResourceSliceService;

    /**
     * 新增资源切分管理表数据。
     *
     * @param schResourceSliceDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schResourceSliceDto.id",
            "schResourceSliceDto.searchString",
            "schResourceSliceDto.updateTimeStart",
            "schResourceSliceDto.updateTimeEnd",
            "schResourceSliceDto.createTimeStart",
            "schResourceSliceDto.createTimeEnd"})
    @SaCheckPermission("schResourceSlice.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody SchResourceSliceDto schResourceSliceDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schResourceSliceDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchResourceSlice schResourceSlice = MyModelUtil.copyTo(schResourceSliceDto, SchResourceSlice.class);
        schResourceSlice = schResourceSliceService.saveNew(schResourceSlice);
        return ResponseResult.success(schResourceSlice.getId());
    }

    /**
     * 更新资源切分管理表数据。
     *
     * @param schResourceSliceDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schResourceSliceDto.searchString",
            "schResourceSliceDto.updateTimeStart",
            "schResourceSliceDto.updateTimeEnd",
            "schResourceSliceDto.createTimeStart",
            "schResourceSliceDto.createTimeEnd"})
    @SaCheckPermission("schResourceSlice.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody SchResourceSliceDto schResourceSliceDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schResourceSliceDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchResourceSlice schResourceSlice = MyModelUtil.copyTo(schResourceSliceDto, SchResourceSlice.class);
        SchResourceSlice originalSchResourceSlice = schResourceSliceService.getById(schResourceSlice.getId());
        if (originalSchResourceSlice == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schResourceSliceService.update(schResourceSlice, originalSchResourceSlice)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除资源切分管理表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schResourceSlice.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除资源切分管理表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schResourceSlice.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的资源切分管理表列表。
     *
     * @param schResourceSliceDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schResourceSlice.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<SchResourceSliceVo>> list(
            @MyRequestBody SchResourceSliceDto schResourceSliceDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchResourceSlice schResourceSliceFilter = MyModelUtil.copyTo(schResourceSliceDtoFilter, SchResourceSlice.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchResourceSlice.class);
        List<SchResourceSlice> schResourceSliceList =
                schResourceSliceService.getSchResourceSliceListWithRelation(schResourceSliceFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(schResourceSliceList, SchResourceSliceVo.class));
    }

    /**
     * 分组列出符合过滤条件的资源切分管理表列表。
     *
     * @param schResourceSliceDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schResourceSlice.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<SchResourceSliceVo>> listWithGroup(
            @MyRequestBody SchResourceSliceDto schResourceSliceDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchResourceSlice.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, SchResourceSlice.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchResourceSlice filter = MyModelUtil.copyTo(schResourceSliceDtoFilter, SchResourceSlice.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<SchResourceSlice> resultList = schResourceSliceService.getGroupedSchResourceSliceListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, SchResourceSliceVo.class));
    }

    /**
     * 查看指定资源切分管理表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @SaCheckPermission("schResourceSlice.view")
    @GetMapping("/view")
    public ResponseResult<SchResourceSliceVo> view(@RequestParam Long id) {
        SchResourceSlice schResourceSlice = schResourceSliceService.getByIdWithRelation(id, MyRelationParam.full());
        if (schResourceSlice == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        SchResourceSliceVo schResourceSliceVo = MyModelUtil.copyTo(schResourceSlice, SchResourceSliceVo.class);
        return ResponseResult.success(schResourceSliceVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        SchResourceSlice originalSchResourceSlice = schResourceSliceService.getById(id);
        if (originalSchResourceSlice == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schResourceSliceService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
