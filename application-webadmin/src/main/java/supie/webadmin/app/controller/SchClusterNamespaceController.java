package supie.webadmin.app.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import supie.common.log.annotation.OperationLog;
import supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;
import supie.webadmin.app.vo.*;
import supie.webadmin.app.dto.*;
import supie.webadmin.app.model.*;
import supie.webadmin.app.service.*;
import supie.common.core.object.*;
import supie.common.core.util.*;
import supie.common.core.constant.*;
import supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 集群命名空间管理操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Tag(name = "k8s集群命名空间管理管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/schClusterNamespace")
public class SchClusterNamespaceController {

    @Autowired
    private SchClusterNamespaceService schClusterNamespaceService;

    /**
     * 新增集群命名空间管理数据。
     *
     * @param schClusterNamespaceDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schClusterNamespaceDto.id",
            "schClusterNamespaceDto.searchString",
            "schClusterNamespaceDto.updateTimeStart",
            "schClusterNamespaceDto.updateTimeEnd",
            "schClusterNamespaceDto.createTimeStart",
            "schClusterNamespaceDto.createTimeEnd"})
    @SaCheckPermission("schClusterNamespace.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody SchClusterNamespaceDto schClusterNamespaceDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schClusterNamespaceDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchClusterNamespace schClusterNamespace = MyModelUtil.copyTo(schClusterNamespaceDto, SchClusterNamespace.class);
        schClusterNamespace = schClusterNamespaceService.saveNew(schClusterNamespace);
        return ResponseResult.success(schClusterNamespace.getId());
    }

    /**
     * 更新集群命名空间管理数据。
     *
     * @param schClusterNamespaceDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schClusterNamespaceDto.searchString",
            "schClusterNamespaceDto.updateTimeStart",
            "schClusterNamespaceDto.updateTimeEnd",
            "schClusterNamespaceDto.createTimeStart",
            "schClusterNamespaceDto.createTimeEnd"})
    @SaCheckPermission("schClusterNamespace.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody SchClusterNamespaceDto schClusterNamespaceDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schClusterNamespaceDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchClusterNamespace schClusterNamespace = MyModelUtil.copyTo(schClusterNamespaceDto, SchClusterNamespace.class);
        SchClusterNamespace originalSchClusterNamespace = schClusterNamespaceService.getById(schClusterNamespace.getId());
        if (originalSchClusterNamespace == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schClusterNamespaceService.update(schClusterNamespace, originalSchClusterNamespace)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除集群命名空间管理数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schClusterNamespace.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除集群命名空间管理数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schClusterNamespace.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的集群命名空间管理列表。
     *
     * @param schClusterNamespaceDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schClusterNamespace.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<SchClusterNamespaceVo>> list(
            @MyRequestBody SchClusterNamespaceDto schClusterNamespaceDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchClusterNamespace schClusterNamespaceFilter = MyModelUtil.copyTo(schClusterNamespaceDtoFilter, SchClusterNamespace.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchClusterNamespace.class);
        List<SchClusterNamespace> schClusterNamespaceList =
                schClusterNamespaceService.getSchClusterNamespaceListWithRelation(schClusterNamespaceFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(schClusterNamespaceList, SchClusterNamespaceVo.class));
    }

    /**
     * 分组列出符合过滤条件的集群命名空间管理列表。
     *
     * @param schClusterNamespaceDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schClusterNamespace.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<SchClusterNamespaceVo>> listWithGroup(
            @MyRequestBody SchClusterNamespaceDto schClusterNamespaceDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchClusterNamespace.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, SchClusterNamespace.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchClusterNamespace filter = MyModelUtil.copyTo(schClusterNamespaceDtoFilter, SchClusterNamespace.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<SchClusterNamespace> resultList = schClusterNamespaceService.getGroupedSchClusterNamespaceListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, SchClusterNamespaceVo.class));
    }

    /**
     * 查看指定集群命名空间管理对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @SaCheckPermission("schClusterNamespace.view")
    @GetMapping("/view")
    public ResponseResult<SchClusterNamespaceVo> view(@RequestParam Long id) {
        SchClusterNamespace schClusterNamespace = schClusterNamespaceService.getByIdWithRelation(id, MyRelationParam.full());
        if (schClusterNamespace == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        SchClusterNamespaceVo schClusterNamespaceVo = MyModelUtil.copyTo(schClusterNamespace, SchClusterNamespaceVo.class);
        return ResponseResult.success(schClusterNamespaceVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        SchClusterNamespace originalSchClusterNamespace = schClusterNamespaceService.getById(id);
        if (originalSchClusterNamespace == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schClusterNamespaceService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }

    /**
     * 集群命名空间统计。
     *
     * @return 应答结果对象，包含对象详情。
     */
    @GetMapping("/clusterNamespaceStatistics")
    public ResponseResult<Map<String,Object>> clusterNamespaceStatistics() {
        return ResponseResult.success(schClusterNamespaceService.clusterNamespaceStatistics());
    }
}
