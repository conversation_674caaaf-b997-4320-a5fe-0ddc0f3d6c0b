package supie.webadmin.app.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import supie.common.log.annotation.OperationLog;
import supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;
import supie.webadmin.app.dao.SchTaskTemplateMapper;
import supie.webadmin.app.vo.*;
import supie.webadmin.app.dto.*;
import supie.webadmin.app.model.*;
import supie.webadmin.app.service.*;
import supie.common.core.object.*;
import supie.common.core.util.*;
import supie.common.core.constant.*;
import supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 新建任务模板表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Tag(name = "新建任务模板表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/schTaskTemplate")
public class SchTaskTemplateController {

    @Autowired
    private SchTaskTemplateService schTaskTemplateService;

    @Resource
    private SchTaskTemplateMapper schTaskTemplateMapper;


    @GetMapping("/templateCount")
    @Operation(summary = "templateCount",description = "统计模板总数、最近更新镜像数、关联镜像数")
    public ResponseResult<TemplateCount> templateCount(){
        return ResponseResult.success( schTaskTemplateMapper.templateCount());
    }

    /**
     * 新增新建任务模板表数据。
     *
     * @param schTaskTemplateDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schTaskTemplateDto.id",
            "schTaskTemplateDto.searchString",
            "schTaskTemplateDto.updateTimeStart",
            "schTaskTemplateDto.updateTimeEnd",
            "schTaskTemplateDto.createTimeStart",
            "schTaskTemplateDto.createTimeEnd"})
    @SaCheckPermission("schTaskTemplate.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody SchTaskTemplateDto schTaskTemplateDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schTaskTemplateDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchTaskTemplate schTaskTemplate = MyModelUtil.copyTo(schTaskTemplateDto, SchTaskTemplate.class);
        schTaskTemplate = schTaskTemplateService.saveNew(schTaskTemplate);
        return ResponseResult.success(schTaskTemplate.getId());
    }

    /**
     * 更新新建任务模板表数据。
     *
     * @param schTaskTemplateDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schTaskTemplateDto.searchString",
            "schTaskTemplateDto.updateTimeStart",
            "schTaskTemplateDto.updateTimeEnd",
            "schTaskTemplateDto.createTimeStart",
            "schTaskTemplateDto.createTimeEnd"})
    @SaCheckPermission("schTaskTemplate.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody SchTaskTemplateDto schTaskTemplateDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schTaskTemplateDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchTaskTemplate schTaskTemplate = MyModelUtil.copyTo(schTaskTemplateDto, SchTaskTemplate.class);
        SchTaskTemplate originalSchTaskTemplate = schTaskTemplateService.getById(schTaskTemplate.getId());
        if (originalSchTaskTemplate == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schTaskTemplateService.update(schTaskTemplate, originalSchTaskTemplate)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除新建任务模板表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schTaskTemplate.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除新建任务模板表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schTaskTemplate.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的新建任务模板表列表。
     *
     * @param schTaskTemplateDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schTaskTemplate.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<SchTaskTemplateVo>> list(
            @MyRequestBody SchTaskTemplateDto schTaskTemplateDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchTaskTemplate schTaskTemplateFilter = MyModelUtil.copyTo(schTaskTemplateDtoFilter, SchTaskTemplate.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchTaskTemplate.class);
        List<SchTaskTemplate> schTaskTemplateList =
                schTaskTemplateService.getSchTaskTemplateListWithRelation(schTaskTemplateFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(schTaskTemplateList, SchTaskTemplateVo.class));
    }

    /**
     * 分组列出符合过滤条件的新建任务模板表列表。
     *
     * @param schTaskTemplateDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schTaskTemplate.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<SchTaskTemplateVo>> listWithGroup(
            @MyRequestBody SchTaskTemplateDto schTaskTemplateDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchTaskTemplate.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, SchTaskTemplate.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchTaskTemplate filter = MyModelUtil.copyTo(schTaskTemplateDtoFilter, SchTaskTemplate.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<SchTaskTemplate> resultList = schTaskTemplateService.getGroupedSchTaskTemplateListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, SchTaskTemplateVo.class));
    }

    /**
     * 查看指定新建任务模板表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @SaCheckPermission("schTaskTemplate.view")
    @GetMapping("/view")
    public ResponseResult<SchTaskTemplateVo> view(@RequestParam Long id) {
        SchTaskTemplate schTaskTemplate = schTaskTemplateService.getByIdWithRelation(id, MyRelationParam.full());
        if (schTaskTemplate == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        SchTaskTemplateVo schTaskTemplateVo = MyModelUtil.copyTo(schTaskTemplate, SchTaskTemplateVo.class);
        return ResponseResult.success(schTaskTemplateVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        SchTaskTemplate originalSchTaskTemplate = schTaskTemplateService.getById(id);
        if (originalSchTaskTemplate == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schTaskTemplateService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
