package supie.webadmin.app.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import supie.common.log.annotation.OperationLog;
import supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;
import supie.webadmin.app.vo.*;
import supie.webadmin.app.dto.*;
import supie.webadmin.app.model.*;
import supie.webadmin.app.service.*;
import supie.common.core.object.*;
import supie.common.core.util.*;
import supie.common.core.constant.*;
import supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * k8s部署管理操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Tag(name = "k8s部署管理管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/schDeployment")
public class SchDeploymentController {

    @Autowired
    private SchDeploymentService schDeploymentService;

    /**
     * 新增k8s部署管理数据。
     *
     * @param schDeploymentDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schDeploymentDto.id",
            "schDeploymentDto.searchString",
            "schDeploymentDto.updateTimeStart",
            "schDeploymentDto.updateTimeEnd",
            "schDeploymentDto.createTimeStart",
            "schDeploymentDto.createTimeEnd"})
    @SaCheckPermission("schDeployment.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<String> add(@MyRequestBody SchDeploymentDto schDeploymentDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schDeploymentDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchDeployment schDeployment = MyModelUtil.copyTo(schDeploymentDto, SchDeployment.class);
        schDeployment = schDeploymentService.saveNew(schDeployment);
        return ResponseResult.success(schDeployment.getId());
    }

    /**
     * 更新k8s部署管理数据。
     *
     * @param schDeploymentDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schDeploymentDto.searchString",
            "schDeploymentDto.updateTimeStart",
            "schDeploymentDto.updateTimeEnd",
            "schDeploymentDto.createTimeStart",
            "schDeploymentDto.createTimeEnd"})
    @SaCheckPermission("schDeployment.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody SchDeploymentDto schDeploymentDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schDeploymentDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchDeployment schDeployment = MyModelUtil.copyTo(schDeploymentDto, SchDeployment.class);
        SchDeployment originalSchDeployment = schDeploymentService.getById(schDeployment.getId());
        if (originalSchDeployment == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schDeploymentService.update(schDeployment, originalSchDeployment)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除k8s部署管理数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schDeployment.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody String id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除k8s部署管理数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schDeployment.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<String> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (String id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的k8s部署管理列表。
     *
     * @param schDeploymentDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schDeployment.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<SchDeploymentVo>> list(
            @MyRequestBody SchDeploymentDto schDeploymentDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchDeployment schDeploymentFilter = MyModelUtil.copyTo(schDeploymentDtoFilter, SchDeployment.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchDeployment.class);
        List<SchDeployment> schDeploymentList =
                schDeploymentService.getSchDeploymentListWithRelation(schDeploymentFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(schDeploymentList, SchDeploymentVo.class));
    }

    /**
     * 分组列出符合过滤条件的k8s部署管理列表。
     *
     * @param schDeploymentDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schDeployment.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<SchDeploymentVo>> listWithGroup(
            @MyRequestBody SchDeploymentDto schDeploymentDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchDeployment.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, SchDeployment.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchDeployment filter = MyModelUtil.copyTo(schDeploymentDtoFilter, SchDeployment.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<SchDeployment> resultList = schDeploymentService.getGroupedSchDeploymentListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, SchDeploymentVo.class));
    }

    /**
     * 查看指定k8s部署管理对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @SaCheckPermission("schDeployment.view")
    @GetMapping("/view")
    public ResponseResult<SchDeploymentVo> view(@RequestParam String id) {
        SchDeployment schDeployment = schDeploymentService.getByIdWithRelation(id, MyRelationParam.full());
        if (schDeployment == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        SchDeploymentVo schDeploymentVo = MyModelUtil.copyTo(schDeployment, SchDeploymentVo.class);
        return ResponseResult.success(schDeploymentVo);
    }

    private ResponseResult<Void> doDelete(String id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        SchDeployment originalSchDeployment = schDeploymentService.getById(id);
        if (originalSchDeployment == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schDeploymentService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
