package supie.webadmin.app.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 资源切分管理表VO视图对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Schema(description = "资源切分管理表VO视图对象")
@Data
public class SchResourceSliceVo {

    /**
     * 编号。
     */
    @Schema(description = "编号")
    private Long id;

    /**
     * 字符id。
     */
    @Schema(description = "字符id")
    private String strId;

    /**
     * 更新时间。
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 创建时间。
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 创建人。
     */
    @Schema(description = "创建人")
    private Long createUserId;

    /**
     * 更新人。
     */
    @Schema(description = "更新人")
    private Long updateUserId;

    /**
     * 数据所属人。
     */
    @Schema(description = "数据所属人")
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @Schema(description = "数据所属部门")
    private Long dataDeptId;

    /**
     * 切分单元名称。
     */
    @Schema(description = "切分单元名称")
    private String partitionName;

    /**
     * 所属资源ID(可选)。
     */
    @Schema(description = "所属资源ID(可选)")
    private Long resourceId;

    /**
     * 所属计算卡ID（可选）。
     */
    @Schema(description = "所属计算卡ID（可选）")
    private Long computeDeviceId;

    /**
     * 算力百分比（1%~100%）。
     */
    @Schema(description = "算力百分比（1%~100%）")
    private String computePercent;

    /**
     * 计算卡的标签。
     */
    @Schema(description = "计算卡的标签")
    private Integer computeDeviceIndex;

    /**
     * 显存（MB）。
     */
    @Schema(description = "显存（MB）")
    private Integer graphicsMemorySize;

    /**
     * 显存（百分比）。
     */
    @Schema(description = "显存（百分比）")
    private BigDecimal graphicsMemoryRate;

    /**
     * 内存（MB）。
     */
    @Schema(description = "内存（MB）")
    private Integer memorySize;

    /**
     * 隔离方式（time/space）。
     */
    @Schema(description = "隔离方式（time/space）")
    private String isolationMode;

    /**
     * 状态（异常、运行中）。
     */
    @Schema(description = "状态（异常、运行中）")
    private String status;

    /**
     * 当前占用该分区的任务 ID（如果有）。
     */
    @Schema(description = "当前占用该分区的任务 ID（如果有）")
    private Long taskId;
}
