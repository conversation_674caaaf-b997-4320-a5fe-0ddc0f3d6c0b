package supie.webadmin.app.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import supie.common.core.base.vo.BaseVo;

/**
 * 业务附件表VO视图对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-02-17
 */
@Schema(description = "业务附件表VO视图对象")
@Data
@EqualsAndHashCode(callSuper = true)
public class BusinessFileVo extends BaseVo {

    /**
     * 编号。
     */
    @Schema(description = "编号")
    private Long id;

    /**
     * 字符编号。
     */
    @Schema(description = "字符编号")
    private String strId;

    /**
     * 数据所属人。
     */
    @Schema(description = "数据所属人")
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @Schema(description = "数据所属部门")
    private Long dataDeptId;

    /**
     * 文件名称。
     */
    @Schema(description = "文件名称")
    private String fileName;

    /**
     * json字段。
     */
    @Schema(description = "json字段")
    private String fileJson;

    /**
     * 绑定字符id。
     */
    private String bindStrId;

    /**
     * 绑定类型。
     */
    private String bindType;

    /**
     * 文件大小。
     */
    @Schema(description = "文件大小")
    private Long fileSize;

    /**
     * 文件类型。
     */
    @Schema(description = "文件类型")
    private String fileType;

    /**
     * 文件扩展类型。
     */
    @Schema(description = "文件扩展类型")
    private String fileFormat;

    /**
     * 备注。
     */
    @Schema(description = "备注")
    private String fileRemark;

    /**
     * 是否图片。
     */
    @Schema(description = "是否图片")
    private String asImage;
}
