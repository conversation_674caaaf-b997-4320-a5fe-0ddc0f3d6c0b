package supie.webadmin.app.dto;

import supie.common.core.validator.UpdateGroup;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;

/**
 * K8S服务管理表Dto对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Schema(description = "K8S服务管理表Dto对象")
@Data
public class SchServiceManagerDto {

    /**
     * 服务主键id。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "服务主键id。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    /**
     * 服务名称。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "服务名称。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    private String serviceName;

    /**
     * 命名空间ID。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "命名空间ID。可支持等于操作符的列表数据过滤。")
    private Long namespaceId;

    /**
     * 集群ID。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "集群ID。可支持等于操作符的列表数据过滤。")
    private Long clusterId;

    /**
     * 服务类型(ClusterIP/NodePort/LoadBalancer)。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "服务类型(ClusterIP/NodePort/LoadBalancer)。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    private String serviceType;

    /**
     * 选择器标签(JSON格式)。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "选择器标签(JSON格式)。可支持等于操作符的列表数据过滤。")
    private String selectorLabels;

    /**
     * 集群IP。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "集群IP。可支持等于操作符的列表数据过滤。")
    private String clusterIp;

    /**
     * 外部IP列表(JSON格式)。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "外部IP列表(JSON格式)。可支持等于操作符的列表数据过滤。")
    private String externalIps;

    /**
     * 端口配置(JSON格式)。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "端口配置(JSON格式)。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    private String ports;

    /**
     * 负载均衡器IP。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "负载均衡器IP。可支持等于操作符的列表数据过滤。")
    private String loadBalancerIp;

    /**
     * 负载均衡器源IP范围。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "负载均衡器源IP范围。可支持等于操作符的列表数据过滤。")
    private String loadBalancerSourceRanges;

    /**
     * 字符id。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "字符id。可支持等于操作符的列表数据过滤。")
    private String strId;

    /**
     * 数据所属人。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "数据所属人。可支持等于操作符的列表数据过滤。")
    private Long dataUserId;

    /**
     * 数据所属部门。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "数据所属部门。可支持等于操作符的列表数据过滤。")
    private Long dataDeptId;

    /**
     * updateTime 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "updateTime 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "updateTime 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String updateTimeEnd;

    /**
     * createTime 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "createTime 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "createTime 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String createTimeEnd;

    /**
     * service_name / service_type / selector_labels / cluster_ip / external_ips / ports / load_balancer_ip LIKE搜索字符串。
     * NOTE: 可支持LIKE操作符的列表数据过滤。
     */
    @Schema(description = "LIKE模糊搜索字符串")
    private String searchString;
}
