package supie.webadmin.app.dto;

import supie.common.core.validator.UpdateGroup;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;

/**
 * K8S容器组件监控表Dto对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Schema(description = "K8S容器组件监控表Dto对象")
@Data
public class SchPodMonitorDto {

    /**
     * 容器主键监控表主键id。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "容器主键监控表主键id。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数据验证失败，容器主键监控表主键id不能为空！", groups = {UpdateGroup.class})
    private Long id;

    /**
     * 集群节点id。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "集群节点id。可支持等于操作符的列表数据过滤。")
    private Long clusterNodeId;

    /**
     * 集群命名空间id。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "集群命名空间id。可支持等于操作符的列表数据过滤。")
    private Long clusterNamespaceId;

    /**
     * 集群标签id。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "集群标签id。可支持等于操作符的列表数据过滤。")
    private String clusterLabelId;

    /**
     * pod名称。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "pod名称。可支持等于操作符的列表数据过滤。")
    private String podName;

    /**
     * pod运行状态(running stopped)。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "pod运行状态(running stopped)。可支持等于操作符的列表数据过滤。")
    private String podRunStatus;

    /**
     * pod就绪状态(就绪数/总数)。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "pod就绪状态(就绪数/总数)。可支持等于操作符的列表数据过滤。")
    private String podReady;

    /**
     * pod运行时间。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "pod运行时间。可支持等于操作符的列表数据过滤。")
    private String podRunTime;

    /**
     * pod重启次数。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "pod重启次数。可支持等于操作符的列表数据过滤。")
    private String podRestartCount;

    /**
     * pod内部ip地址(集群通信使用)。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "pod内部ip地址(集群通信使用)。可支持等于操作符的列表数据过滤。")
    private String podInnerIp;

    /**
     * 配置文件。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "配置文件。可支持等于操作符的列表数据过滤。")
    private String podConfig;

    /**
     * 扩展字段。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "扩展字段。可支持等于操作符的列表数据过滤。")
    private String podExtends;

    /**
     * 字符编号。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "字符编号。可支持等于操作符的列表数据过滤。")
    private String strId;

    /**
     * 数据所属人。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "数据所属人。可支持等于操作符的列表数据过滤。")
    private Long dataUserId;

    /**
     * 数据所属部门。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "数据所属部门。可支持等于操作符的列表数据过滤。")
    private Long dataDeptId;

    /**
     * updateTime 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "updateTime 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "updateTime 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String updateTimeEnd;

    /**
     * createTime 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "createTime 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "createTime 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String createTimeEnd;

    /**
     * pod_name / pod_run_status / pod_ready / pod_run_time / pod_restart_count / pod_inner_ip LIKE搜索字符串。
     * NOTE: 可支持LIKE操作符的列表数据过滤。
     */
    @Schema(description = "LIKE模糊搜索字符串")
    private String searchString;
}
