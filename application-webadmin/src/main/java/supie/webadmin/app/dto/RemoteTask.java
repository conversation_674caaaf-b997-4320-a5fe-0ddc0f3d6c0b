package supie.webadmin.app.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 *<AUTHOR>
 *@Description 远程任务调度
 *@date 2025/6/11 20:27
 */
@Tag(name = "RemoteTask", description = "远程任务调度")
@Data
public class RemoteTask {

    /**
     *  是否打开url 1 ip:
     */
    @Schema(description = "是否打开url",example = "1表示需要")
    private String openUrl;

    /**
     * docker 容器启动的服务名选项
     */
    @Schema(description = "服务启动名称(不能使用中文)")
    private String serviceName;

    /**
     * 容器任务名称
     */
    @Schema(description = "容器任务名称",example = "推理训练任务")
    @NotNull(message = "容器任务名称不能为空！")
    private String taskName;
    /**
     * 显存大小
     */
    @Schema(description = "显存大小(默认单位MB)",example = "167899")
    @NotNull(message = "数据验证失败，显存大小不能为空！")
    private String graphicSize;

    /**
     * cpu核数
     */
    @Schema(description = "cpu核数",example = "4")
    private Integer  cpuCore;

    /**
     * 内存大小
     */
    @Schema(description = "内存大小(默认单位MB)",example = "167899")
    private String memorySize;

    /**
     * 需要的资源 NPU  物理卡
     */
    @Schema(description = "需要的资源(physical-物理卡|vnpu-显卡|non-不需要)",example = "physical")
    @NotNull(message = "数据验证失败，需要的资源 NPU  物理卡不能为空！")
   private  String needResource;


    /**
     * 暴露端口
     */
    @Schema(description = "容器内部端口",example = "8080")
    @NotNull(message = "数据验证失败，容器内部端口不能为空！")
    private String  containerPort;

    /**
     * 镜像名称
     */
    @Schema(description = "镜像名称",example = "oder:ubuntu-python3.10-910-cann8.0")
    @NotNull(message = "数据验证失败，镜像名称不能为空！")
    private  String imageName;

    /**
     * 镜像版本号
     */
    @Schema(description = "镜像版本号",example = "1.0.0")
    private String imageVersion;

    /**
     * 环境变量
     */
    @Schema(description = "环境变量", example = "{\"TZ\": \"Asia/Shanghai\", \"ASCEND_VERSION\": \"ascend910\"}")
    private Map<String,String> environment;

    /**
     * 命令
     */
    @Schema(description = "命令", example = "[\"bin/bash\", \"-c\", \"python train.py\"]")
    private String command;


    /**
     * 容器内部路径
     */
    @Schema(description = "容器内部挂载路径",example = "/containerPath/user")
    private String containerPath;

    /**
     * 容器内部路径
     */
    @Schema(description = "容器卷映射路径",example = "/serverPath/user")
    private String containerMapPath;


    /**
     * 是否需要多个端口
     */
    @Schema(description = "是否需要多个端口",example = "true")
    private Boolean multiplePort;

    /**
     * 需要端口数
     */
    @Schema(description = "需要端口数",example = "1")
    private  Integer needPortNum;

    /**
     * /data/models:/models
     */
    @Schema(description = "容器卷挂载列表",example = "[\"/data/models:/models\"]")
    private List<String> volumeMounts;

    /**
     * 重启策略
     */
    @Schema(description = "重启策略",example = "always")
    private String restart;

    /**
     * 容器名称
     */
    @JsonIgnore
    @Schema(description = "容器名称")
    private  String containerName;

    /**
     * 暴露端口列表
     */
    @JsonIgnore
    private List<String> export;

    /**
     * jupyter 访问
     */
    @JsonIgnore
    private String visitLink;


    /**
     * 代码服务表示 1 jupyter 2 codeServer
     */
    @Schema(description = "代码服务表示 1 jupyter 2 codeServer")
    private  Integer codeMark;

    /**
     * 传递token
     */
    @JsonIgnore
    private  String token;


}
