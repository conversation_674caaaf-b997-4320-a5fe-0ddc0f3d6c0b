package supie.webadmin.app.dto;

import supie.common.core.validator.UpdateGroup;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 资源切分管理表Dto对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Schema(description = "资源切分管理表Dto对象")
@Data
public class SchResourceSliceDto {

    /**
     * 编号。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "编号。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数据验证失败，编号不能为空！", groups = {UpdateGroup.class})
    private Long id;

    /**
     * 字符id。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "字符id。可支持等于操作符的列表数据过滤。")
    private String strId;

    /**
     * 更新时间。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "更新时间。可支持等于操作符的列表数据过滤。")
    private Date updateTime;

    /**
     * 创建时间。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "创建时间。可支持等于操作符的列表数据过滤。")
    private Date createTime;

    /**
     * 创建人。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "创建人。可支持等于操作符的列表数据过滤。")
    private Long createUserId;

    /**
     * 更新人。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "更新人。可支持等于操作符的列表数据过滤。")
    private Long updateUserId;

    /**
     * 数据所属人。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "数据所属人。可支持等于操作符的列表数据过滤。")
    private Long dataUserId;

    /**
     * 数据所属部门。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "数据所属部门。可支持等于操作符的列表数据过滤。")
    private Long dataDeptId;

    /**
     * 切分单元名称。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "切分单元名称。可支持等于操作符的列表数据过滤。")
    private String partitionName;

    /**
     * 所属资源ID(可选)。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "所属资源ID(可选)。可支持等于操作符的列表数据过滤。")
    private Long resourceId;

    /**
     * 所属计算卡ID（可选）。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "所属计算卡ID（可选）。可支持等于操作符的列表数据过滤。")
    private Long computeDeviceId;

    /**
     * 算力百分比（1%~100%）。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "算力百分比（1%~100%）。可支持等于操作符的列表数据过滤。")
    private String computePercent;

    /**
     * 计算卡的标签。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "计算卡的标签。可支持等于操作符的列表数据过滤。")
    private Integer computeDeviceIndex;

    /**
     * 显存（MB）。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "显存（MB）。可支持等于操作符的列表数据过滤。")
    private Integer graphicsMemorySize;

    /**
     * 显存（百分比）。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "显存（百分比）。可支持等于操作符的列表数据过滤。")
    private BigDecimal graphicsMemoryRate;

    /**
     * 内存（MB）。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "内存（MB）。可支持等于操作符的列表数据过滤。")
    private Integer memorySize;

    /**
     * 隔离方式（time/space）。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "隔离方式（time/space）。可支持等于操作符的列表数据过滤。")
    private String isolationMode;

    /**
     * 状态（异常、运行中）。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "状态（异常、运行中）。可支持等于操作符的列表数据过滤。")
    private String status;

    /**
     * 当前占用该分区的任务 ID（如果有）。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "当前占用该分区的任务 ID（如果有）。可支持等于操作符的列表数据过滤。")
    private Long taskId;

    /**
     * updateTime 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "updateTime 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "updateTime 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String updateTimeEnd;

    /**
     * createTime 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "createTime 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "createTime 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String createTimeEnd;

    /**
     * str_id / partition_name / compute_percent / isolation_mode / status LIKE搜索字符串。
     * NOTE: 可支持LIKE操作符的列表数据过滤。
     */
    @Schema(description = "LIKE模糊搜索字符串")
    private String searchString;
}
