package supie.webadmin.app.dto;

import supie.common.core.validator.UpdateGroup;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

/**
 * 节点基础监控表Dto对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Schema(description = "节点基础监控表Dto对象")
@Data
public class SchNodeBasicMetricsDto {

    /**
     * 编号。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "编号。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数据验证失败，编号不能为空！", groups = {UpdateGroup.class})
    private Long id;

    /**
     * 字符id。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "字符id。可支持等于操作符的列表数据过滤。")
    private String strId;

    /**
     * 更新时间。
     */
    @Schema(description = "更新时间。")
    private Date updateTime;

    /**
     * 更新人。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "更新人。可支持等于操作符的列表数据过滤。")
    private Long updateUserId;

    /**
     * 数据所属人。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "数据所属人。可支持等于操作符的列表数据过滤。")
    private Long dataUserId;

    /**
     * 数据所属部门。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "数据所属部门。可支持等于操作符的列表数据过滤。")
    private Long dataDeptId;

    /**
     * 资源ID。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "资源ID。可支持等于操作符的列表数据过滤。")
    private Long resourceId;

    /**
     * CPU利用率。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "CPU利用率。可支持等于操作符的列表数据过滤。")
    private BigDecimal cpuUtilization;

    /**
     * 整体CPU使用率。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "整体CPU使用率。可支持等于操作符的列表数据过滤。")
    private BigDecimal overallCpuUsage;

    /**
     * 内存利用率。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "内存利用率。可支持等于操作符的列表数据过滤。")
    private BigDecimal memoryUtilization;

    /**
     * 显存使用率。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "显存使用率。可支持等于操作符的列表数据过滤。")
    private BigDecimal videoUtilization;

    /**
     * ts 数据插入的时间
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "ts 数据插入的时间。可支持等于操作符的列表数据过滤。")
    private Timestamp ts;


    /**
     * createTime 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "createTime 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "createTime 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String createTimeEnd;

    /**
     * tsStart 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "tsStart 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String tsStartStart;

    /**
     * tsStart 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "tsStart 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String tsStartEnd;

    /**
     * tsEnd 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "tsEnd 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String tsEndStart;

    /**
     * tsEnd 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "tsEnd 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String tsEndEnd;


    /**
     * 网络速度总和
     */
    private double networkSpeedSum;


    /**
     * 可用内存量
     */
    private double availableMemory;


    /**
     * 磁盘IO读写总量（bytes）
     */
    private double diskIoWrittenAndReadSum;


    /**
     * 当前线程数
     */
    private double nowThread;

    /**
     * 进程内存使用率
     */
    private double processMemoryUsage;


    /**
     * 进程内存总量
     */
    private double processMemoryAmount;


    /**
     * 磁盘使用总量
     */
    private double diskUsedSum;


    /**
     * 磁盘利用率
     */
    private double diskUtilization;

    /**
     * 系统内存利用率
     */
    private double systemMemoryUtilization;

    /**
     * memory_utilization / video_utilization LIKE搜索字符串。
     * NOTE: 可支持LIKE操作符的列表数据过滤。
     */
    @Schema(description = "LIKE模糊搜索字符串")
    private String searchString;
}
