package supie.webadmin.app.dto;

import supie.common.core.validator.UpdateGroup;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

/**
 * 任务监控表（踩点）Dto对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Schema(description = "任务监控表（踩点）Dto对象")
@Data
public class SchTaskMonitoringDto {

    /**
     * 编号。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "编号。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数据验证失败，编号不能为空！", groups = {UpdateGroup.class})
    private Long id;

    /**
     * 字符id。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "字符id。可支持等于操作符的列表数据过滤。")
    private String strId;

    /**
     * 更新时间。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "更新时间。可支持等于操作符的列表数据过滤。")
    private Date updateTime;

    /**
     * 创建时间。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "创建时间。可支持等于操作符的列表数据过滤。")
    private Date createTime;

    /**
     * 创建人。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "创建人。可支持等于操作符的列表数据过滤。")
    private Long createUserId;

    /**
     * 更新人。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "更新人。可支持等于操作符的列表数据过滤。")
    private Long updateUserId;

    /**
     * 数据所属人。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "数据所属人。可支持等于操作符的列表数据过滤。")
    private Long dataUserId;

    /**
     * 数据所属部门。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "数据所属部门。可支持等于操作符的列表数据过滤。")
    private Long dataDeptId;

    /**
     * 任务id。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "任务id。可支持等于操作符的列表数据过滤。")
    private Long taskId;

    /**
     * 计算卡ID。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "计算卡ID。可支持等于操作符的列表数据过滤。")
    private Long computeDeviceId;

    /**
     * 资源ID。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "资源ID。可支持等于操作符的列表数据过滤。")
    private Long resourceId;

    /**
     * CPU使用率。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "CPU使用率。可支持等于操作符的列表数据过滤。")
    private BigDecimal cpuUsage;

    /**
     * 内存使用量。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "内存使用量。可支持等于操作符的列表数据过滤。")
    private BigDecimal memoryUsage;

    /**
     * 显存使用量。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "显存使用量。可支持等于操作符的列表数据过滤。")
    private String graphicsMemoryUsage;

    /**
     * ts 。
     */
    @Schema(description = "ts ")
    private Timestamp ts;

    /**
     * 容器id。
     */
    @Schema(description = "容器id ")
    private String containerId;


    /**
     * updateTime 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "updateTime 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "updateTime 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String updateTimeEnd;

    /**
     * createTime 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "createTime 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "createTime 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String createTimeEnd;

    /**
     * tsStart 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "tsStart 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String tsStart;

    /**
     * tsStart 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "tsStart 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String tsEnd;

    /**
     * str_id / memory_usage / graphics_memory_usage LIKE搜索字符串。
     * NOTE: 可支持LIKE操作符的列表数据过滤。
     */
    @Schema(description = "LIKE模糊搜索字符串")
    private String searchString;
}
