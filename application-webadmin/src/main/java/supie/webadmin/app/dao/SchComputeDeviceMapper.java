package supie.webadmin.app.dao;

import supie.common.core.annotation.EnableDataPerm;
import supie.common.core.base.dao.BaseDaoMapper;
import supie.webadmin.app.model.SchComputeDevice;
import org.apache.ibatis.annotations.Param;

import java.util.*;

/**
 * 计算卡表（GPU、NPU）数据操作访问接口。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@EnableDataPerm
public interface SchComputeDeviceMapper extends BaseDaoMapper<SchComputeDevice> {

    /**
     * 批量插入对象列表。
     *
     * @param schComputeDeviceList 新增对象列表。
     */
    void insertList(List<SchComputeDevice> schComputeDeviceList);

    /**
     * 获取分组计算后的数据对象列表。
     *
     * @param schComputeDeviceFilter 主表过滤对象。
     * @param groupSelect 分组显示字段列表字符串，SELECT从句的参数。
     * @param groupBy 分组字段列表字符串，GROUP BY从句的参数。
     * @param orderBy 排序字符串，ORDER BY从句的参数。
     * @return 对象列表。
     */
    List<SchComputeDevice> getGroupedSchComputeDeviceList(
            @Param("schComputeDeviceFilter") SchComputeDevice schComputeDeviceFilter,
            @Param("groupSelect") String groupSelect,
            @Param("groupBy") String groupBy,
            @Param("orderBy") String orderBy);

    /**
     * 获取过滤后的对象列表。
     *
     * @param schComputeDeviceFilter 主表过滤对象。
     * @param orderBy 排序字符串，order by从句的参数。
     * @return 对象列表。
     */
    List<SchComputeDevice> getSchComputeDeviceList(
            @Param("schComputeDeviceFilter") SchComputeDevice schComputeDeviceFilter, @Param("orderBy") String orderBy);
    /*
    * sql查询被切分的资源数
    * */
    Integer getVirtualResoureCount();

    /*
    * 查询被占用或切分的卡
    * */
    List<SchComputeDevice> getPartitionOrOccupyComputeDevice();

    /**
     *  通过vnpuId查 resourceId 查询出卡信息
     *
     * @param  vnpId 虚拟出来vNpu 信息
     * @param id 资源id resourceId
     * @return 结果
     */
    SchComputeDevice queryDevice(@Param("vnpuId") Integer vnpId,@Param("id") Long id);

    /**
     * 查询·
     * @param runPartition vnpuID
     * @return 可用Vnpu结果列表
     */
    List<Integer> queryAvailableVnpu(@Param("idList") List<Long> runPartition);
}
