package supie.webadmin.app.dao;

import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Select;
import supie.common.core.annotation.EnableDataPerm;
import supie.common.core.base.dao.BaseDaoMapper;
import supie.webadmin.app.model.ResourceTree;
import supie.webadmin.app.model.SchComputeDevice;
import supie.webadmin.app.model.SchResourceInfo;
import org.apache.ibatis.annotations.Param;
import supie.webadmin.app.model.SchTaskInfo;

import java.util.*;

/**
 * 服务资源信息表数据操作访问接口。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@EnableDataPerm
public interface SchResourceInfoMapper extends BaseDaoMapper<SchResourceInfo> {

    /**
     * 批量插入对象列表。
     *
     * @param schResourceInfoList 新增对象列表。
     */
    void insertList(List<SchResourceInfo> schResourceInfoList);

    /**
     * 获取分组计算后的数据对象列表。
     *
     * @param schResourceInfoFilter 主表过滤对象。
     * @param groupSelect 分组显示字段列表字符串，SELECT从句的参数。
     * @param groupBy 分组字段列表字符串，GROUP BY从句的参数。
     * @param orderBy 排序字符串，ORDER BY从句的参数。
     * @return 对象列表。
     */
    List<SchResourceInfo> getGroupedSchResourceInfoList(
            @Param("schResourceInfoFilter") SchResourceInfo schResourceInfoFilter,
            @Param("groupSelect") String groupSelect,
            @Param("groupBy") String groupBy,
            @Param("orderBy") String orderBy);

    /**
     * 获取过滤后的对象列表。
     *
     * @param schResourceInfoFilter 主表过滤对象。
     * @param orderBy 排序字符串，order by从句的参数。
     * @return 对象列表。
     */
    List<SchResourceInfo> getSchResourceInfoList(
            @Param("schResourceInfoFilter") SchResourceInfo schResourceInfoFilter, @Param("orderBy") String orderBy);

    /**
     * 统计资源信息
     * @return 具体统计结果列表
     */
    HashMap<String,Long> getResourceStatistics();

    /**
     *  查询卡信息，切分信息
     * @return 返回卡信息 切分信息
     */
    List<ResourceTree> queryDeviceSlice();

    /**
     * 查询资源池信息、资源信息
     * @return 返回资源信息，资源池信息
     */
    List<ResourceTree>  queryResourcePool();

    /**
     * 查询审批信息
     * @param taskId 任务id
     * @return 任务审批信息
     */
    @Select("SELECT *FROM sch_task_info ti WHERE ti.id=#{id}  AND ti.approve_state='approved'")
    SchTaskInfo approval(@Param("id") Long taskId);

    /**
     *  通过资源池id查询主机
     * @return 返回卡信息 切分信息
     */
    List<SchResourceInfo> queryResourceInfoByPoolId(Long poolId);

    /*
    * 获取资源状态数量
    * */
    @MapKey("status")
    List<Map<String, Object>> getResourceStatusGroup();

    @MapKey("id")
    List<Map<String, Object>>getResourceAggregationStats(@Param("resourceIds")List<Long> resourceIds);

    /**
     *  查询资源信息
     * @param id 资源id
     * @return 内存 显卡 cpu 核心数  系统版本 系统信息
     */
    @Select("SELECT memory_capacity,cpu_core_count,system_version,systemInfo FROM sch_resource_info WHERE  id=#{id}")
    SchResourceInfo queryMsg(@Param("id") Long id);

    @MapKey("computeDeviceId")
    List<Map<String,Object>> queryComputeDeviceTask();

    @Select("SELECT * FROM sch_compute_device WHERE resource_id IN (SELECT id FROM sch_resource_info WHERE is_delete = 1) AND is_delete = 1")
    List<SchComputeDevice> queryComputeDeviceAll();


    /**
     * 查询可用资源
     * @param id 资源vnpuid  存储表vccid
     */
    SchResourceInfo queryAvaliable(@Param("id") Integer id);

    /**
     * 查询poolId
     * @param id 资源id resourceID
     * @return poolId
     */
    List<Long> queryPoolId(@Param("id") Long id);

    /**
     * 获取资源信息
     * @param taskInfoId 任务id
     * @return 返沪资源信息Id
     */
    SchResourceInfo queryResource(@Param("id") Long taskInfoId);
}
