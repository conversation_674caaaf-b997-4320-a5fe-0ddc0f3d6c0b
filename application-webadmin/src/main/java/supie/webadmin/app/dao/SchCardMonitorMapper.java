package supie.webadmin.app.dao;

import supie.common.core.annotation.EnableDataPerm;
import supie.common.core.base.dao.BaseDaoMapper;
import supie.webadmin.app.model.SchCardMonitor;
import org.apache.ibatis.annotations.Param;

import java.util.*;

/**
 * 服务卡监控(NPU,GPU监控)数据操作访问接口。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@EnableDataPerm
public interface SchCardMonitorMapper extends BaseDaoMapper<SchCardMonitor> {

    /**
     * 批量插入对象列表。
     *
     * @param schCardMonitorList 新增对象列表。
     */
    void insertList(List<SchCardMonitor> schCardMonitorList);

    /**
     * 获取分组计算后的数据对象列表。
     *
     * @param schCardMonitorFilter 主表过滤对象。
     * @param groupSelect 分组显示字段列表字符串，SELECT从句的参数。
     * @param groupBy 分组字段列表字符串，GROUP BY从句的参数。
     * @param orderBy 排序字符串，ORDER BY从句的参数。
     * @return 对象列表。
     */
    List<SchCardMonitor> getGroupedSchCardMonitorList(
            @Param("schCardMonitorFilter") SchCardMonitor schCardMonitorFilter,
            @Param("groupSelect") String groupSelect,
            @Param("groupBy") String groupBy,
            @Param("orderBy") String orderBy);

    /**
     * 获取过滤后的对象列表。
     *
     * @param schCardMonitorFilter 主表过滤对象。
     * @param orderBy 排序字符串，order by从句的参数。
     * @return 对象列表。
     */
    List<SchCardMonitor> getSchCardMonitorList(
            @Param("schCardMonitorFilter") SchCardMonitor schCardMonitorFilter, @Param("orderBy") String orderBy);

    SchCardMonitor getNpuMonitoringByComputeDeviceId(Long computeDeviceId, Date ts);

    List<SchCardMonitor> statisticalIndicators(@Param("schCardMonitorFilter") SchCardMonitor schCardMonitorFilter,List<Long> resourceIdList);
    /**
     * 查询出每台主机时间最晚的一条数据
     * @param schResourceInfoIdList 服务器id列表
     * @return 返回对应的集合
     */
    List<SchCardMonitor> getSchCardMonitorListByIdList(@Param("schResourceInfoIdList") List<Long> schResourceInfoIdList);
    List<SchCardMonitor> getSchCardMonitorListByids(@Param("computeDeviceIds") List<Long> computeDeviceIds);

    List<SchCardMonitor> getSchCardMonitorListByResourceId(@Param("resourceId") Long resource);

    List<SchCardMonitor> getNpuMonitoringByResourceInfoId(Long resourceId, Date ts);

}
