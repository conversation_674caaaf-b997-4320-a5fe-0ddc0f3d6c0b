<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="supie.webadmin.app.dao.SchServicePortMapper">
    <resultMap id="BaseResultMap" type="supie.webadmin.app.model.SchServicePort">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="service_id" jdbcType="BIGINT" property="serviceId"/>
        <result column="port" jdbcType="INTEGER" property="port"/>
        <result column="target_port" jdbcType="VARCHAR" property="targetPort"/>
        <result column="node_port" jdbcType="INTEGER" property="nodePort"/>
        <result column="protocol" jdbcType="VARCHAR" property="protocol"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO sch_service_port
            (id,
            service_id,
            port,
            target_port,
            node_port,
            protocol,
            name,
            str_id,
            update_time,
            create_time,
            create_user_id,
            update_user_id,
            data_user_id,
            data_dept_id,
            is_delete)
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.id},
            #{item.serviceId},
            #{item.port},
            #{item.targetPort},
            #{item.nodePort},
            #{item.protocol},
            #{item.name},
            #{item.strId},
            #{item.updateTime},
            #{item.createTime},
            #{item.createUserId},
            #{item.updateUserId},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.isDelete})
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="supie.webadmin.app.dao.SchServicePortMapper.inputFilterRef"/>
        AND sch_service_port.is_delete = ${@supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="schServicePortFilter != null">
            <if test="schServicePortFilter.id != null">
                AND sch_service_port.id = #{schServicePortFilter.id}
            </if>
            <if test="schServicePortFilter.serviceId != null">
                AND sch_service_port.service_id = #{schServicePortFilter.serviceId}
            </if>
            <if test="schServicePortFilter.port != null">
                AND sch_service_port.port = #{schServicePortFilter.port}
            </if>
            <if test="schServicePortFilter.targetPort != null and schServicePortFilter.targetPort != ''">
                <bind name = "safeSchServicePortTargetPort" value = "'%' + schServicePortFilter.targetPort + '%'" />
                AND sch_service_port.target_port LIKE #{safeSchServicePortTargetPort}
            </if>
            <if test="schServicePortFilter.nodePort != null">
                AND sch_service_port.node_port = #{schServicePortFilter.nodePort}
            </if>
            <if test="schServicePortFilter.protocol != null and schServicePortFilter.protocol != ''">
                <bind name = "safeSchServicePortProtocol" value = "'%' + schServicePortFilter.protocol + '%'" />
                AND sch_service_port.protocol LIKE #{safeSchServicePortProtocol}
            </if>
            <if test="schServicePortFilter.name != null and schServicePortFilter.name != ''">
                <bind name = "safeSchServicePortName" value = "'%' + schServicePortFilter.name + '%'" />
                AND sch_service_port.name LIKE #{safeSchServicePortName}
            </if>
            <if test="schServicePortFilter.strId != null and schServicePortFilter.strId != ''">
                <bind name = "safeSchServicePortStrId" value = "'%' + schServicePortFilter.strId + '%'" />
                AND sch_service_port.str_id LIKE #{safeSchServicePortStrId}
            </if>
            <if test="schServicePortFilter.updateTimeStart != null and schServicePortFilter.updateTimeStart != ''">
                AND sch_service_port.update_time &gt;= #{schServicePortFilter.updateTimeStart}
            </if>
            <if test="schServicePortFilter.updateTimeEnd != null and schServicePortFilter.updateTimeEnd != ''">
                AND sch_service_port.update_time &lt;= #{schServicePortFilter.updateTimeEnd}
            </if>
            <if test="schServicePortFilter.createTimeStart != null and schServicePortFilter.createTimeStart != ''">
                AND sch_service_port.create_time &gt;= #{schServicePortFilter.createTimeStart}
            </if>
            <if test="schServicePortFilter.createTimeEnd != null and schServicePortFilter.createTimeEnd != ''">
                AND sch_service_port.create_time &lt;= #{schServicePortFilter.createTimeEnd}
            </if>
            <if test="schServicePortFilter.createUserId != null">
                AND sch_service_port.create_user_id = #{schServicePortFilter.createUserId}
            </if>
            <if test="schServicePortFilter.updateUserId != null">
                AND sch_service_port.update_user_id = #{schServicePortFilter.updateUserId}
            </if>
            <if test="schServicePortFilter.dataUserId != null">
                AND sch_service_port.data_user_id = #{schServicePortFilter.dataUserId}
            </if>
            <if test="schServicePortFilter.dataDeptId != null">
                AND sch_service_port.data_dept_id = #{schServicePortFilter.dataDeptId}
            </if>
            <if test="schServicePortFilter.isDelete != null">
                AND sch_service_port.is_delete = #{schServicePortFilter.isDelete}
            </if>
            <if test="schServicePortFilter.searchString != null and schServicePortFilter.searchString != ''">
                <bind name = "safeSchServicePortSearchString" value = "'%' + schServicePortFilter.searchString + '%'" />
                AND CONCAT(IFNULL(sch_service_port.target_port,''), IFNULL(sch_service_port.protocol,''), IFNULL(sch_service_port.name,'')) LIKE #{safeSchServicePortSearchString}
            </if>
        </if>
    </sql>

    <select id="getGroupedSchServicePortList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchServicePort">
        SELECT * FROM
            (SELECT
                COUNT(id) id,
                ${groupSelect}
            FROM sch_service_port
            <where>
                <include refid="filterRef"/>
            </where>
            GROUP BY ${groupBy}) sch_service_port
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSchServicePortList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchServicePort">
        SELECT * FROM sch_service_port
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
