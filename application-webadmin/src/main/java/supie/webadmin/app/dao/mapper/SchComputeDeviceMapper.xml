<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="supie.webadmin.app.dao.SchComputeDeviceMapper">
    <resultMap id="BaseResultMap" type="supie.webadmin.app.model.SchComputeDevice">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="resource_id" jdbcType="BIGINT" property="resourceId"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="total_compute" jdbcType="VARCHAR" property="totalCompute"/>
        <result column="device_type" jdbcType="VARCHAR" property="deviceType"/>
        <result column="model_number" jdbcType="VARCHAR" property="modelNumber"/>
        <result column="device_name" jdbcType="VARCHAR" property="deviceName"/>
        <result column="memory_size" jdbcType="INTEGER" property="memorySize"/>
        <result column="semi_precision" jdbcType="DECIMAL" property="semiPrecision"/>
        <result column="single_precision" jdbcType="DECIMAL" property="singlePrecision"/>
        <result column="double_precision" jdbcType="DECIMAL" property="doublePrecision"/>
        <result column="cuda_number" jdbcType="INTEGER" property="cudaNumber"/>
        <result column="tensor_number" jdbcType="INTEGER" property="tensorNumber"/>
        <result column="architecture" jdbcType="VARCHAR" property="architecture"/>
        <result column="memory_type" jdbcType="VARCHAR" property="memoryType"/>
        <result column="card_type" jdbcType="VARCHAR" property="cardType"/>
        <result column="product_type" jdbcType="VARCHAR" property="productType"/>
        <result column="device_number" jdbcType="INTEGER" property="deviceNumber"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO sch_compute_device
            (id,
            str_id,
            update_time,
            create_time,
            create_user_id,
            update_user_id,
            data_user_id,
            data_dept_id,
            is_delete,
            resource_id,
            status,
            total_compute,
            device_type,
            model_number,
            device_name,
            memory_size,
            semi_precision,
            single_precision,
            double_precision,
            cuda_number,
            tensor_number,
            architecture,
                card_type,
            device_number,
            product_type,
            memory_type)
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.id},
            #{item.strId},
            #{item.updateTime},
            #{item.createTime},
            #{item.createUserId},
            #{item.updateUserId},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.isDelete},
            #{item.resourceId},
            #{item.status},
            #{item.totalCompute},
            #{item.deviceType},
            #{item.modelNumber},
            #{item.deviceName},
            #{item.memorySize},
            #{item.semiPrecision},
            #{item.singlePrecision},
            #{item.doublePrecision},
            #{item.cudaNumber},
             #{item.cardType},
            #{item.tensorNumber},
            #{item.architecture},
            #{item.deviceNumber},
            #{item.productType},
            #{item.memoryType})
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="supie.webadmin.app.dao.SchComputeDeviceMapper.inputFilterRef"/>
        AND sch_compute_device.is_delete = ${@supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="schComputeDeviceFilter != null">
            <if test="schComputeDeviceFilter.id != null">
                AND sch_compute_device.id = #{schComputeDeviceFilter.id}
            </if>
            <if test="schComputeDeviceFilter.strId != null and schComputeDeviceFilter.strId != ''">
                <bind name = "safeSchComputeDeviceStrId" value = "'%' + schComputeDeviceFilter.strId + '%'" />
                AND sch_compute_device.str_id LIKE #{safeSchComputeDeviceStrId}
            </if>
            <if test="schComputeDeviceFilter.updateTimeStart != null and schComputeDeviceFilter.updateTimeStart != ''">
                AND sch_compute_device.update_time &gt;= #{schComputeDeviceFilter.updateTimeStart}
            </if>
            <if test="schComputeDeviceFilter.updateTimeEnd != null and schComputeDeviceFilter.updateTimeEnd != ''">
                AND sch_compute_device.update_time &lt;= #{schComputeDeviceFilter.updateTimeEnd}
            </if>
            <if test="schComputeDeviceFilter.createTimeStart != null and schComputeDeviceFilter.createTimeStart != ''">
                AND sch_compute_device.create_time &gt;= #{schComputeDeviceFilter.createTimeStart}
            </if>
            <if test="schComputeDeviceFilter.createTimeEnd != null and schComputeDeviceFilter.createTimeEnd != ''">
                AND sch_compute_device.create_time &lt;= #{schComputeDeviceFilter.createTimeEnd}
            </if>
            <if test="schComputeDeviceFilter.createUserId != null">
                AND sch_compute_device.create_user_id = #{schComputeDeviceFilter.createUserId}
            </if>
            <if test="schComputeDeviceFilter.updateUserId != null">
                AND sch_compute_device.update_user_id = #{schComputeDeviceFilter.updateUserId}
            </if>
            <if test="schComputeDeviceFilter.dataUserId != null">
                AND sch_compute_device.data_user_id = #{schComputeDeviceFilter.dataUserId}
            </if>
            <if test="schComputeDeviceFilter.dataDeptId != null">
                AND sch_compute_device.data_dept_id = #{schComputeDeviceFilter.dataDeptId}
            </if>
            <if test="schComputeDeviceFilter.isDelete != null">
                AND sch_compute_device.is_delete = #{schComputeDeviceFilter.isDelete}
            </if>
            <if test="schComputeDeviceFilter.resourceId != null">
                AND sch_compute_device.resource_id = #{schComputeDeviceFilter.resourceId}
            </if>
            <if test="schComputeDeviceFilter.status != null and schComputeDeviceFilter.status != ''">
                <bind name = "safeSchComputeDeviceStatus" value = "'%' + schComputeDeviceFilter.status + '%'" />
                AND sch_compute_device.status LIKE #{safeSchComputeDeviceStatus}
            </if>
            <if test="schComputeDeviceFilter.totalCompute != null and schComputeDeviceFilter.totalCompute != ''">
                <bind name = "safeSchComputeDeviceTotalCompute" value = "'%' + schComputeDeviceFilter.totalCompute + '%'" />
                AND sch_compute_device.total_compute LIKE #{safeSchComputeDeviceTotalCompute}
            </if>
            <if test="schComputeDeviceFilter.deviceType != null and schComputeDeviceFilter.deviceType != ''">
                <bind name = "safeSchComputeDeviceDeviceType" value = "'%' + schComputeDeviceFilter.deviceType + '%'" />
                AND sch_compute_device.device_type LIKE #{safeSchComputeDeviceDeviceType}
            </if>
            <if test="schComputeDeviceFilter.modelNumber != null and schComputeDeviceFilter.modelNumber != ''">
                <bind name = "safeSchComputeDeviceModelNumber" value = "'%' + schComputeDeviceFilter.modelNumber + '%'" />
                AND sch_compute_device.model_number LIKE #{safeSchComputeDeviceModelNumber}
            </if>
            <if test="schComputeDeviceFilter.deviceName != null and schComputeDeviceFilter.deviceName != ''">
                <bind name = "safeSchComputeDeviceDeviceName" value = "'%' + schComputeDeviceFilter.deviceName + '%'" />
                AND sch_compute_device.device_name LIKE #{safeSchComputeDeviceDeviceName}
            </if>
            <if test="schComputeDeviceFilter.memorySize != null">
                AND sch_compute_device.memory_size = #{schComputeDeviceFilter.memorySize}
            </if>
            <if test="schComputeDeviceFilter.semiPrecision != null">
                AND sch_compute_device.semi_precision = #{schComputeDeviceFilter.semiPrecision}
            </if>
            <if test="schComputeDeviceFilter.singlePrecision != null">
                AND sch_compute_device.single_precision = #{schComputeDeviceFilter.singlePrecision}
            </if>
            <if test="schComputeDeviceFilter.doublePrecision != null">
                AND sch_compute_device.double_precision = #{schComputeDeviceFilter.doublePrecision}
            </if>
            <if test="schComputeDeviceFilter.cudaNumber != null">
                AND sch_compute_device.cuda_number = #{schComputeDeviceFilter.cudaNumber}
            </if>
            <if test="schComputeDeviceFilter.tensorNumber != null">
                AND sch_compute_device.tensor_number = #{schComputeDeviceFilter.tensorNumber}
            </if>
            <if test="schComputeDeviceFilter.architecture != null and schComputeDeviceFilter.architecture != ''">
                <bind name = "safeSchComputeDeviceArchitecture" value = "'%' + schComputeDeviceFilter.architecture + '%'" />
                AND sch_compute_device.architecture LIKE #{safeSchComputeDeviceArchitecture}
            </if>
            <if test="schComputeDeviceFilter.memoryType != null and schComputeDeviceFilter.memoryType != ''">
                <bind name = "safeSchComputeDeviceMemoryType" value = "'%' + schComputeDeviceFilter.memoryType + '%'" />
                AND sch_compute_device.memory_type LIKE #{safeSchComputeDeviceMemoryType}
            </if>
            <if test="schComputeDeviceFilter.searchString != null and schComputeDeviceFilter.searchString != ''">
                <bind name = "safeSchComputeDeviceSearchString" value = "'%' + schComputeDeviceFilter.searchString + '%'" />
                AND CONCAT(IFNULL(sch_compute_device.status,''), IFNULL(sch_compute_device.device_type,''), IFNULL(sch_compute_device.model_number,''), IFNULL(sch_compute_device.device_name,'')) LIKE #{safeSchComputeDeviceSearchString}
            </if>
        </if>
    </sql>

    <select id="getGroupedSchComputeDeviceList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchComputeDevice">
        SELECT * FROM
            (SELECT
                COUNT(id) id,
                ${groupSelect}
            FROM sch_compute_device
            <where>
                <include refid="filterRef"/>
            </where>
            GROUP BY ${groupBy}) sch_compute_device
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSchComputeDeviceList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchComputeDevice">
        SELECT * FROM sch_compute_device
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
    <select id="getVirtualResoureCount" resultType="java.lang.Integer">
        SELECT COUNT(*) AS total_count
        FROM sch_compute_device d
        WHERE EXISTS (
                SELECT 1
                FROM sch_virtual_compute_card_situation s
                WHERE s.compute_device_id = d.id
                  AND s.is_delete = 1
            )
          AND d.is_delete = 1
    </select>
    <select id="queryDevice" resultType="supie.webadmin.app.model.SchComputeDevice">
        SELECT
            cd.id,
            cd.resource_id,
            cd.model_number,
            cd.device_name,
            cd.device_type
        FROM sch_compute_device cd
                 JOIN sch_virtual_compute_card_situation vcc ON cd.id = vcc.compute_device_id
                 JOIN sch_resource_info ri ON cd.resource_id = ri.id
        WHERE vcc.is_delete = 1
          AND cd.is_delete = 1
          AND ri.is_delete = 1
          AND vcc.vcc_id =#{vnpuId}  AND ri.id=#{id}
    </select>
    <select id="getPartitionOrOccupyComputeDevice" resultType="supie.webadmin.app.model.SchComputeDevice">
        SELECT *
        FROM sch_compute_device
        WHERE id IN (
            SELECT compute_device_id FROM sch_task_info
            WHERE is_delete = 1 AND compute_device_id IS NOT NULL AND status IN ('running','queued')
            UNION
            SELECT compute_device_id FROM sch_virtual_compute_card_situation
            WHERE is_delete = 1 AND compute_device_id IS NOT NULL
        );
    </select>
    <select id="queryAvailableVnpu" resultType="java.lang.Integer">
        SELECT vcc.vcc_id
        FROM sch_compute_device cd
        JOIN sch_virtual_compute_card_situation vcc ON vcc.compute_device_id = cd.id
        <where>
            vcc.is_delete = 1
            AND cd.is_delete = 1
            <if test="idList != null and idList.size() > 0 ">
                AND vcc.vcc_id NOT IN
                <foreach item="item" collection="idList" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
