<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="supie.webadmin.app.dao.SchResourceInfoMapper">
    <resultMap id="BaseResultMap" type="supie.webadmin.app.model.SchResourceInfo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="uuid" jdbcType="VARCHAR" property="uuid"/>
        <result column="resource_name" jdbcType="VARCHAR" property="resourceName"/>
        <result column="host_ip" jdbcType="VARCHAR" property="hostIp"/>
        <result column="resource_type" jdbcType="VARCHAR" property="resourceType"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="graphics_memory" jdbcType="VARCHAR" property="graphicsMemory"/>
        <result column="memory_capacity" jdbcType="VARCHAR" property="memoryCapacity"/>
        <result column="cpu_core_count" jdbcType="VARCHAR" property="cpuCoreCount"/>
        <result column="connect_config_json" jdbcType="VARCHAR" property="connectConfigJson"/>
        <result column="compute_device_id" jdbcType="BIGINT" property="computeDeviceId"/>
        <result column="systemInfo"  property="systemInfo"/>
        <result column="system_version"  property="systemVersion"/>
        <result column="gpu_count"  property="gpuCount"/>
        <result column="gpu_memory"  property="gpuMemory"/>
        <result column="used_memory"  property="usedMemory"/>
        <result column="available_memory"  property="availableMemory"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO sch_resource_info
        (id,
        str_id,
        update_time,
        create_time,
        create_user_id,
        update_user_id,
        data_user_id,
        data_dept_id,
        is_delete,
        uuid,
        resource_name,
        host_ip,
        resource_type,
        status,
        graphics_memory,
        memory_capacity,
        cpu_core_count,
        connect_config_json,
        compute_device_id,
        systemInfo,
        system_version,
        gpu_count,
        gpu_memory,
        used_memory,
        available_memory
        )
        VALUES
        <foreach collection="list" index="index" item="item" separator=",">
            (#{item.id},
            #{item.strId},
            #{item.updateTime},
            #{item.createTime},
            #{item.createUserId},
            #{item.updateUserId},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.isDelete},
            #{item.uuid},
            #{item.resourceName},
            #{item.hostIp},
            #{item.resourceType},
            #{item.status},
            #{item.graphicsMemory},
            #{item.memoryCapacity},
            #{item.cpuCoreCount},
            #{item.connectConfigJson},
            #{item.computeDeviceId},
            #{item.systemInfo},
            #{item.systemVersion},
            #{item.gpuCount},
            #{item.gpuMemory},
            #{item.usedMemory},
            #{item.availableMemory}
            )
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="supie.webadmin.app.dao.SchResourceInfoMapper.inputFilterRef"/>
        AND sch_resource_info.is_delete = ${@supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="schResourceInfoFilter != null">
            <if test="schResourceInfoFilter.id != null">
                AND sch_resource_info.id = #{schResourceInfoFilter.id}
            </if>
            <if test="schResourceInfoFilter.strId != null and schResourceInfoFilter.strId != ''">
                <bind name = "safeSchResourceInfoStrId" value = "'%' + schResourceInfoFilter.strId + '%'" />
                AND sch_resource_info.str_id LIKE #{safeSchResourceInfoStrId}
            </if>
            <if test="schResourceInfoFilter.updateTimeStart != null and schResourceInfoFilter.updateTimeStart != ''">
                AND sch_resource_info.update_time &gt;= #{schResourceInfoFilter.updateTimeStart}
            </if>
            <if test="schResourceInfoFilter.updateTimeEnd != null and schResourceInfoFilter.updateTimeEnd != ''">
                AND sch_resource_info.update_time &lt;= #{schResourceInfoFilter.updateTimeEnd}
            </if>
            <if test="schResourceInfoFilter.createTimeStart != null and schResourceInfoFilter.createTimeStart != ''">
                AND sch_resource_info.create_time &gt;= #{schResourceInfoFilter.createTimeStart}
            </if>
            <if test="schResourceInfoFilter.createTimeEnd != null and schResourceInfoFilter.createTimeEnd != ''">
                AND sch_resource_info.create_time &lt;= #{schResourceInfoFilter.createTimeEnd}
            </if>
            <if test="schResourceInfoFilter.createUserId != null">
                AND sch_resource_info.create_user_id = #{schResourceInfoFilter.createUserId}
            </if>
            <if test="schResourceInfoFilter.updateUserId != null">
                AND sch_resource_info.update_user_id = #{schResourceInfoFilter.updateUserId}
            </if>
            <if test="schResourceInfoFilter.dataUserId != null">
                AND sch_resource_info.data_user_id = #{schResourceInfoFilter.dataUserId}
            </if>
            <if test="schResourceInfoFilter.dataDeptId != null">
                AND sch_resource_info.data_dept_id = #{schResourceInfoFilter.dataDeptId}
            </if>
            <if test="schResourceInfoFilter.isDelete != null">
                AND sch_resource_info.is_delete = #{schResourceInfoFilter.isDelete}
            </if>
            <if test="schResourceInfoFilter.uuid != null and schResourceInfoFilter.uuid != ''">
                <bind name = "safeSchResourceInfoUuid" value = "'%' + schResourceInfoFilter.uuid + '%'" />
                AND sch_resource_info.uuid LIKE #{safeSchResourceInfoUuid}
            </if>
            <if test="schResourceInfoFilter.resourceName != null and schResourceInfoFilter.resourceName != ''">
                <bind name = "safeSchResourceInfoResourceName" value = "'%' + schResourceInfoFilter.resourceName + '%'" />
                AND sch_resource_info.resource_name LIKE #{safeSchResourceInfoResourceName}
            </if>
            <if test="schResourceInfoFilter.hostIp != null and schResourceInfoFilter.hostIp != ''">
                <bind name = "safeSchResourceInfoHostIp" value = "'%' + schResourceInfoFilter.hostIp + '%'" />
                AND sch_resource_info.host_ip LIKE #{safeSchResourceInfoHostIp}
            </if>
            <if test="schResourceInfoFilter.resourceType != null and schResourceInfoFilter.resourceType != ''">
                <bind name = "safeSchResourceInfoResourceType" value = "'%' + schResourceInfoFilter.resourceType + '%'" />
                AND sch_resource_info.resource_type LIKE #{safeSchResourceInfoResourceType}
            </if>
            <if test="schResourceInfoFilter.status != null and schResourceInfoFilter.status != ''">
                <bind name = "safeSchResourceInfoStatus" value = "'%' + schResourceInfoFilter.status + '%'" />
                AND sch_resource_info.status LIKE #{safeSchResourceInfoStatus}
            </if>
            <if test="schResourceInfoFilter.graphicsMemory != null and schResourceInfoFilter.graphicsMemory != ''">
                <bind name = "safeSchResourceInfoGraphicsMemory" value = "'%' + schResourceInfoFilter.graphicsMemory + '%'" />
                AND sch_resource_info.graphics_memory LIKE #{safeSchResourceInfoGraphicsMemory}
            </if>
            <if test="schResourceInfoFilter.connectConfigJson != null and schResourceInfoFilter.connectConfigJson != ''">
                <bind name = "safeSchResourceInfoConnectConfigJson" value = "'%' + schResourceInfoFilter.connectConfigJson + '%'" />
                AND sch_resource_info.connect_config_json LIKE #{safeSchResourceInfoConnectConfigJson}
            </if>
            <if test="schResourceInfoFilter.computeDeviceId != null">
                AND sch_resource_info.compute_device_id = #{schResourceInfoFilter.computeDeviceId}
            </if>
            <if test="schResourceInfoFilter.searchString != null and schResourceInfoFilter.searchString != ''">
                <bind name = "safeSchResourceInfoSearchString" value = "'%' + schResourceInfoFilter.searchString + '%'" />
                AND CONCAT(IFNULL(sch_resource_info.uuid,''), IFNULL(sch_resource_info.resource_name,''), IFNULL(sch_resource_info.graphics_memory,''), IFNULL(sch_resource_info.memory_capacity,'')) LIKE #{safeSchResourceInfoSearchString}
            </if>
        </if>
    </sql>

    <select id="getGroupedSchResourceInfoList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchResourceInfo">
        SELECT * FROM
            (SELECT
                COUNT(id) id,
                ${groupSelect}
            FROM sch_resource_info
            <where>
                <include refid="filterRef"/>
            </where>
            GROUP BY ${groupBy}) sch_resource_info
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSchResourceInfoList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchResourceInfo">
        SELECT * FROM sch_resource_info
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getResourceStatistics" resultType="hashmap">    <!--    已改, -->
        SELECT
        COALESCE(COUNT(*), 0) AS 'resourceTotal',

        COALESCE(SUM(
        CASE
        WHEN cpu_core_count != 'Unknown' AND cpu_core_count REGEXP '^[0-9]+(\\.[0-9]+)?$'
        THEN CAST(cpu_core_count AS DECIMAL(20,2))
        ELSE 0
        END
        ), 0) AS 'cpuCoreTotal',

        COALESCE(SUM(
        CASE
        WHEN memory_capacity != 'Unknown' AND memory_capacity REGEXP '^[0-9]+(\\.[0-9]+)?$'
        THEN CAST(memory_capacity AS DECIMAL(20,2))
        ELSE 0
        END
        ), 0) AS 'memoryTotal',

        COALESCE(SUM(
        CASE
        WHEN graphics_memory REGEXP '^[0-9]+(\\.[0-9]+)?$'
        THEN CAST(graphics_memory AS DECIMAL(20,2))
        ELSE 0
        END
        ), 0) AS 'gpuMemoryTotal',

        COALESCE(SUM(CASE WHEN status = 'offline' THEN 1 ELSE 0 END), 0) AS 'offlineCount',
        COALESCE(SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END), 0) AS 'activeCount',
        COALESCE(SUM(CASE WHEN status = 'maintenance' THEN 1 ELSE 0 END), 0) AS 'maintenanceCount',
        COALESCE(SUM(CASE WHEN status = 'error' THEN 1 ELSE 0 END), 0) AS 'errorCount'

        FROM sch_resource_info
        WHERE is_delete = 1;
    </select>

    <select id="queryResourcePool" resultType="supie.webadmin.app.model.ResourceTree">
        SELECT sri2.id            AS resourceId,
               sri2.resource_name AS resourceName,
               srp.id             AS poolId,
               srp.pool_name      AS poolName
        FROM sch_resource_info sri2
                 JOIN sch_resource_pool_member rmp
                      ON sri2.id = rmp.resource_id
                 JOIN sch_resource_pool srp ON rmp.pool_id = srp.id
        WHERE sri2.is_delete = 1
          AND srp.is_delete = 1
    </select>
    <select id="queryDeviceSlice" resultType="supie.webadmin.app.model.ResourceTree">
        SELECT sri.id            AS resourceId,
               sri.resource_name AS resourceName,
               scd.id            AS deviceId,
               scd.device_name   AS deviceName,
               scd.model_number  AS modelNumber,
               scc.id            AS sliceId,
               scc.vcc_id        AS vnpuId
        FROM sch_resource_info sri
                 JOIN sch_compute_device scd ON sri.id = scd.resource_id
                 JOIN sch_virtual_compute_card_situation scc ON scc.compute_device_id = scd.id
        WHERE sri.is_delete = 1
          AND scd.is_delete = 1
          AND scc.is_delete = 1
    </select>
    <select id="queryResourceInfoByPoolId" resultType="supie.webadmin.app.model.SchResourceInfo">
        SELECT *
        FROM sch_resource_info sri
        WHERE sri.is_delete = 1
          AND sri.id IN (SELECT resource_id from sch_resource_pool_member where pool_id = #{poolId})
    </select>
    <select id="getResourceStatusGroup" resultType="java.util.Map">
        SELECT STATUS,COUNT(*) AS count
        FROM sch_resource_info
        WHERE is_delete = 1
        GROUP BY `status`;
    </select>
    <select id="getResourceAggregationStats" resultType="map">
            SELECT
            SUM(CASE WHEN cpu_core_count REGEXP '^[0-9]+(\\.[0-9]+)?$' THEN CAST(cpu_core_count AS DECIMAL(20,2)) ELSE 0 END) AS cpuCores,
            SUM(CASE WHEN memory_capacity REGEXP '^[0-9]+(\\.[0-9]+)?$' THEN CAST(memory_capacity AS DECIMAL(20,2)) ELSE 0 END) AS memorySize,
            SUM(CASE WHEN graphics_memory REGEXP '^[0-9]+(\\.[0-9]+)?$' THEN CAST(graphics_memory AS DECIMAL(20,2)) ELSE 0 END) AS gpuMemoryTotal
            FROM sch_resource_info
        <where>
            is_delete = 1
            <if test="resourceIds != null and !resourceIds.isEmpty()">
                AND id IN
                <foreach collection="resourceIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
    <select id="queryComputeDeviceTask" resultType="map">
        SELECT
            t.id AS taskId,
            t.task_name AS taskName,
            t.status AS taskStatus,
            t.graphic_needed_mb AS graphicNeededMb,
            t.memory_needed_mb AS memoryNeededMb,
            t.cpu_need AS cpuNeed,
            c.id AS computeDeviceId,
            c.device_name AS deviceName,
            c.device_type AS deviceType,
            r.id AS resourceId,
            r.resource_name AS resourceName,
            r.host_ip AS hostIp
        FROM
            sch_resource_info r
                JOIN sch_compute_device c ON r.id = c.resource_id AND c.is_delete = 1
                LEFT JOIN sch_task_info t
                          ON c.id = t.compute_device_id
                              AND t.is_delete = 1
        WHERE
            r.is_delete = 1;
    </select>
    <select id="queryAvaliable" resultType="supie.webadmin.app.model.SchResourceInfo">
        SELECT
        ri.id,
        ri.login_name,
        ri.resource_name,
        ri.host_ip,
        ri.password,
        ri.status,
        ri.resource_type,
        ri.resource_name
        FROM sch_compute_device cd
                 JOIN sch_virtual_compute_card_situation vcc ON cd.id = vcc.compute_device_id
                 JOIN sch_resource_info ri ON cd.resource_id = ri.id
        WHERE vcc.is_delete = 1
          AND cd.is_delete = 1
          AND ri.is_delete = 1
          AND vcc.vcc_id = #{id}
    </select>
    <select id="queryPoolId" resultType="java.lang.Long">
        SELECT rp.id
        FROM sch_resource_info ri
                 JOIN sch_resource_pool_member rpm ON ri.id = rpm.resource_id
                 JOIN sch_resource_pool rp ON rp.id = rpm.pool_id
        WHERE rp.is_delete = 1
          AND ri.is_delete = 1
          AND ri.id =#{id}
    </select>
    <select id="queryResource" resultType="supie.webadmin.app.model.SchResourceInfo">
        SELECT ri.*
        FROM sch_task_info ti
                 JOIN sch_resource_info ri ON ti.resource_id = ri.id
        WHERE ti.is_delete = 1
          AND ri.is_delete = 1
          AND ti.id = #{id}
    </select>


</mapper>
