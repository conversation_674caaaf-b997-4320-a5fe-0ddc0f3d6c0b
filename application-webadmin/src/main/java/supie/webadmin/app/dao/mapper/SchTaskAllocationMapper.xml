<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="supie.webadmin.app.dao.SchTaskAllocationMapper">
    <resultMap id="BaseResultMap" type="supie.webadmin.app.model.SchTaskAllocation">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="task_id" jdbcType="BIGINT" property="taskId"/>
        <result column="resource_id" jdbcType="BIGINT" property="resourceId"/>
        <result column="compute_device_id" jdbcType="BIGINT" property="computeDeviceId"/>
        <result column="partition_id" jdbcType="BIGINT" property="partitionId"/>
        <result column="cpu_core_number" jdbcType="INTEGER" property="cpuCoreNumber"/>
        <result column="compute_percent" jdbcType="VARCHAR" property="computePercent"/>
        <result column="graphics_memory" jdbcType="VARCHAR" property="graphicsMemory"/>
        <result column="memory" jdbcType="VARCHAR" property="memory"/>
        <result column="start_time" jdbcType="VARCHAR" property="startTime"/>
        <result column="end_time" jdbcType="VARCHAR" property="endTime"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO sch_task_allocation
            (id,
            str_id,
            update_time,
            create_time,
            create_user_id,
            update_user_id,
            data_user_id,
            data_dept_id,
            is_delete,
            task_id,
            resource_id,
            compute_device_id,
            partition_id,
            cpu_core_number,
            compute_percent,
            graphics_memory,
            memory,
            start_time,
            end_time)
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.id},
            #{item.strId},
            #{item.updateTime},
            #{item.createTime},
            #{item.createUserId},
            #{item.updateUserId},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.isDelete},
            #{item.taskId},
            #{item.resourceId},
            #{item.computeDeviceId},
            #{item.partitionId},
            #{item.cpuCoreNumber},
            #{item.computePercent},
            #{item.graphicsMemory},
            #{item.memory},
            #{item.startTime},
            #{item.endTime})
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="supie.webadmin.app.dao.SchTaskAllocationMapper.inputFilterRef"/>
        AND sch_task_allocation.is_delete = ${@supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="schTaskAllocationFilter != null">
            <if test="schTaskAllocationFilter.id != null">
                AND sch_task_allocation.id = #{schTaskAllocationFilter.id}
            </if>
            <if test="schTaskAllocationFilter.strId != null and schTaskAllocationFilter.strId != ''">
                <bind name = "safeSchTaskAllocationStrId" value = "'%' + schTaskAllocationFilter.strId + '%'" />
                AND sch_task_allocation.str_id LIKE #{safeSchTaskAllocationStrId}
            </if>
            <if test="schTaskAllocationFilter.updateTimeStart != null and schTaskAllocationFilter.updateTimeStart != ''">
                AND sch_task_allocation.update_time &gt;= #{schTaskAllocationFilter.updateTimeStart}
            </if>
            <if test="schTaskAllocationFilter.updateTimeEnd != null and schTaskAllocationFilter.updateTimeEnd != ''">
                AND sch_task_allocation.update_time &lt;= #{schTaskAllocationFilter.updateTimeEnd}
            </if>
            <if test="schTaskAllocationFilter.createTimeStart != null and schTaskAllocationFilter.createTimeStart != ''">
                AND sch_task_allocation.create_time &gt;= #{schTaskAllocationFilter.createTimeStart}
            </if>
            <if test="schTaskAllocationFilter.createTimeEnd != null and schTaskAllocationFilter.createTimeEnd != ''">
                AND sch_task_allocation.create_time &lt;= #{schTaskAllocationFilter.createTimeEnd}
            </if>
            <if test="schTaskAllocationFilter.createUserId != null">
                AND sch_task_allocation.create_user_id = #{schTaskAllocationFilter.createUserId}
            </if>
            <if test="schTaskAllocationFilter.updateUserId != null">
                AND sch_task_allocation.update_user_id = #{schTaskAllocationFilter.updateUserId}
            </if>
            <if test="schTaskAllocationFilter.dataUserId != null">
                AND sch_task_allocation.data_user_id = #{schTaskAllocationFilter.dataUserId}
            </if>
            <if test="schTaskAllocationFilter.dataDeptId != null">
                AND sch_task_allocation.data_dept_id = #{schTaskAllocationFilter.dataDeptId}
            </if>
            <if test="schTaskAllocationFilter.isDelete != null">
                AND sch_task_allocation.is_delete = #{schTaskAllocationFilter.isDelete}
            </if>
            <if test="schTaskAllocationFilter.taskId != null">
                AND sch_task_allocation.task_id = #{schTaskAllocationFilter.taskId}
            </if>
            <if test="schTaskAllocationFilter.resourceId != null">
                AND sch_task_allocation.resource_id = #{schTaskAllocationFilter.resourceId}
            </if>
            <if test="schTaskAllocationFilter.computeDeviceId != null">
                AND sch_task_allocation.compute_device_id = #{schTaskAllocationFilter.computeDeviceId}
            </if>
            <if test="schTaskAllocationFilter.partitionId != null">
                AND sch_task_allocation.partition_id = #{schTaskAllocationFilter.partitionId}
            </if>
            <if test="schTaskAllocationFilter.cpuCoreNumber != null">
                AND sch_task_allocation.cpu_core_number = #{schTaskAllocationFilter.cpuCoreNumber}
            </if>
            <if test="schTaskAllocationFilter.computePercent != null and schTaskAllocationFilter.computePercent != ''">
                <bind name = "safeSchTaskAllocationComputePercent" value = "'%' + schTaskAllocationFilter.computePercent + '%'" />
                AND sch_task_allocation.compute_percent LIKE #{safeSchTaskAllocationComputePercent}
            </if>
            <if test="schTaskAllocationFilter.graphicsMemory != null and schTaskAllocationFilter.graphicsMemory != ''">
                <bind name = "safeSchTaskAllocationGraphicsMemory" value = "'%' + schTaskAllocationFilter.graphicsMemory + '%'" />
                AND sch_task_allocation.graphics_memory LIKE #{safeSchTaskAllocationGraphicsMemory}
            </if>
            <if test="schTaskAllocationFilter.memory != null and schTaskAllocationFilter.memory != ''">
                <bind name = "safeSchTaskAllocationMemory" value = "'%' + schTaskAllocationFilter.memory + '%'" />
                AND sch_task_allocation.memory LIKE #{safeSchTaskAllocationMemory}
            </if>
            <if test="schTaskAllocationFilter.startTime != null and schTaskAllocationFilter.startTime != ''">
                <bind name = "safeSchTaskAllocationStartTime" value = "'%' + schTaskAllocationFilter.startTime + '%'" />
                AND sch_task_allocation.start_time LIKE #{safeSchTaskAllocationStartTime}
            </if>
            <if test="schTaskAllocationFilter.endTime != null and schTaskAllocationFilter.endTime != ''">
                <bind name = "safeSchTaskAllocationEndTime" value = "'%' + schTaskAllocationFilter.endTime + '%'" />
                AND sch_task_allocation.end_time LIKE #{safeSchTaskAllocationEndTime}
            </if>
            <if test="schTaskAllocationFilter.searchString != null and schTaskAllocationFilter.searchString != ''">
                <bind name = "safeSchTaskAllocationSearchString" value = "'%' + schTaskAllocationFilter.searchString + '%'" />
                AND CONCAT(IFNULL(sch_task_allocation.str_id,''), IFNULL(sch_task_allocation.compute_percent,''), IFNULL(sch_task_allocation.graphics_memory,''), IFNULL(sch_task_allocation.memory,''), IFNULL(sch_task_allocation.start_time,''), IFNULL(sch_task_allocation.end_time,'')) LIKE #{safeSchTaskAllocationSearchString}
            </if>
        </if>
    </sql>

    <select id="getGroupedSchTaskAllocationList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchTaskAllocation">
        SELECT * FROM
            (SELECT
                COUNT(id) id,
                ${groupSelect}
            FROM sch_task_allocation
            <where>
                <include refid="filterRef"/>
            </where>
            GROUP BY ${groupBy}) sch_task_allocation
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSchTaskAllocationList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchTaskAllocation">
        SELECT * FROM sch_task_allocation
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
