package supie.webadmin.app.dao;

import supie.common.core.annotation.EnableDataPerm;
import supie.common.core.base.dao.BaseDaoMapper;
import supie.webadmin.app.model.SchTaskLogs;
import org.apache.ibatis.annotations.Param;

import java.util.*;

/**
 * 任务日志表数据操作访问接口。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@EnableDataPerm
public interface SchTaskLogsMapper extends BaseDaoMapper<SchTaskLogs> {

    /**
     * 批量插入对象列表。
     *
     * @param schTaskLogsList 新增对象列表。
     */
    void insertList(List<SchTaskLogs> schTaskLogsList);

    /**
     * 获取分组计算后的数据对象列表。
     *
     * @param schTaskLogsFilter 主表过滤对象。
     * @param groupSelect 分组显示字段列表字符串，SELECT从句的参数。
     * @param groupBy 分组字段列表字符串，GROUP BY从句的参数。
     * @param orderBy 排序字符串，ORDER BY从句的参数。
     * @return 对象列表。
     */
    List<SchTaskLogs> getGroupedSchTaskLogsList(
            @Param("schTaskLogsFilter") SchTaskLogs schTaskLogsFilter,
            @Param("groupSelect") String groupSelect,
            @Param("groupBy") String groupBy,
            @Param("orderBy") String orderBy);

    /**
     * 获取过滤后的对象列表。
     *
     * @param schTaskLogsFilter 主表过滤对象。
     * @param orderBy 排序字符串，order by从句的参数。
     * @return 对象列表。
     */
    List<SchTaskLogs> getSchTaskLogsList(
            @Param("schTaskLogsFilter") SchTaskLogs schTaskLogsFilter, @Param("orderBy") String orderBy);
}
