package supie.webadmin.app.dao;

import supie.common.core.annotation.EnableDataPerm;
import supie.common.core.base.dao.BaseDaoMapper;
import supie.webadmin.app.model.SchServicePort;
import org.apache.ibatis.annotations.Param;

import java.util.*;

/**
 * K8S Service端口配置表数据操作访问接口。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@EnableDataPerm
public interface SchServicePortMapper extends BaseDaoMapper<SchServicePort> {

    /**
     * 批量插入对象列表。
     *
     * @param schServicePortList 新增对象列表。
     */
    void insertList(List<SchServicePort> schServicePortList);

    /**
     * 获取分组计算后的数据对象列表。
     *
     * @param schServicePortFilter 主表过滤对象。
     * @param groupSelect 分组显示字段列表字符串，SELECT从句的参数。
     * @param groupBy 分组字段列表字符串，GROUP BY从句的参数。
     * @param orderBy 排序字符串，ORDER BY从句的参数。
     * @return 对象列表。
     */
    List<SchServicePort> getGroupedSchServicePortList(
            @Param("schServicePortFilter") SchServicePort schServicePortFilter,
            @Param("groupSelect") String groupSelect,
            @Param("groupBy") String groupBy,
            @Param("orderBy") String orderBy);

    /**
     * 获取过滤后的对象列表。
     *
     * @param schServicePortFilter 主表过滤对象。
     * @param orderBy 排序字符串，order by从句的参数。
     * @return 对象列表。
     */
    List<SchServicePort> getSchServicePortList(
            @Param("schServicePortFilter") SchServicePort schServicePortFilter, @Param("orderBy") String orderBy);
}
