<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="supie.webadmin.app.dao.SchTaskImageMapper">
    <resultMap id="BaseResultMap" type="supie.webadmin.app.model.SchTaskImage">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="image_name" jdbcType="VARCHAR" property="imageName"/>
        <result column="image_desc" jdbcType="VARCHAR" property="imageDesc"/>
        <result column="image_version" jdbcType="VARCHAR" property="imageVersion"/>
        <result column="image_url" jdbcType="VARCHAR" property="imageUrl"/>
    </resultMap>
    <insert id="insertList">
        INSERT INTO sch_task_image
            (id,
            str_id,
            update_time,
            create_time,
            create_user_id,
            update_user_id,
            data_user_id,
            data_dept_id,
            is_delete,
            image_name,
            image_desc,
            image_version,
            image_url)
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.id},
            #{item.strId},
            #{item.updateTime},
            #{item.createTime},
            #{item.createUserId},
            #{item.updateUserId},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.isDelete},
            #{item.imageName},
            #{item.imageDesc},
            #{item.imageVersion},
            #{item.imageUrl})
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="supie.webadmin.app.dao.SchTaskImageMapper.inputFilterRef"/>
        AND sch_task_image.is_delete = ${@supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="schTaskImageFilter != null">
            <if test="schTaskImageFilter.id != null">
                AND sch_task_image.id = #{schTaskImageFilter.id}
            </if>
            <if test="schTaskImageFilter.strId != null and schTaskImageFilter.strId != ''">
                <bind name = "safeSchTaskImageStrId" value = "'%' + schTaskImageFilter.strId + '%'" />
                AND sch_task_image.str_id LIKE #{safeSchTaskImageStrId}
            </if>
            <if test="schTaskImageFilter.updateTimeStart != null and schTaskImageFilter.updateTimeStart != ''">
                AND sch_task_image.update_time &gt;= #{schTaskImageFilter.updateTimeStart}
            </if>
            <if test="schTaskImageFilter.updateTimeEnd != null and schTaskImageFilter.updateTimeEnd != ''">
                AND sch_task_image.update_time &lt;= #{schTaskImageFilter.updateTimeEnd}
            </if>
            <if test="schTaskImageFilter.createTimeStart != null and schTaskImageFilter.createTimeStart != ''">
                AND sch_task_image.create_time &gt;= #{schTaskImageFilter.createTimeStart}
            </if>
            <if test="schTaskImageFilter.createTimeEnd != null and schTaskImageFilter.createTimeEnd != ''">
                AND sch_task_image.create_time &lt;= #{schTaskImageFilter.createTimeEnd}
            </if>
            <if test="schTaskImageFilter.createUserId != null">
                AND sch_task_image.create_user_id = #{schTaskImageFilter.createUserId}
            </if>
            <if test="schTaskImageFilter.dataDeptId != null">
                AND sch_task_image.data_dept_id = #{schTaskImageFilter.dataDeptId}
            </if>
            <if test="schTaskImageFilter.isDelete != null">
                AND sch_task_image.is_delete = #{schTaskImageFilter.isDelete}
            </if>
            <if test="schTaskImageFilter.imageName != null and schTaskImageFilter.imageName != ''">
                <bind name = "safeSchTaskImageImageName" value = "'%' + schTaskImageFilter.imageName + '%'" />
                AND sch_task_image.image_name LIKE #{safeSchTaskImageImageName}
            </if>
            <if test="schTaskImageFilter.imageDesc != null and schTaskImageFilter.imageDesc != ''">
                <bind name = "safeSchTaskImageImageDesc" value = "'%' + schTaskImageFilter.imageDesc + '%'" />
                AND sch_task_image.image_desc LIKE #{safeSchTaskImageImageDesc}
            </if>
            <if test="schTaskImageFilter.imageUrl != null and schTaskImageFilter.imageUrl != ''">
                <bind name = "safeSchTaskImageImageUrl" value = "'%' + schTaskImageFilter.imageUrl + '%'" />
                AND sch_task_image.image_url LIKE #{safeSchTaskImageImageUrl}
            </if>
            <if test="schTaskImageFilter.searchString != null and schTaskImageFilter.searchString != ''">
                <bind name = "safeSchTaskImageSearchString" value = "'%' + schTaskImageFilter.searchString + '%'" />
                AND CONCAT(IFNULL(sch_task_image.str_id,''), IFNULL(sch_task_image.image_name,''), IFNULL(sch_task_image.image_desc,''), IFNULL(sch_task_image.image_url,'')) LIKE #{safeSchTaskImageSearchString}
            </if>
        </if>
    </sql>

    <select id="getGroupedSchTaskImageList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchTaskImage">
        SELECT * FROM
            (SELECT
                COUNT(id) id,
                ${groupSelect}
            FROM sch_task_image
            <where>
                <include refid="filterRef"/>
            </where>
            GROUP BY ${groupBy}) sch_task_image
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSchTaskImageList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchTaskImage">
        SELECT * FROM sch_task_image
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
    <select id="imageCount" resultType="supie.webadmin.app.vo.ImageCountVo">
        SELECT (SELECT COUNT(id) FROM sch_task_image WHERE is_delete = 1)      AS total,
               (SELECT COUNT(*)
                FROM sch_task_template
                WHERE is_delete = 1
                  AND update_time >= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)) AS relateUpdate,
               (SELECT COUNT(*)
                FROM sch_task_info ti
                         JOIN sch_task_image tm ON ti.task_image_id = tm.id
                WHERE ti.container_status IN ('running', 'exited')
                  AND ti.is_delete = 1
                  AND tm.is_delete = 1)                                        AS runTotal
    </select>
</mapper>
