<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="supie.webadmin.app.dao.SchTaskTemplateMapper">
    <resultMap id="BaseResultMap" type="supie.webadmin.app.model.SchTaskTemplate">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="template_name" jdbcType="VARCHAR" property="templateName"/>
        <result column="template_desc" jdbcType="VARCHAR" property="templateDesc"/>
        <result column="run_command" jdbcType="VARCHAR" property="runCommand"/>
        <result column="env_config" jdbcType="VARCHAR" property="envConfig"/>
        <result column="image_id" jdbcType="BIGINT" property="imageId"/>
        <result column="template_config_json" jdbcType="VARCHAR" property="templateConfigJson"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO sch_task_template
            (id,
            str_id,
            update_time,
            create_time,
            create_user_id,
            update_user_id,
            data_user_id,
            data_dept_id,
            is_delete,
            template_name,
            template_desc,
            run_command,
            env_config,
            template_config_json,
            image_id)
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.id},
            #{item.strId},
            #{item.updateTime},
            #{item.createTime},
            #{item.createUserId},
            #{item.updateUserId},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.isDelete},
            #{item.templateName},
            #{item.templateDesc},
            #{item.runCommand},
            #{item.envConfig},
            #{item.templateConfigJson}
            #{item.imageId})
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="supie.webadmin.app.dao.SchTaskTemplateMapper.inputFilterRef"/>
        AND sch_task_template.is_delete = ${@supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="schTaskTemplateFilter != null">
            <if test="schTaskTemplateFilter.id != null">
                AND sch_task_template.id = #{schTaskTemplateFilter.id}
            </if>
            <if test="schTaskTemplateFilter.strId != null and schTaskTemplateFilter.strId != ''">
                <bind name = "safeSchTaskTemplateStrId" value = "'%' + schTaskTemplateFilter.strId + '%'" />
                AND sch_task_template.str_id LIKE #{safeSchTaskTemplateStrId}
            </if>
            <if test="schTaskTemplateFilter.updateTimeStart != null and schTaskTemplateFilter.updateTimeStart != ''">
                AND sch_task_template.update_time &gt;= #{schTaskTemplateFilter.updateTimeStart}
            </if>
            <if test="schTaskTemplateFilter.updateTimeEnd != null and schTaskTemplateFilter.updateTimeEnd != ''">
                AND sch_task_template.update_time &lt;= #{schTaskTemplateFilter.updateTimeEnd}
            </if>
            <if test="schTaskTemplateFilter.createTimeStart != null and schTaskTemplateFilter.createTimeStart != ''">
                AND sch_task_template.create_time &gt;= #{schTaskTemplateFilter.createTimeStart}
            </if>
            <if test="schTaskTemplateFilter.createTimeEnd != null and schTaskTemplateFilter.createTimeEnd != ''">
                AND sch_task_template.create_time &lt;= #{schTaskTemplateFilter.createTimeEnd}
            </if>
            <if test="schTaskTemplateFilter.createUserId != null">
                AND sch_task_template.create_user_id = #{schTaskTemplateFilter.createUserId}
            </if>
            <if test="schTaskTemplateFilter.updateUserId != null">
                AND sch_task_template.update_user_id = #{schTaskTemplateFilter.updateUserId}
            </if>
            <if test="schTaskTemplateFilter.dataUserId != null">
                AND sch_task_template.data_user_id = #{schTaskTemplateFilter.dataUserId}
            </if>
            <if test="schTaskTemplateFilter.dataDeptId != null">
                AND sch_task_template.data_dept_id = #{schTaskTemplateFilter.dataDeptId}
            </if>
            <if test="schTaskTemplateFilter.isDelete != null">
                AND sch_task_template.is_delete = #{schTaskTemplateFilter.isDelete}
            </if>
            <if test="schTaskTemplateFilter.templateName != null and schTaskTemplateFilter.templateName != ''">
                <bind name = "safeSchTaskTemplateTemplateName" value = "'%' + schTaskTemplateFilter.templateName + '%'" />
                AND sch_task_template.template_name LIKE #{safeSchTaskTemplateTemplateName}
            </if>
            <if test="schTaskTemplateFilter.templateDesc != null and schTaskTemplateFilter.templateDesc != ''">
                <bind name = "safeSchTaskTemplateTemplateDesc" value = "'%' + schTaskTemplateFilter.templateDesc + '%'" />
                AND sch_task_template.template_desc LIKE #{safeSchTaskTemplateTemplateDesc}
            </if>
            <if test="schTaskTemplateFilter.runCommand != null and schTaskTemplateFilter.runCommand != ''">
                <bind name = "safeSchTaskTemplateRunCommand" value = "'%' + schTaskTemplateFilter.runCommand + '%'" />
                AND sch_task_template.run_command LIKE #{safeSchTaskTemplateRunCommand}
            </if>
            <if test="schTaskTemplateFilter.envConfig != null and schTaskTemplateFilter.envConfig != ''">
                <bind name = "safeSchTaskTemplateEnvConfig" value = "'%' + schTaskTemplateFilter.envConfig + '%'" />
                AND sch_task_template.env_config LIKE #{safeSchTaskTemplateEnvConfig}
            </if>
            <if test="schTaskTemplateFilter.imageId != null">
                AND sch_task_template.image_id = #{schTaskTemplateFilter.imageId}
            </if>
            <if test="schTaskTemplateFilter.searchString != null and schTaskTemplateFilter.searchString != ''">
                <bind name = "safeSchTaskTemplateSearchString" value = "'%' + schTaskTemplateFilter.searchString + '%'" />
                AND CONCAT(IFNULL(sch_task_template.str_id,''), IFNULL(sch_task_template.template_name,''), IFNULL(sch_task_template.template_desc,''), IFNULL(sch_task_template.run_command,''), IFNULL(sch_task_template.env_config,'')) LIKE #{safeSchTaskTemplateSearchString}
            </if>
        </if>
    </sql>

    <select id="getGroupedSchTaskTemplateList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchTaskTemplate">
        SELECT * FROM
            (SELECT
                COUNT(id) id,
                ${groupSelect}
            FROM sch_task_template
            <where>
                <include refid="filterRef"/>
            </where>
            GROUP BY ${groupBy}) sch_task_template
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSchTaskTemplateList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchTaskTemplate">
        SELECT * FROM sch_task_template
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
    <select id="templateCount" resultType="supie.webadmin.app.vo.TemplateCount">
        SELECT (SELECT COUNT(id) FROM sch_task_template WHERE is_delete = 1)   AS total,
               (SELECT COUNT(tt.image_id)
                FROM sch_task_template tt
                         JOIN sch_task_image ti ON tt.image_id = ti.id
                WHERE tt.is_delete = 1
                  AND ti.is_delete = 1)                                        AS relationImage,
               (SELECT COUNT(*)
                FROM sch_task_template
                WHERE is_delete = 1
                  AND update_time >= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)) AS relateUpdate
    </select>

</mapper>
