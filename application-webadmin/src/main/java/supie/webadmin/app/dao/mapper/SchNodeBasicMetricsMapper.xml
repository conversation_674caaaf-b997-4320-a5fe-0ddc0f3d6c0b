<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="supie.webadmin.app.dao.SchNodeBasicMetricsMapper">
    <resultMap id="BaseResultMap" type="supie.webadmin.app.model.SchNodeBasicMetrics">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="resource_id" jdbcType="BIGINT" property="resourceId"/>
        <result column="cpu_utilization" jdbcType="DECIMAL" property="cpuUtilization"/>
        <result column="overall_cpu_usage" jdbcType="DECIMAL" property="overallCpuUsage"/>
        <result column="memory_utilization" jdbcType="DECIMAL" property="memoryUtilization"/>
        <result column="video_utilization" jdbcType="DECIMAL" property="videoUtilization"/>
        <result column="ts" jdbcType="TIMESTAMP" property="ts"/>
        <result column="network_speed_sum" jdbcType="DOUBLE" property="networkSpeedSum"/>
        <result column="available_memory" jdbcType="DOUBLE" property="availableMemory"/>
        <result column="disk_io_written_and_read_sum" jdbcType="DOUBLE" property="diskIoWrittenAndReadSum"/>
        <result column="now_thread" jdbcType="DOUBLE" property="nowThread"/>
        <result column="process_memory_usage" jdbcType="DOUBLE" property="processMemoryUsage"/>
        <result column="process_memory_amount" jdbcType="DOUBLE" property="processMemoryAmount"/>
        <result column="disk_used_sum" jdbcType="DOUBLE" property="diskUsedSum"/>
        <result column="disk_utilization" jdbcType="DOUBLE" property="diskUtilization"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO sch_node_basic_metrics
        (id,
        str_id,
        create_user_id,
        update_user_id,
        data_user_id,
        data_dept_id,
        is_delete,
        resource_id,
        cpu_utilization,
        overall_cpu_usage,
        memory_utilization,
        video_utilization,
        ts,
        network_speed_sum,
        available_memory,
        disk_io_written_and_read_sum,
        now_thread,
        process_memory_usage,
        process_memory_amount,
        disk_used_sum,
        disk_utilization)
        VALUES
        <foreach collection="list" index="index" item="item" separator=",">
            (#{item.id},
            #{item.strId},
            #{item.createUserId},
            #{item.updateUserId},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.isDelete},
            #{item.resourceId},
            #{item.cpuUtilization},
            #{item.overallCpuUsage},
            #{item.memoryUtilization},
            #{item.videoUtilization},
            #{item.ts},
            #{item.networkSpeedSum},
            #{item.availableMemory},
            #{item.diskIoWrittenAndReadSum},
            #{item.nowThread},
            #{item.processMemoryUsage},
            #{item.processMemoryAmount},
            #{item.diskUsedSum},
            #{item.diskUtilization},)
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="supie.webadmin.app.dao.SchNodeBasicMetricsMapper.inputFilterRef"/>
        AND sch_node_basic_metrics.is_delete = ${@supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="schNodeBasicMetricsFilter != null">
            <if test="schNodeBasicMetricsFilter.id != null">
                AND sch_node_basic_metrics.id = #{schNodeBasicMetricsFilter.id}
            </if>
            <if test="schNodeBasicMetricsFilter.strId != null and schNodeBasicMetricsFilter.strId != ''">
                <bind name = "safeSchNodeBasicMetricsStrId" value = "'%' + schNodeBasicMetricsFilter.strId + '%'" />
                AND sch_node_basic_metrics.str_id LIKE #{safeSchNodeBasicMetricsStrId}
            </if>
            <if test="schNodeBasicMetricsFilter.createUserId != null">
                AND sch_node_basic_metrics.create_user_id = #{schNodeBasicMetricsFilter.createUserId}
            </if>
            <if test="schNodeBasicMetricsFilter.updateUserId != null">
                AND sch_node_basic_metrics.update_user_id = #{schNodeBasicMetricsFilter.updateUserId}
            </if>
            <if test="schNodeBasicMetricsFilter.dataUserId != null">
                AND sch_node_basic_metrics.data_user_id = #{schNodeBasicMetricsFilter.dataUserId}
            </if>
            <if test="schNodeBasicMetricsFilter.dataDeptId != null">
                AND sch_node_basic_metrics.data_dept_id = #{schNodeBasicMetricsFilter.dataDeptId}
            </if>
            <if test="schNodeBasicMetricsFilter.resourceId != null">
                AND sch_node_basic_metrics.resource_id = #{schNodeBasicMetricsFilter.resourceId}
            </if>
            <if test="schNodeBasicMetricsFilter.cpuUtilization != null">
                AND sch_node_basic_metrics.cpu_utilization = #{schNodeBasicMetricsFilter.cpuUtilization}
            </if>
            <if test="schNodeBasicMetricsFilter.overallCpuUsage != null">
                AND sch_node_basic_metrics.overall_cpu_usage = #{schNodeBasicMetricsFilter.overallCpuUsage}
            </if>
            <if test="schNodeBasicMetricsFilter.memoryUtilization != null and schNodeBasicMetricsFilter.memoryUtilization != ''">
                <bind name = "safeSchNodeBasicMetricsMemoryUtilization" value = "'%' + schNodeBasicMetricsFilter.memoryUtilization + '%'" />
                AND sch_node_basic_metrics.memory_utilization LIKE #{safeSchNodeBasicMetricsMemoryUtilization}
            </if>
            <if test="schNodeBasicMetricsFilter.videoUtilization != null and schNodeBasicMetricsFilter.videoUtilization != ''">
                <bind name = "safeSchNodeBasicMetricsVideoUtilization" value = "'%' + schNodeBasicMetricsFilter.videoUtilization + '%'" />
                AND sch_node_basic_metrics.video_utilization LIKE #{safeSchNodeBasicMetricsVideoUtilization}
            </if>
            <if test="schNodeBasicMetricsFilter.tsStartStart != null and schNodeBasicMetricsFilter.tsStartStart != ''">
                AND sch_node_basic_metrics.ts_start &gt;= #{schNodeBasicMetricsFilter.tsStartStart}
            </if>
            <if test="schNodeBasicMetricsFilter.tsStartEnd != null and schNodeBasicMetricsFilter.tsStartEnd != ''">
                AND sch_node_basic_metrics.ts_start &lt;= #{schNodeBasicMetricsFilter.tsStartEnd}
            </if>
            <if test="schNodeBasicMetricsFilter.tsEndStart != null and schNodeBasicMetricsFilter.tsEndStart != ''">
                AND sch_node_basic_metrics.ts_end &gt;= #{schNodeBasicMetricsFilter.tsEndStart}
            </if>
            <if test="schNodeBasicMetricsFilter.tsEndEnd != null and schNodeBasicMetricsFilter.tsEndEnd != ''">
                AND sch_node_basic_metrics.ts_end &lt;= #{schNodeBasicMetricsFilter.tsEndEnd}
            </if>
            <if test="schNodeBasicMetricsFilter.searchString != null and schNodeBasicMetricsFilter.searchString != ''">
                <bind name = "safeSchNodeBasicMetricsSearchString" value = "'%' + schNodeBasicMetricsFilter.searchString + '%'" />
                AND CONCAT(IFNULL(sch_node_basic_metrics.memory_utilization,''), IFNULL(sch_node_basic_metrics.video_utilization,'')) LIKE #{safeSchNodeBasicMetricsSearchString}
            </if>
        </if>
    </sql>

    <select id="getGroupedSchNodeBasicMetricsList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchNodeBasicMetrics">
        SELECT * FROM
            (SELECT
                COUNT(id) id,
                ${groupSelect}
            FROM sch_node_basic_metrics
            <where>
                <include refid="filterRef"/>
            </where>
            GROUP BY ${groupBy}) sch_node_basic_metrics
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSchNodeBasicMetricsList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchNodeBasicMetrics">
        SELECT * FROM sch_node_basic_metrics
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getAvgCpuUsage" resultType="java.lang.Double">
        SELECT AVG(overall_cpu_usage)
        FROM sch_node_basic_metrics
        WHERE is_delete = 1;
    </select>

    <select id="getLatestData" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchNodeBasicMetrics">
        SELECT *
        FROM sch_node_basic_metrics
        <where>
            <if test="resourceId != null ">
                AND resource_id =  #{resourceId}
            </if>
        </where>
        ORDER BY ts DESC
        LiMIT 1;
    </select>

    <select id="getCpuMonitoringByResourceId" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchNodeBasicMetrics">
        SELECT *
        FROM sch_node_basic_metrics
        <where>
            <if test="resourceId != null ">
                AND resource_id = #{resourceId}
            </if>
            <if test="ts != null ">
                AND ts = #{ts}
            </if>
        </where>
        ORDER BY ts DESC
    </select>
    <select id="getSchNodeBasicMetrics" resultType="map">
        SELECT t1.*
        FROM sch_node_basic_metrics t1
                 JOIN (
            SELECT resource_id, MAX(id) AS max_id
            FROM sch_node_basic_metrics
            WHERE is_delete = 1
            GROUP BY resource_id
        ) t2 ON t1.resource_id = t2.resource_id AND t1.id = t2.max_id
        WHERE t1.is_delete = 1
        ORDER BY t1.overall_cpu_usage DESC, t1.memory_utilization DESC;
    </select>

    <select id="statisticalIndicators" resultType="supie.webadmin.app.model.SchNodeBasicMetrics">
        SELECT ts AS tsLocal,*
        FROM
        sch_node_basic_metrics
        <where>
            AND resource_id IN #{resourceIdList}
            <if test="schNodeBasicMetricsFilter.tsStartStart != null and schNodeBasicMetricsFilter.tsStartStart != ''">
                AND ts &gt;= #{schNodeBasicMetricsFilter.tsStartStart}
            </if>
            <if test="schNodeBasicMetricsFilter.tsEndEnd != null and schNodeBasicMetricsFilter.tsEndEnd != ''">
                AND ts &lt;= #{schNodeBasicMetricsFilter.tsEndEnd}
            </if>
        </where>
        ORDER BY
        ts Asc
    </select>
    <select id="getSchNodeBasicMetricsListByTs" resultType="supie.webadmin.app.model.SchNodeBasicMetrics">
        SELECT *
        FROM sch_node_basic_metrics
        WHERE resource_id IN
        <foreach collection="resourceIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND ts >= now() - INTERVAL 60 MINUTE;
        ORDER BY ts DESC, resource_id DESC
    </select>
</mapper>
