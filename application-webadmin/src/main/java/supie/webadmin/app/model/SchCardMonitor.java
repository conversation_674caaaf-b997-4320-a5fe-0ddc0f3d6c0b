package supie.webadmin.app.model;

import com.baomidou.mybatisplus.annotation.*;
import supie.common.core.util.MyCommonUtil;
import supie.common.core.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 服务卡监控(NPU,GPU监控)实体对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Data
@TableName(value = "sch_card_monitor")
public class SchCardMonitor {

    /**
     * 主键id。
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 资源表主键id。
     */
    @TableField(value = "resource_id")
    private Long resourceId;

    /**
     * 计算卡表id。
     */
    @TableField(value = "compute_device_id")
    private Long computeDeviceId;

    /**
     * 卡利用率。
     */
    @TableField(value = "card_utilization")
    private BigDecimal cardUtilization;

    /**
     * 监控类型(1NPU,2GPU)。
     */
    @TableField(value = "monitor_type")
    private Integer monitorType;

    /**
     * 更新时间。
     */
    @TableField(exist = false)
    private Date updateTime;

    /**
     * 创建时间。
     */
    @TableField(exist = false)
    private Date createTime;

    /**
     * createTime 范围过滤起始值(>=)。
     */
    private String tsStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    private String tsEnd;

    /**
     * 创建人。
     */
    @TableField(value = "create_user_id")
    private Long createUserId;

    /**
     * 更新人。
     */
    @TableField(value = "update_user_id")
    private Long updateUserId;

    /**
     * 数据所属人。
     */
    @UserFilterColumn
    @TableField(value = "data_user_id")
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @DeptFilterColumn
    @TableField(value = "data_dept_id")
    private Long dataDeptId;

    /**
     * 逻辑删除标记字段(1: 正常 -1: 已删除)。
     */
    @TableLogic
    @TableField(value = "is_delete")
    private Integer isDelete;

    /**
     * 字符串ID。
     */
    @TableField(value = "str_id")
    private String strId;
    /*
    * 卡高带宽内存总容量（HBM）
    * */
    @TableField(value = "hb_total")
    private Long hbTotal;

    /*
     * 卡高带宽内存（HBM）利用率
     * */
    @TableField(value = "hb_util")
    private String hbUtil;

    /*
     * 卡高带宽内存已使用总容量（HBM）
     * */
    @TableField(value = "hb_used")
    private Long hbUsed;


    /*
     * 通用内存总容量
     * */
    @TableField(value = "memory_total")
    private Double memoryTotal;

    /*
     * 已使用通用内存总容量
     * */
    @TableField(value = "memory_used")
    private Double memoryUsed;

    /*
     * 通用内存利用率
     * */
    @TableField(value = "memory_util")
    private BigDecimal memoryUtil;

    /**
     * 序号
     */
    @TableField(value = "serial_number")
    private Integer serialNumber;

    /**
     * ts 范围过滤起始值(>=)。
     */
    @TableField(value = "ts")
    private Timestamp ts;

    /**
     * 温度（以摄氏度为单位）
     */
    private Integer temp;

    /**
     * 总内存信息（单位：MB）
     */
    private Long totalMemMemory;

    /**
     * 已使用内存（单位：MB）
     */
    private Long usedMemMemory;

    /**
     * 空闲显存
     */
    private Long fbFree;

    /**
     * 总显存
     */
    private Long fbTotal;

    /**
     * 已使用显存
     */
    private Long fbUsed;

    @TableField(exist = false)
    private LocalDateTime tsLocal;

    /**
     * updateTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String updateTimeEnd;

    /**
     * createTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String createTimeEnd;

    /**
     * str_id LIKE搜索字符串。
     */
    @TableField(exist = false)
    private String searchString;

    public void setSearchString(String searchString) {
        this.searchString = MyCommonUtil.replaceSqlWildcard(searchString);
    }
}
