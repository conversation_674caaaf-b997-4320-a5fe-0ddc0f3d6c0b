package supie.webadmin.app.model;

import com.baomidou.mybatisplus.annotation.*;
import supie.common.core.util.MyCommonUtil;
import supie.common.core.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 集群节点表实体对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Data
@TableName(value = "sch_cluster_node")
public class SchClusterNode {

    /**
     * 集群节点表主键id。
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 资源信息表主键id。
     */
    @TableField(value = "resource_info_id")
    private Long resourceInfoId;

    /**
     * 节点ip。
     */
    @TableField(value = "node_ip")
    private String nodeIp;

    /**
     * 节点主机名。
     */
    @TableField(value = "node_hostname")
    private String nodeHostname;

    /**
     * 节点端口号。
     */
    @TableField(value = "node_port")
    private String nodePort;

    /**
     * 节点名称。
     */
    @TableField(value = "node_name")
    private String nodeName;

    /**
     * 节点标签。
     */
    @TableField(value = "node_labels")
    private String nodeLabels;

    /**
     * 节点状态(ready notReady)。
     */
    @TableField(value = "node_status")
    private String nodeStatus;

    /**
     * 节点描述。
     */
    @TableField(value = "node_description")
    private String nodeDescription;

    /**
     * 节点角色(master slave)。
     */
    @TableField(value = "node_roles")
    private String nodeRoles;

    /**
     * 节点运行时间。
     */
    @TableField(value = "node_run_time")
    private String nodeRunTime;

    /**
     * 节点版本号。
     */
    @TableField(value = "node_version")
    private String nodeVersion;

    /**
     * 集群id。
     */
    @TableField(value = "cluster_manager_id")
    private Long clusterManagerId;

    /**
     * 字符编号。
     */
    @TableField(value = "str_id")
    private String strId;

    /**
     * 更新时间。
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 创建时间。
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 创建人。
     */
    @TableField(value = "create_user_id")
    private Long createUserId;

    /**
     * 更新人。
     */
    @TableField(value = "update_user_id")
    private Long updateUserId;

    /**
     * 数据所属人。
     */
    @UserFilterColumn
    @TableField(value = "data_user_id")
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @DeptFilterColumn
    @TableField(value = "data_dept_id")
    private Long dataDeptId;

    /**
     * 逻辑删除标记字段(1: 正常 -1: 已删除)。
     */
    @TableLogic
    @TableField(value = "is_delete")
    private Integer isDelete;

    /**
     * updateTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String updateTimeEnd;

    /**
     * createTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String createTimeEnd;

    /**
     * node_ip / node_hostname / node_port / node_name / node_labels / node_status / node_description LIKE搜索字符串。
     */
    @TableField(exist = false)
    private String searchString;

    public void setSearchString(String searchString) {
        this.searchString = MyCommonUtil.replaceSqlWildcard(searchString);
    }
}
