package supie.webadmin.app.model;

import com.baomidou.mybatisplus.annotation.*;
import supie.common.core.util.MyCommonUtil;
import supie.common.core.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * K8SIngress路由管理表实体对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Data
@TableName(value = "sch_ingress")
public class SchIngress {

    /**
     * Ingress主键id。
     */
    @TableId(value = "id")
    private Long id;

    /**
     * Ingress名称。
     */
    @TableField(value = "ingress_name")
    private String ingressName;


    /**
     * 命名空间对象。
     */

    @RelationOneToOne(
            masterIdField = "namespaceId",
            slaveIdField = "id",
            slaveModelClass = SchClusterNamespace.class
    )
    @TableField(exist = false)
    private  SchClusterNamespace namespace;
    /**
     * 命名空间ID。
     */
    @TableField(value = "namespace_id")
    private Long namespaceId;

    /**
     * 集群ID。
     */
    @TableField(value = "cluster_id")
    private Long clusterId;

    /**
     * Ingress类名称。
     */
    @TableField(value = "ingress_class_name")
    private String ingressClassName;


    /**
     * 类型描述
     */
    @TableField(exist = false)
    @RelationOneToOne(
            masterIdField = "ingressClassName",
            slaveIdField = "id",
            slaveModelClass = SchIngressClasses.class
    )
    private SchIngressClasses ingressClasses;

    /**
     * TLS配置(JSON格式)。
     */
    @TableField(value = "tls_config")
    private String tlsConfig;

    /**
     * 注解(JSON格式)。
     */
    @TableField(value = "annotations")
    private String annotations;

    /**
     * 路由规则(JSON格式)。
     */
    @TableField(value = "rules")
    private String rules;

    /**
     * Ingress状态。 Ingress状态(Creating 正在被创建和配置) Running(已成功创建并正常运行) Updating(配置正在更新) Error(创建或配置过程中出现错误) Deleted(已从集群中删除)  NotReady(已创建并就绪，但可能尚未接收流量)
     */
    @TableField(value = "status")
    private String status;

    /**
     * 字符id。
     */
    @TableField(value = "str_id")
    private String strId;

    /**
     * 更新时间。
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 创建时间。
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 创建人。
     */
    @TableField(value = "create_user_id")
    private Long createUserId;

    /**
     * 更新人。
     */
    @TableField(value = "update_user_id")
    private Long updateUserId;

    /**
     * 数据所属人。
     */
    @UserFilterColumn
    @TableField(value = "data_user_id")
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @DeptFilterColumn
    @TableField(value = "data_dept_id")
    private Long dataDeptId;

    /**
     * 逻辑删除标记字段(1: 正常 -1: 已删除)。
     */
    @TableLogic
    @TableField(value = "is_delete")
    private Integer isDelete;

    /**
     * updateTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String updateTimeEnd;

    /**
     * createTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String createTimeEnd;

    /**
     * ingress_name / ingress_class_name / tls_config / annotations / rules / status LIKE搜索字符串。
     */
    @TableField(exist = false)
    private String searchString;

    public void setSearchString(String searchString) {
        this.searchString = MyCommonUtil.replaceSqlWildcard(searchString);
    }
}
