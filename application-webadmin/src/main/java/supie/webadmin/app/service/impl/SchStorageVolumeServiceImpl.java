package supie.webadmin.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import supie.webadmin.app.service.*;
import supie.webadmin.app.dao.*;
import supie.webadmin.app.model.*;
import supie.common.core.base.dao.BaseDaoMapper;
import supie.common.core.constant.GlobalDeletedFlag;
import supie.common.core.object.TokenData;
import supie.common.core.object.MyRelationParam;
import supie.common.core.base.service.BaseService;
import supie.common.sequence.wrapper.IdGeneratorWrapper;
import com.github.pagehelper.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 存储卷管理表数据操作服务类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Slf4j
@Service("schStorageVolumeService")
public class SchStorageVolumeServiceImpl extends BaseService<SchStorageVolume, Long> implements SchStorageVolumeService {

    @Autowired
    private IdGeneratorWrapper idGenerator;
    @Autowired
    private SchStorageVolumeMapper schStorageVolumeMapper;

    /**
     * 返回当前Service的主表Mapper对象。
     *
     * @return 主表Mapper对象。
     */
    @Override
    protected BaseDaoMapper<SchStorageVolume> mapper() {
        return schStorageVolumeMapper;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SchStorageVolume saveNew(SchStorageVolume schStorageVolume) {
        schStorageVolumeMapper.insert(this.buildDefaultValue(schStorageVolume));
        return schStorageVolume;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNewBatch(List<SchStorageVolume> schStorageVolumeList) {
        if (CollUtil.isNotEmpty(schStorageVolumeList)) {
            schStorageVolumeList.forEach(this::buildDefaultValue);
            schStorageVolumeMapper.insertList(schStorageVolumeList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean update(SchStorageVolume schStorageVolume, SchStorageVolume originalSchStorageVolume) {
        schStorageVolume.setCreateUserId(originalSchStorageVolume.getCreateUserId());
        schStorageVolume.setUpdateUserId(TokenData.takeFromRequest().getUserId());
        schStorageVolume.setUpdateTime(new Date());
        schStorageVolume.setCreateTime(originalSchStorageVolume.getCreateTime());
        // 这里重点提示，在执行主表数据更新之前，如果有哪些字段不支持修改操作，请用原有数据对象字段替换当前数据字段。
        UpdateWrapper<SchStorageVolume> uw = this.createUpdateQueryForNullValue(schStorageVolume, schStorageVolume.getId());
        return schStorageVolumeMapper.update(schStorageVolume, uw) == 1;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean remove(Long id) {
        return schStorageVolumeMapper.deleteById(id) == 1;
    }

    @Override
    public List<SchStorageVolume> getSchStorageVolumeList(SchStorageVolume filter, String orderBy) {
        return schStorageVolumeMapper.getSchStorageVolumeList(filter, orderBy);
    }

    @Override
    public List<SchStorageVolume> getSchStorageVolumeListWithRelation(SchStorageVolume filter, String orderBy) {
        List<SchStorageVolume> resultList = schStorageVolumeMapper.getSchStorageVolumeList(filter, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    @Override
    public List<SchStorageVolume> getGroupedSchStorageVolumeListWithRelation(
            SchStorageVolume filter, String groupSelect, String groupBy, String orderBy) {
        List<SchStorageVolume> resultList =
                schStorageVolumeMapper.getGroupedSchStorageVolumeList(filter, groupSelect, groupBy, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        // NOTE: 这里只是包含了关联数据，聚合计算数据没有包含。
        // 主要原因是，由于聚合字段通常被视为普通字段使用，不会在group by的从句中出现，语义上也不会在此关联。
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    private SchStorageVolume buildDefaultValue(SchStorageVolume schStorageVolume) {
        if (schStorageVolume.getId() == null) {
            schStorageVolume.setId(idGenerator.nextLongId());
        }
        TokenData tokenData = TokenData.takeFromRequest();
        schStorageVolume.setCreateUserId(tokenData.getUserId());
        schStorageVolume.setUpdateUserId(tokenData.getUserId());
        Date now = new Date();
        schStorageVolume.setUpdateTime(now);
        schStorageVolume.setCreateTime(now);
        schStorageVolume.setIsDelete(GlobalDeletedFlag.NORMAL);
        return schStorageVolume;
    }
}
