package supie.webadmin.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import jakarta.annotation.Resource;
import supie.webadmin.app.service.*;
import supie.webadmin.app.dao.*;
import supie.webadmin.app.model.*;
import supie.common.core.base.dao.BaseDaoMapper;
import supie.common.core.constant.GlobalDeletedFlag;
import supie.common.core.object.TokenData;
import supie.common.core.object.MyRelationParam;
import supie.common.core.base.service.BaseService;
import supie.common.sequence.wrapper.IdGeneratorWrapper;
import com.github.pagehelper.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 资源池信息表数据操作服务类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Slf4j
@Service("schResourcePoolService")
public class SchResourcePoolServiceImpl extends BaseService<SchResourcePool, Long> implements SchResourcePoolService {

    @Autowired
    private IdGeneratorWrapper idGenerator;
    @Autowired
    private SchResourcePoolMapper schResourcePoolMapper;
    @Autowired
    private SchResourcePoolMemberService schResourcePoolMemberService;
    @Autowired
    private SchResourceInfoService schResourceInfoService;
    @Autowired
    private SchResourcePoolService schResourcePoolService;

    @Resource
    private  SchResourcePoolMemberMapper resourcePoolMemberMapper;
    @Autowired
    private SchResourceInfoMapper schResourceInfoMapper;
    @Autowired
    private SchComputeDeviceMapper schComputeDeviceMapper;
    @Autowired
    private SchNodeBasicMetricsService schNodeBasicMetricsService;
    @Autowired
    private SchCardMonitorService schCardMonitorService;
    @Autowired
    private SchTaskInfoServiceImpl schTaskInfoService;
    @Autowired
    private SchTaskInfoMapper schTaskInfoMapper;

    /**
     * 返回当前Service的主表Mapper对象。
     *
     * @return 主表Mapper对象。
     */
    @Override
    protected BaseDaoMapper<SchResourcePool> mapper() {
        return schResourcePoolMapper;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SchResourcePool saveNew(SchResourcePool schResourcePool) {
        schResourcePoolMapper.insert(this.buildDefaultValue(schResourcePool));
        // 构建绑定关系
        List<SchResourceInfo> infoList = schResourcePool.getResourceInfoList();
        List<SchResourcePoolMember> newItem = new ArrayList<>();
        infoList.forEach(i->{
            log.info("当前数据id--{}",i.getId());
            SchResourcePoolMember item = new SchResourcePoolMember()
                    .setResourceId(i.getId())
                    .setPoolId(schResourcePool.getId())
                    .setId(idGenerator.nextLongId());
            newItem.add(item);

        });
        if (!newItem.isEmpty()) {
            resourcePoolMemberMapper.insertList(newItem);
        }
        return schResourcePool;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNewBatch(List<SchResourcePool> schResourcePoolList) {
        if (CollUtil.isNotEmpty(schResourcePoolList)) {
            schResourcePoolList.forEach(this::buildDefaultValue);
            schResourcePoolMapper.insertList(schResourcePoolList);
        }
    }

    /**
     *  执行数据更操作
     * @param schResourcePool         更新的对象。
     * @param originalSchResourcePool 原有数据对象。
     * @return 更新资源池数据
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean update(SchResourcePool schResourcePool, SchResourcePool originalSchResourcePool) {
        schResourcePool.setCreateUserId(originalSchResourcePool.getCreateUserId());
        schResourcePool.setCreateTime(originalSchResourcePool.getCreateTime());
        // 这里重点提示，在执行主表数据更新之前，如果有哪些字段不支持修改操作，请用原有数据对象字段替换当前数据字段。
        UpdateWrapper<SchResourcePool> uw = this.createUpdateQueryForNullValue(schResourcePool, schResourcePool.getId());
        // 更新数据关系
        boolean flag = schResourcePoolMapper.update(schResourcePool, uw) == 1;
        List<SchResourceInfo> resourceInfoList = schResourcePool.getResourceInfoList();
        if (CollUtil.isNotEmpty(resourceInfoList)) {
            List<Long> resourceIdList = resourceInfoList.stream()
                    .map(SchResourceInfo::getId)
                    .filter(Objects::nonNull)
                    .toList();
            // 查询当前池中已有的关联记录
            List<SchResourcePoolMember> existingRelations = schResourcePoolMapper
                    .queryOlderBind(resourceIdList, schResourcePool.getId());
            // 记录已经存在的映射关系
            Map<Long,SchResourcePoolMember>  existRelationMap = new HashMap<>();
            if(!existingRelations.isEmpty()){
                existingRelations.forEach(i->{
                    existRelationMap.put(i.getResourceId(),i);
                });
            }
            // 变更数据存储
            List<SchResourcePoolMember> newRelation = new ArrayList<>();
            for (SchResourceInfo  item : resourceInfoList) {
                Long resourceId = item.getId();
                // 不存在则更新
                if(!existRelationMap.containsKey(resourceId)){
                    newRelation.add(
                            new SchResourcePoolMember()
                                    .setResourceId(resourceId)
                                    .setPoolId(schResourcePool.getId())
                                    .setUpdateTime(new Date())
                                    .setCreateTime(new Date())
                    );

                }
            }
            if(!newRelation.isEmpty()){
                resourcePoolMemberMapper.insertList(newRelation);
            }

        }
        return flag;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean remove(Long id) {
        return schResourcePoolMapper.deleteById(id) == 1;
    }

    @Override
    public List<SchResourcePool> getSchResourcePoolList(SchResourcePool filter, String orderBy) {
        return schResourcePoolMapper.getSchResourcePoolList(filter, orderBy);
    }

    @Override
    public List<SchResourcePool> getSchResourcePoolListWithRelation(SchResourcePool filter, String orderBy) {
        List<SchResourcePool> resultList = schResourcePoolMapper.getSchResourcePoolList(filter, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        resultList.forEach(i->{
             List<SchResourceInfo>  infos = schResourcePoolMapper.selfBuildInfoList(i.getId());
             i.setResourceInfoList(infos);
             i.setResourcePoolStatistics(this.resourcePoolStatistics(i));
        });
        return resultList;
    }

    public List<SchResourcePool> getSchResourcePoolListWithRelationV1(SchResourcePool filter, String orderBy) {
        List<SchResourcePool> resultList = schResourcePoolMapper.getSchResourcePoolList(filter, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        resultList.forEach(i->{
            List<SchResourceInfo>  infos = schResourcePoolMapper.selfBuildInfoList(i.getId());
            i.setResourceInfoList(infos);
        });
        for (SchResourcePool schResourcePool:resultList) {
            Map<String, Object> resourcePoolStatistics = schResourcePool.getResourcePoolStatistics();
            schResourcePool.setResourcePoolStatistics(resourcePoolStatistics);
        }

        return resultList;
    }
    @Override
    public List<SchResourcePool> getGroupedSchResourcePoolListWithRelation(
            SchResourcePool filter, String groupSelect, String groupBy, String orderBy) {
        List<SchResourcePool> resultList =
                schResourcePoolMapper.getGroupedSchResourcePoolList(filter, groupSelect, groupBy, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        // NOTE: 这里只是包含了关联数据，聚合计算数据没有包含。
        // 主要原因是，由于聚合字段通常被视为普通字段使用，不会在group by的从句中出现，语义上也不会在此关联。
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    private SchResourcePool buildDefaultValue(SchResourcePool schResourcePool) {
        if (schResourcePool.getId() == null) {
            schResourcePool.setId(idGenerator.nextLongId());
        }
        TokenData tokenData = TokenData.takeFromRequest();
        assert tokenData != null;
        schResourcePool.setCreateUserId(tokenData.getUserId());
        schResourcePool.setCreateTime(new Date());
        schResourcePool.setUpdateTime(new Date());
        schResourcePool.setIsDelete(GlobalDeletedFlag.NORMAL);
        return schResourcePool;
    }

    @Override
    public Map<String, Object> getResourcePoolInfo() {
        //查询资源池总数
        Integer poolCount = schResourcePoolMapper.queryResourcePoolCount();
        //查询全部资源池中的资源个数（去重）
        List<Long> resourcePoolMemberResourceIdList = schResourcePoolMemberService.getResourcePoolMemberIdList();
        Integer resourceNumber = resourcePoolMemberResourceIdList.size();
        //根据资源id列表查询资源信息，得到资源集合
        //根据资源id列表查询cpu核数总数，总内存数，总计算卡内存（显卡内存）
        List<Map<String, Object>> resourceAggregationStats = schResourceInfoService.getResourceAggregationStats(resourcePoolMemberResourceIdList);
        //获取计算卡数量
        LambdaQueryWrapper<SchComputeDevice> queryWrapper = new LambdaQueryWrapper<>();
        if(!CollUtil.isEmpty(resourcePoolMemberResourceIdList)){
            queryWrapper.in(SchComputeDevice::getResourceId, resourcePoolMemberResourceIdList);
        }
        queryWrapper.eq(SchComputeDevice::getIsDelete, 1);
        List<SchComputeDevice> schComputeDeviceList = schComputeDeviceMapper.selectList(queryWrapper);
        int computeDeviceNumber = schComputeDeviceList.size();
        if(resourceAggregationStats != null){
            Map<String, Object> map = new HashMap<>();
            map.put("poolCount", poolCount);
            map.put("resourceNumber", resourceNumber);
            map.put("computeDeviceNumber", computeDeviceNumber);
            map.put("resourceAggregationStats", resourceAggregationStats);
            return map;
        }
        return null;
    }


    /**
     * 资源池统计。
     *
     * @param
     */
    @Override
    public Map<String, Object> resourcePoolStatistics(SchResourcePool schResourcePool) {
        Map<String, Object> map = new HashMap<>();
        //查询资源池下所有主机
        List<SchResourceInfo> schResourceInfoList = schResourceInfoMapper.queryResourceInfoByPoolId(schResourcePool.getId());
        List<Long> resourceIdList = schResourceInfoList.stream()
                .map(SchResourceInfo::getId)
                .toList();
        if(resourceIdList.isEmpty()){
            return map;
        }
        int totalCpuCores = 0;
        int computeDeviceNumber = 0;
        //int totalGpuCount = 0;
        long totalVideoMemory = 0;
        //计算池的cpu核心数，计算卡数，显存总量
        for (SchResourceInfo resourceInfo:schResourceInfoList) {
            totalCpuCores += Optional.ofNullable(resourceInfo.getCpuCoreCount())
                    .filter(s -> !s.isEmpty())
                    .map(Integer::parseInt)
                    .orElse(0);

//            totalGpuCount += Optional.ofNullable(resourceInfo.getGpuCount())
//                    .filter(s -> !s.isEmpty())
//                    .map(Integer::parseInt)
//                    .orElse(0);

            totalVideoMemory += Optional.ofNullable(resourceInfo.getGraphicsMemory())
                    .filter(s -> !s.isEmpty())
                    .map(Double::parseDouble)  // 先解析为 double
                    .map(Double::longValue)     // 再转为 long
                    .orElse(0L);

        }
        //获取资源池的计算卡数
        LambdaQueryWrapper<SchComputeDevice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SchComputeDevice::getResourceId, resourceIdList)
                .eq(SchComputeDevice::getIsDelete, 1);
        List<SchComputeDevice> schComputeDeviceList = schComputeDeviceMapper.selectList(queryWrapper);
        computeDeviceNumber = schComputeDeviceList.size();
        map.put("totalCpuCores",totalCpuCores);
        map.put("totalGpuCount",computeDeviceNumber);
        //map.put("totalGpuCount",totalGpuCount);
        map.put("totalVideoMemory",totalVideoMemory);
        List<SchNodeBasicMetrics> allSchNodeBasicMetricsList=new  ArrayList<>();
//        List<SchCardMonitor> allSchCardMonitorList=new ArrayList<>();
        //查询主机监控信息
        for(SchResourceInfo resourceInfo:schResourceInfoList){
            SchNodeBasicMetrics newSchNodeBasicMetrics = schNodeBasicMetricsService.getLatestData(resourceInfo.getId());
            if(newSchNodeBasicMetrics==null){
                continue;
            }
            allSchNodeBasicMetricsList.add(newSchNodeBasicMetrics);
//            List<SchCardMonitor> schCardMonitorList=schCardMonitorService.getNpuMonitoringByResourceInfoId(resourceInfo.getId(),newSchNodeBasicMetrics.getTs());
//            allSchCardMonitorList.addAll(schCardMonitorList);
        }
        //计算平均值
        SchNodeBasicMetrics schNodeBasicMetrics = schResourceInfoService.calculateCPUAverage(allSchNodeBasicMetricsList);
        map.put("cpuUtilization",schNodeBasicMetrics.getCpuUtilization());
        map.put("overallCpuUsage",schNodeBasicMetrics.getOverallCpuUsage());
        map.put("videoUtilization",schNodeBasicMetrics.getVideoUtilization());
        map.put("memoryUtilization",schNodeBasicMetrics.getMemoryUtilization());
        map.put("diskUtilization",schNodeBasicMetrics.getDiskUtilization());
        //查询任务列表
        List<SchTaskInfo> schTaskInfoList= schTaskInfoMapper.selectList(new LambdaQueryWrapper<SchTaskInfo>()
                .in(SchTaskInfo::getResourceId,resourceIdList));
// 等待初始化的 pending 任务状态
        List<SchTaskInfo> pendingSchTaskInfoList = schTaskInfoList.stream()
                .filter(i -> "pending".equals(i.getStatus()))
                .toList();

// 等待队列 queued 任务状态
        List<SchTaskInfo> queuedSchTaskInfoList = schTaskInfoList.stream()
                .filter(i -> "queued".equals(i.getStatus()))
                .toList();

// 启动中 starting 任务状态
        List<SchTaskInfo> startingSchTaskInfoList = schTaskInfoList.stream()
                .filter(i -> "starting".equals(i.getStatus()))
                .toList();
        //筛选初始化的stop任务状态
        List<SchTaskInfo> stopSchTaskInfoList= schTaskInfoList.stream()
                .filter(i -> "stop".equals(i.getStatus()))
                .toList();
        //筛选进行中得任务
        List<SchTaskInfo> runSchTaskInfoList= schTaskInfoList.stream()
                .filter(i -> "running".equals(i.getStatus()))
                .toList();
        //筛选完成任务
        List<SchTaskInfo> finishedSchTaskInfoList= schTaskInfoList.stream()
                .filter(i -> "finished".equals(i.getStatus()))
                .toList();
        //筛选失败得任务
        List<SchTaskInfo> failedSchTaskInfoList= schTaskInfoList.stream()
                .filter(i -> "failed".equals(i.getStatus()))
                .toList();
        map.put("pendingTask",pendingSchTaskInfoList.size());
        map.put("queuedTask",queuedSchTaskInfoList.size());
        map.put("startingTask",startingSchTaskInfoList.size());
        map.put("stopTask",stopSchTaskInfoList.size());
        map.put("runningTask",runSchTaskInfoList.size());
        map.put("finishedTask",finishedSchTaskInfoList.size());
        map.put("failedTask",failedSchTaskInfoList.size());
        map.put("totalTask",schTaskInfoList.size());
        //取出卡的监控信息
        List<SchCardMonitor> schCardMonitors = schResourcePoolService.computeDeviceInfoByResource(schResourcePool);
        Map<String, Object> mapCardMonitors = schResourcePoolService.sumDeviceInfo(schCardMonitors);
        map.putAll(mapCardMonitors);
        return map;
    }

    @Override
    public List<SchCardMonitor> computeDeviceInfoByResource(SchResourcePool schResourcePool) {
        Long id = schResourcePool.getId();
        List<SchResourceInfo> schResourceInfoList = schResourceInfoMapper.queryResourceInfoByPoolId(id);
        List<Long> resourceIdList = schResourceInfoList.stream()
                .map(SchResourceInfo::getId)
                .toList();
        LambdaQueryWrapper<SchComputeDevice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SchComputeDevice::getResourceId, resourceIdList);
        List<SchComputeDevice> schComputeDeviceList = schComputeDeviceMapper.selectList(queryWrapper);
        schComputeDeviceList.stream().map(SchComputeDevice::getId).toList();
        List<SchCardMonitor> schCardMonitorList = schCardMonitorService.getSchCardMonitorListByids(resourceIdList);
        return schCardMonitorList;
    }

    @Override
    public Map<String, Object> sumDeviceInfo(List<SchCardMonitor> schCardMonitorList) {
        Map<String, Object> map = new HashMap<>();
        Long totalHB = 0L;
        Long usedHB = 0L;
        if (CollUtil.isNotEmpty(schCardMonitorList)) {
            for (SchCardMonitor schCardMonitor:schCardMonitorList) {
                totalHB += schCardMonitor.getHbTotal();
                usedHB += schCardMonitor.getHbUsed();
            }
        }
        map.put("totalHB",totalHB);
        map.put("usedHB",usedHB);
        return map;
    }

    @Override
    public Map<String, Object> getAllCardStatistics() {

        return null;
    }
}
