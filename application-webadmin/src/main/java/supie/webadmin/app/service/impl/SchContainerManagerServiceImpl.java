package supie.webadmin.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import supie.common.core.exception.MyRuntimeException;
import supie.webadmin.app.service.*;
import supie.webadmin.app.dao.*;
import supie.webadmin.app.model.*;
import supie.common.core.base.dao.BaseDaoMapper;
import supie.common.core.constant.GlobalDeletedFlag;
import supie.common.core.object.TokenData;
import supie.common.core.object.MyRelationParam;
import supie.common.core.base.service.BaseService;
import supie.common.sequence.wrapper.IdGeneratorWrapper;
import com.github.pagehelper.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import supie.webadmin.app.util.DockerJavaUtil;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 容器管理数据操作服务类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Slf4j
@Service("schContainerManagerService")
public class SchContainerManagerServiceImpl extends BaseService<SchContainerManager, Long> implements SchContainerManagerService {

    @Autowired
    private IdGeneratorWrapper idGenerator;
    @Autowired
    private SchContainerManagerMapper schContainerManagerMapper;
    @Autowired
    private SchClusterNodeServiceImpl schClusterNodeService;
    @Autowired
    private DockerJavaUtil dockerJavaUtil;
    @Autowired
    private SchResourceInfoServiceImpl schResourceInfoService;
    @Autowired
    private SchTaskImageServiceImpl schTaskImageService;

    /**
     * 返回当前Service的主表Mapper对象。
     *
     * @return 主表Mapper对象。
     */
    @Override
    protected BaseDaoMapper<SchContainerManager> mapper() {
        return schContainerManagerMapper;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SchContainerManager saveNew(SchContainerManager schContainerManager) {
        schContainerManagerMapper.insert(this.buildDefaultValue(schContainerManager));
        return schContainerManager;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNewBatch(List<SchContainerManager> schContainerManagerList) {
        if (CollUtil.isNotEmpty(schContainerManagerList)) {
            schContainerManagerList.forEach(this::buildDefaultValue);
            schContainerManagerMapper.insertList(schContainerManagerList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean update(SchContainerManager schContainerManager, SchContainerManager originalSchContainerManager) {
        schContainerManager.setCreateUserId(originalSchContainerManager.getCreateUserId());
        schContainerManager.setUpdateUserId(TokenData.takeFromRequest().getUserId());
        schContainerManager.setUpdateTime(new Date());
        schContainerManager.setCreateTime(originalSchContainerManager.getCreateTime());
        // 这里重点提示，在执行主表数据更新之前，如果有哪些字段不支持修改操作，请用原有数据对象字段替换当前数据字段。
        UpdateWrapper<SchContainerManager> uw = this.createUpdateQueryForNullValue(schContainerManager, schContainerManager.getId());
        return schContainerManagerMapper.update(schContainerManager, uw) == 1;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean remove(Long id) {
        return schContainerManagerMapper.deleteById(id) == 1;
    }

    @Override
    public List<SchContainerManager> getSchContainerManagerList(SchContainerManager filter, String orderBy) {
        return schContainerManagerMapper.getSchContainerManagerList(filter, orderBy);
    }

    @Override
    public List<SchContainerManager> getSchContainerManagerListWithRelation(SchContainerManager filter, String orderBy) {
        List<SchContainerManager> resultList = schContainerManagerMapper.getSchContainerManagerList(filter, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    @Override
    public List<SchContainerManager> getGroupedSchContainerManagerListWithRelation(
            SchContainerManager filter, String groupSelect, String groupBy, String orderBy) {
        List<SchContainerManager> resultList =
                schContainerManagerMapper.getGroupedSchContainerManagerList(filter, groupSelect, groupBy, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        // NOTE: 这里只是包含了关联数据，聚合计算数据没有包含。
        // 主要原因是，由于聚合字段通常被视为普通字段使用，不会在group by的从句中出现，语义上也不会在此关联。
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    private SchContainerManager buildDefaultValue(SchContainerManager schContainerManager) {
        if (schContainerManager.getId() == null) {
            schContainerManager.setId(idGenerator.nextLongId());
        }
        TokenData tokenData = TokenData.takeFromRequest();
        schContainerManager.setCreateUserId(tokenData.getUserId());
        schContainerManager.setUpdateUserId(tokenData.getUserId());
        Date now = new Date();
        schContainerManager.setUpdateTime(now);
        schContainerManager.setCreateTime(now);
        schContainerManager.setIsDelete(GlobalDeletedFlag.NORMAL);
        return schContainerManager;
    }

    @Override
    public void changeServiceStatus(SchContainerManager schContainerManager, String serviceStatus) {
        String containerId = schContainerManager.getContainerId();
        SchClusterNode  schClusterNode=  schClusterNodeService.getById(schContainerManager.getClusterNodeId());
        Long schResourceInfoId = schClusterNode.getResourceInfoId();
        if ("start".equals(serviceStatus)){
            dockerJavaUtil.startContainer(containerId,schResourceInfoId);
            //更新状态
            schContainerManager.setContainerStatus("running");
            schContainerManager.setContainerOperation("start");
            schContainerManager.setContainerOperationTime(new Date());
            String timePeriods=schContainerManager.getContainerRunTime();
            schContainerManager.setContainerRunTime(this.updateContainerRunTime(timePeriods,serviceStatus));
            this.updateById(schContainerManager);
        }else if ("stop".equals(serviceStatus)){
            dockerJavaUtil.stopContainer(containerId,schResourceInfoId);
            //更新状态
            schContainerManager.setContainerStatus("stopped");
            schContainerManager.setContainerOperation("stop");
            schContainerManager.setContainerOperationTime(new Date());
            String timePeriods=schContainerManager.getContainerRunTime();
            schContainerManager.setContainerRunTime(this.updateContainerRunTime(timePeriods,serviceStatus));
            this.updateById(schContainerManager);

        }else if("restart".equals(serviceStatus)){
            dockerJavaUtil.restartContainer(containerId,schResourceInfoId);
            //更新状态
            schContainerManager.setContainerStatus("running");
            schContainerManager.setContainerOperation("restart");
            schContainerManager.setContainerOperationTime(new Date());
            String timePeriods=schContainerManager.getContainerRunTime();
            schContainerManager.setContainerRunTime(this.updateContainerRunTime(timePeriods,serviceStatus));
            this.updateById(schContainerManager);
        }
    }


    private String updateContainerRunTime(String timePeriods, String serviceStatus) {
        if(timePeriods==null){
            timePeriods="{\"time_periods\":[]}";
        }
        JSONObject timePeriodsJson = JSONObject.parseObject(timePeriods);
        JSONArray jsonArray = timePeriodsJson.getJSONArray("time_periods");
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String formattedDateTime = formatter.format(new Date());
        if("start".equals(serviceStatus)) {
            JSONObject startTime = new JSONObject();
            startTime.put("start_time", formattedDateTime);
            jsonArray.add(startTime);
        } else if ("stop".equals(serviceStatus)) {
            JSONObject lastJson = jsonArray.getJSONObject(jsonArray.size() - 1);
            if(lastJson.containsKey("stop_time")){
                return JSONObject.toJSONString(timePeriodsJson);
            }
            JSONObject stopTime = new JSONObject();
            stopTime.put("stop_time", formattedDateTime);
            jsonArray.add(stopTime);
        }else if ("restart".equals(serviceStatus)) {
            JSONObject lastJson = jsonArray.getJSONObject(jsonArray.size() - 1);
            if(lastJson.containsKey("start_time")){
                JSONObject stopTime = new JSONObject();
                stopTime.put("stop_time", formattedDateTime);
                jsonArray.add(stopTime);
            }
            JSONObject startTime = new JSONObject();
            startTime.put("start_time", formattedDateTime);
            jsonArray.add(startTime);
        }
        return JSONObject.toJSONString(timePeriodsJson);
    }


    /**
     * 容器状态统计。
     *
     * @return 应答结果对象，包含对象详情。
     */
    @Override
    public Map<String, Object> containerStateStatistics() {
        Map<String, Object> map = new HashMap<>();
        List<SchContainerManager> schContainerManagerList=schContainerManagerMapper.selectList( null);
        if(schContainerManagerList.isEmpty()){
            map.put("running",0);
            map.put("stopped",0);
            map.put("total",0);
            return map;
        }
        //根据状态分类
        Map<String, List<SchContainerManager>> statusMap = schContainerManagerList.stream().collect(Collectors.groupingBy(SchContainerManager::getContainerStatus));
        map.put("running",statusMap.get("running")==null?0:statusMap.get("running").size());
        map.put("stopped",statusMap.get("stopped")==null?0:statusMap.get("stopped").size());
        map.put("total",schContainerManagerList.size());
        //根据节点分类
        Map<Long, List<SchContainerManager>> nodeMap = schContainerManagerList.stream().collect(Collectors.groupingBy(SchContainerManager::getClusterNodeId));
        List<Map<String, Object>> nodeStatisticsList = new ArrayList<>();
        for (Map.Entry<Long, List<SchContainerManager>> entry : nodeMap.entrySet()) {
            Map<String, Object> nodeEntry = new HashMap<>();
            nodeEntry.put("clusterNodeId", entry.getKey());
            nodeEntry.put("count", entry.getValue().size());
            nodeStatisticsList.add(nodeEntry);
        }
        map.put("nodeStatistics", nodeStatisticsList);
        return map;
    }

    /**
     * 创建并启动容器。
     *
     * @return 应答结果对象，包含对象详情。
     */
    @Override
    public SchContainerManager createAndStartContainer(SchContainerManager schContainerManager) {
        //查询集群节点
        SchClusterNode schClusterNode=schClusterNodeService.getById(schContainerManager.getClusterNodeId());
        if(schClusterNode==null){
            throw new MyRuntimeException("集群节点不存在！");
        }
        //查询远程主机
        SchResourceInfo schResourceInfo = schResourceInfoService.getById(schClusterNode.getResourceInfoId());
        if (schResourceInfo == null) {
            throw new MyRuntimeException("远程主机不存在！");
        }
        //查询已启动容器
        List<SchContainerManager> schContainerManagerList=schContainerManagerMapper.selectList( new LambdaQueryWrapper<SchContainerManager>()
                .eq(SchContainerManager::getContainerStatus,"running"));
        //提取端口
        List<String> ports = schContainerManagerList.stream().map(SchContainerManager::getExportPort).toList();
        String exportPort;
        if(schContainerManager.getExportPort()==null){
            //随机生成端口
            exportPort = String.valueOf(dockerJavaUtil.getPort(schResourceInfo.getHostIp()));
        }else {
            exportPort = schContainerManager.getExportPort();
            if (ports.contains(exportPort)) {
                throw new MyRuntimeException("端口已被占用！");            }
        }
        //查询镜像
        SchTaskImage schTaskImage = schTaskImageService.getById(schContainerManager.getTaskImageId());
        String images  = schTaskImage.getImageName()+":"+schTaskImage.getImageVersion();
        //启动容器
        String containerId = dockerJavaUtil.createServiceCmd(schContainerManager.getContainerName(),images,schContainerManager.getInnerPort(),exportPort,schResourceInfo.getId());
        schContainerManager.setContainerId(containerId);
        schContainerManager.setExportPort(exportPort);
        schContainerManager.setContainerStatus("running");
        schContainerManager.setContainerRunTime(updateContainerRunTime(schContainerManager.getContainerRunTime(),"start"));
        schContainerManager.setContainerOperation("start");
        schContainerManager.setContainerOperationTime(new Date());
        schContainerManager.setDataDeptId(TokenData.takeFromRequest().getDeptId());
        schContainerManager.setDataUserId(TokenData.takeFromRequest().getUserId());
        schContainerManager = this.saveNew(schContainerManager);
        return schContainerManager;
    }

    /**
     * 删除容器。
     *
     * @return 应答结果对象，包含对象详情。
     */
    @Override
    public void deleteContainer(SchContainerManager schContainerManager) {
        //查询集群节点
        SchClusterNode schClusterNode=schClusterNodeService.getById(schContainerManager.getClusterNodeId());
        if(schClusterNode==null){
            throw new MyRuntimeException("集群节点不存在！");
        }
        //查询远程主机
        SchResourceInfo schResourceInfo = schResourceInfoService.getById(schClusterNode.getResourceInfoId());
        if (schResourceInfo == null) {
            throw new MyRuntimeException("远程主机不存在！");
        }
        dockerJavaUtil.stopContainer(schContainerManager.getContainerId(), schResourceInfo.getId());
        dockerJavaUtil.deleteContainer(schContainerManager.getContainerId(), schResourceInfo.getId());
        schContainerManager.setContainerRunTime(updateContainerRunTime(schContainerManager.getContainerRunTime(),"stop"));
        schContainerManager.setContainerStatus("stopped");
        schContainerManagerMapper.updateById(schContainerManager);
        schContainerManagerMapper.deleteById(schContainerManager.getId());
    }

}
