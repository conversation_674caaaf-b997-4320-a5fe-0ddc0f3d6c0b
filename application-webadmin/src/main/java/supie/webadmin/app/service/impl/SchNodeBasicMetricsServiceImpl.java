package supie.webadmin.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import jakarta.annotation.Resource;
import org.springframework.scheduling.annotation.Scheduled;
import supie.common.core.annotation.MyDataSource;
import supie.webadmin.app.service.*;
import supie.webadmin.app.dao.*;
import supie.webadmin.app.model.*;
import supie.common.core.base.dao.BaseDaoMapper;
import supie.common.core.constant.GlobalDeletedFlag;
import supie.common.core.object.TokenData;
import supie.common.core.object.MyRelationParam;
import supie.common.core.base.service.BaseService;
import supie.common.sequence.wrapper.IdGeneratorWrapper;
import com.github.pagehelper.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import supie.webadmin.app.util.CpuMonitoringInformationToolUtil;
import supie.webadmin.app.util.GpuMonitoringInformationToolUtil;
import supie.webadmin.app.util.NpuToolUtil;
import supie.webadmin.config.DataSourceType;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 节点基础监控表数据操作服务类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Slf4j
@Service("schNodeBasicMetricsService")
@MyDataSource(DataSourceType.CLICKHOME)
public class SchNodeBasicMetricsServiceImpl extends BaseService<SchNodeBasicMetrics, Long> implements SchNodeBasicMetricsService {

    @Autowired
    private IdGeneratorWrapper idGenerator;
    @Autowired
    private SchNodeBasicMetricsMapper schNodeBasicMetricsMapper;
    @Autowired
    private SchComputeDeviceMapper schComputeDeviceMapper;
    @Autowired
    private SchResourceInfoService schResourceInfoService;
    @Autowired
    private SchCardMonitorService schCardMonitorService;
    @Autowired
    private SchComputeDeviceService schComputeDeviceService;
    @Resource
    private CpuMonitoringInformationToolUtil cpuMonitoringInformationToolUtil;
    @Resource
    private NpuToolUtil npuToolUtil;
    @Resource
    private GpuMonitoringInformationToolUtil gpuMonitoringInformationToolUtil;

    /**
     * 返回当前Service的主表Mapper对象。
     *
     * @return 主表Mapper对象。
     */
    @Override
    protected BaseDaoMapper<SchNodeBasicMetrics> mapper() {
        return schNodeBasicMetricsMapper;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SchNodeBasicMetrics saveNew(SchNodeBasicMetrics schNodeBasicMetrics) {
        schNodeBasicMetricsMapper.insert(this.buildDefaultValue(schNodeBasicMetrics));
        return schNodeBasicMetrics;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNewBatch(List<SchNodeBasicMetrics> schNodeBasicMetricsList) {
        if (CollUtil.isNotEmpty(schNodeBasicMetricsList)) {
            schNodeBasicMetricsList.forEach(this::buildDefaultValue);
            schNodeBasicMetricsMapper.insertList(schNodeBasicMetricsList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean update(SchNodeBasicMetrics schNodeBasicMetrics, SchNodeBasicMetrics originalSchNodeBasicMetrics) {
        schNodeBasicMetrics.setCreateUserId(originalSchNodeBasicMetrics.getCreateUserId());
        schNodeBasicMetrics.setCreateTime(originalSchNodeBasicMetrics.getCreateTime());
        // 这里重点提示，在执行主表数据更新之前，如果有哪些字段不支持修改操作，请用原有数据对象字段替换当前数据字段。
        UpdateWrapper<SchNodeBasicMetrics> uw = this.createUpdateQueryForNullValue(schNodeBasicMetrics, schNodeBasicMetrics.getId());
        return schNodeBasicMetricsMapper.update(schNodeBasicMetrics, uw) == 1;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean remove(Long id) {
        return schNodeBasicMetricsMapper.deleteById(id) == 1;
    }

    @Override
    public List<SchNodeBasicMetrics> getSchNodeBasicMetricsList(SchNodeBasicMetrics filter, String orderBy) {
        return schNodeBasicMetricsMapper.getSchNodeBasicMetricsList(filter, orderBy);
    }

    @Override
    public List<SchNodeBasicMetrics> getSchNodeBasicMetricsListWithRelation(SchNodeBasicMetrics filter, String orderBy) {
        List<SchNodeBasicMetrics> resultList = schNodeBasicMetricsMapper.getSchNodeBasicMetricsList(filter, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    @Override
    public List<SchNodeBasicMetrics> getGroupedSchNodeBasicMetricsListWithRelation(
            SchNodeBasicMetrics filter, String groupSelect, String groupBy, String orderBy) {
        List<SchNodeBasicMetrics> resultList =
                schNodeBasicMetricsMapper.getGroupedSchNodeBasicMetricsList(filter, groupSelect, groupBy, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        // NOTE: 这里只是包含了关联数据，聚合计算数据没有包含。
        // 主要原因是，由于聚合字段通常被视为普通字段使用，不会在group by的从句中出现，语义上也不会在此关联。
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    private SchNodeBasicMetrics buildDefaultValue(SchNodeBasicMetrics schNodeBasicMetrics) {
        if (schNodeBasicMetrics.getId() == null) {
            schNodeBasicMetrics.setId(idGenerator.nextLongId());
        }
        TokenData tokenData = TokenData.takeFromRequest();
        schNodeBasicMetrics.setCreateUserId(tokenData.getUserId());
        schNodeBasicMetrics.setCreateTime(new Date());
        schNodeBasicMetrics.setUpdateTime(new Date());
        schNodeBasicMetrics.setIsDelete(GlobalDeletedFlag.NORMAL);
        return schNodeBasicMetrics;
    }

    @Override
    //@Scheduled(cron = "0/20 * * * * *")
    //@Scheduled(cron = "0 10 * * * *")
    public void collectNodeBasicInfo(){
        // 记录日志，标记监控数据获取的开始
        log.info("====================> 监听远程服务器实时修改监控数据 <====================");
        int len = 0;
        // 从数据库中获取所有远程主机列表
        List<SchResourceInfo> schResourceInfoList = schResourceInfoService.getAllList();
        if (schResourceInfoList == null || schResourceInfoList.isEmpty()) {
            log.info("没有找到远程主机");
            return;
        }
        List<SchCardMonitor> collectSchCardMonitor = new ArrayList<>();
        Map<Long, List<SchComputeDevice>> collectComputeDevices = schComputeDeviceService.getAllComputeDeviceList().stream()
                .collect(Collectors.groupingBy(SchComputeDevice::getResourceId));
        //统一监控时间
        Timestamp ts=new Timestamp(System.currentTimeMillis());
        for (SchResourceInfo schResourceInfo : schResourceInfoList) {
            //
            List<Map<String, String>> npuInformations  ;
            if (schResourceInfo.getComputeDevicePort() != null && schResourceInfo.getCpuPort()  != null && !schResourceInfo.getComputeDevicePort().isEmpty()) {
                npuInformations = npuToolUtil.npuInformationss(
                        "http://" + schResourceInfo.getHostIp() + ":" + schResourceInfo.getComputeDevicePort() + "/metrics"
                );
            }else {
                log.error("远程服务器 {} 缺少NPU普罗米修斯端口号", schResourceInfo.getHostIp());
                continue;
            }
            //
            if(npuInformations.isEmpty()){
                schResourceInfo.setStatus("offline");
                log.error("远程服务器 {} 状态异常", schResourceInfo.getHostIp());
                continue;
            }
            List<Map<String, String>> serialNumberList = Optional.ofNullable(npuInformations)
                    .orElse(Collections.emptyList()).stream()
                    .sorted(Comparator.comparingInt(map -> Integer.parseInt(map.get("serialNumber"))))
                    .collect(Collectors.toList());//排序
            double totalFbMemory = 0.0;
            double usedFbMemory = 0.0;
            double videoUtilization = 0.0;
                if(!npuInformations.isEmpty()){//计算节点显存
                    for (Map<String, String> npuInformation : npuInformations) {
                        totalFbMemory += Double.parseDouble(npuInformation.get("fbTotal"));
                        usedFbMemory += Double.parseDouble(npuInformation.get("fbUsed"));
                    }
                    videoUtilization = (usedFbMemory / totalFbMemory) * 100;
                }


            Map<String, String> map = new HashMap<>();
            //Timestamp ts=new Timestamp(System.currentTimeMillis());
            List<SchComputeDevice> schComputeDeviceList = collectComputeDevices.get(schResourceInfo.getId());
            for (SchComputeDevice schComputeDevice:schComputeDeviceList) {
                Integer deviceNumber = schComputeDevice.getDeviceNumber();
                if (serialNumberList != null && !serialNumberList.isEmpty()) {
                    map = serialNumberList.get(deviceNumber);
                    if(map.isEmpty()){
                        log.debug(JSONObject.toJSONString(serialNumberList));
                        log.error("远程服务器 {} 缺少NPU {} 显存信息", schResourceInfo.getId(), deviceNumber);
                        continue;
                    }
                    map.put("serialNumber", deviceNumber.toString());
                    SchCardMonitor schCardMonitor = new SchCardMonitor();
                    schCardMonitor.setResourceId(schResourceInfo.getId());
                    schCardMonitor.setComputeDeviceId(schComputeDevice.getId());
                    schCardMonitor.setMonitorType(1);
                    schCardMonitor.setUpdateTime(new Date());
                    schCardMonitor.setCreateTime(new Date());
                    //if (tokenData != null) {
                        schCardMonitor.setCreateUserId(1923195822141345792L);
                   // }
                    log.debug("监控map: {}", map);
                    schCardMonitor.setIsDelete(GlobalDeletedFlag.NORMAL);
                    schCardMonitor.setCardUtilization((BigDecimal.valueOf(Long.parseLong(map.get("utilRate")))));
                    schCardMonitor.setHbTotal(Long.valueOf(map.get("fbTotal")));
                    schCardMonitor.setTemp(Integer.parseInt(map.get("temp")));
                    schCardMonitor.setHbUtil(map.get("fbUsedUtil"));
                    schCardMonitor.setHbUsed(Long.valueOf(map.get("fbUsed")));
//                    if(map)
//                    Double totalMemMemory = Double.parseDouble(map.get("totalMemMemory")) == 0.0 ? 0.0 : Double.parseDouble(map.get("totalMemMemory"));
//                    Double usedMemMemory = Double.parseDouble(map.get("usedMemMemory")) == 0.0 ? 0.0 : Double.parseDouble(map.get("usedMemMemory"));
//                    schCardMonitor.setMemoryTotal(totalMemMemory);
//                    schCardMonitor.setMemoryUsed(usedMemMemory);
                    schCardMonitor.setCardUtilization(BigDecimal.valueOf(Double.parseDouble(map.get("utilRate"))));
                    schCardMonitor.setTs(ts);
                    schCardMonitor.setSerialNumber(Integer.parseInt(map.get("serialNumber")));
                    collectSchCardMonitor.add(schCardMonitor);
                    map.clear();
                }else {
                    log.error("远程服务器 {} 缺少NPU信息", schResourceInfo.getHostIp());
                    break;
                }
            }
            //存储npu监控信息
            if(!collectSchCardMonitor.isEmpty()){
                schCardMonitorService.saveNewBatch(collectSchCardMonitor);
                int size = collectSchCardMonitor.size();
                collectSchCardMonitor.clear();
                log.info("{} NPU监控数据新增成功", schResourceInfo.getHostIp());
                len += size;
            }


            // 检查CPU Prometheus端口是否存在
            if (schResourceInfo.getCpuPort() == null) {
                log.error("远程服务器 {} 缺少CPU普罗米修斯端口号", schResourceInfo.getHostIp());
                continue;
            }

            // 获取CPU的监控数据
            Map<String, Double> cpuMonitoringData = cpuMonitoringInformationToolUtil.getCpuMonitoringInformationTool(
                    "http://" + schResourceInfo.getHostIp() + ":" + schResourceInfo.getCpuPort() + "/metrics"
            );
            cpuMonitoringData.put("videoUtilization", videoUtilization);
            if (cpuMonitoringData == null || cpuMonitoringData.isEmpty()) {
                log.info("{} 无CPU监控数据", schResourceInfo.getHostIp());
                continue;
            }

            List<SchNodeBasicMetrics> hardwareMonitoringDataList = createHardwareMonitoringDataList(cpuMonitoringData, schResourceInfo,ts);

            // 将收集到的CPU监控数据批量插入数据库
            schNodeBasicMetricsMapper.insertList(hardwareMonitoringDataList);
            log.info("{} CPU监控数据新增成功", schResourceInfo.getHostIp());
        }
        log.info("NPU监控数据新增成功,共{}条", len);
    }

    private List<SchNodeBasicMetrics> createHardwareMonitoringDataList(Map<String, Double> cpuMonitoringData, SchResourceInfo schResourceInfo,Timestamp ts) {
        List<SchNodeBasicMetrics> list = new ArrayList<>();
        SchNodeBasicMetrics data = new SchNodeBasicMetrics();
        // 假设cpuMonitoringData的keys与HardwareMonitoringData的字段对应
        data.setCpuUtilization(BigDecimal.valueOf(cpuMonitoringData.get("cpuUtilization")));
        data.setOverallCpuUsage(BigDecimal.valueOf(cpuMonitoringData.get("overallCpuUsage")));
        data.setMemoryUtilization(BigDecimal.valueOf(cpuMonitoringData.get("systemMemoryUtilizatiion")));
        data.setNetworkSpeedSum(cpuMonitoringData.get("networkSpeedSum"));
        data.setAvailableMemory(cpuMonitoringData.get("availableMemory"));
        data.setDiskIoWrittenAndReadSum(cpuMonitoringData.get("diskIOWrittenAndReadSum"));
        data.setNowThread(cpuMonitoringData.get("nowThread"));
        data.setProcessMemoryUsage(cpuMonitoringData.get("processMemoryUsage"));
        data.setProcessMemoryAmount(cpuMonitoringData.get("processMemoryAmount"));
        data.setDiskUsedSum(cpuMonitoringData.get("diskUsedSum"));
        data.setDiskUtilization(cpuMonitoringData.get("diskUtilization"));
        data.setTs(ts);
        data.setResourceId(schResourceInfo.getId());
        data.setIsDelete(GlobalDeletedFlag.NORMAL);
        data.setCreateTime(new Date());
        data.setUpdateTime(new Date());
        data.setDataDeptId(schResourceInfo.getDataDeptId());
        data.setDataUserId(schResourceInfo.getDataUserId());
        data.setDataUserId(schResourceInfo.getDataUserId());
        data.setVideoUtilization(BigDecimal.valueOf(cpuMonitoringData.get("videoUtilization")));//-----------------显存使用率
        list.add(data);
        return list;
    }

    private SchCardMonitor handleTypeGPURemoteHost(SchResourceInfo schResourceInfo,  SchComputeDevice schComputeDevice) {
        if (schResourceInfo.getComputeDevicePort() == null || schResourceInfo.getComputeDevicePort().isEmpty()) {
            log.error("远程服务器 {} 缺少GPU普罗米修斯端口号", schResourceInfo.getHostIp());
            return null;
        }
        try{
            Map<String, Object> gpuInformation = gpuMonitoringInformationToolUtil.getGpuInformation("http://" + schResourceInfo.getHostIp() + ":" + schResourceInfo.getComputeDevicePort() + "/metrics");
            SchCardMonitor schCardMonitor = new SchCardMonitor();
            schCardMonitor.setResourceId(schResourceInfo.getId());
            schCardMonitor.setId(idGenerator.nextLongId());
            schCardMonitor.setMonitorType(2);
            schCardMonitor.setUpdateTime(new Date());
            schCardMonitor.setCreateTime(new Date());
            schCardMonitor.setCreateUserId(TokenData.takeFromRequest().getUserId());
            schCardMonitor.setIsDelete(GlobalDeletedFlag.NORMAL);
            schCardMonitor.setComputeDeviceId(schComputeDevice.getId());
            schCardMonitor.setHbUsed((Long) gpuInformation.get("hbUsed"));
            schCardMonitor.setHbTotal((Long) gpuInformation.get("graphicsMemorySum"));
            schCardMonitor.setHbUtil((String) gpuInformation.get("graphicsMemoryUtilization"));
            return schCardMonitor;
        }catch (Exception e){
            log.error("远程服务器 {} 获取gpu信息失败，{}", schResourceInfo.getHostIp(), e.getMessage());
        }
        log.info("{} GPU监控数据新增成功", schResourceInfo.getHostIp());
        return null;
    }

    private List<Map<String, Object>> handleTypeNPURemoteHost(SchResourceInfo schResourceInfo) {
        if (schResourceInfo.getComputeDevicePort() == null || schResourceInfo.getComputeDevicePort().isEmpty()) {
            log.error("远程服务器 {} 缺少NPU普罗米修斯端口号", schResourceInfo.getHostIp());
            return null;
        }
        List<Map<String,Object>> npuMonitoringInformationTool = npuToolUtil.getNpuInformation(
                "http://" + schResourceInfo.getHostIp() + ":" + schResourceInfo.getComputeDevicePort() + "/metrics");
        return npuMonitoringInformationTool;
    }

    @Override
    public Double getAllNodeCpuUsage() {
        Double avgCpuUsage = schNodeBasicMetricsMapper.getAvgCpuUsage();
        return avgCpuUsage;
    }

    @Override//resource_id -> {Long@20779} 1929793323694821376
    public List<Map<String, Object>> getSchNodeBasicMetrics() {
        List<Map<String, Object>> result = schNodeBasicMetricsMapper.getSchNodeBasicMetrics();
        List<Map<String, Object>> newList = new ArrayList<>();
        Set<Long> resourceIdList = new HashSet<>();

        List<SchResourceInfo> schResourceInfoList = schResourceInfoService.getAllList();
        List<Long> resourceInfoIdList = schResourceInfoList.stream()
                .map(SchResourceInfo::getId)
                .collect(Collectors.toList());
        Map<Long, String> resourceMap = schResourceInfoList.stream()
                .collect(Collectors.toMap(
                        SchResourceInfo::getId,
                        info -> Objects.toString(info.getResourceName(), "未知主机"),
                        (existing, replacement) -> existing // 处理重复键的情况
                ));

        for (Map<String, Object> row : result) {
            Map<String, Object> newRow = new HashMap<>();
            Long resourceId = (Long) row.get("resource_id");
            if(!resourceInfoIdList.contains(resourceId)){
                continue;
            }
            newRow.put("resourceId", resourceId);
            newRow.put("resourceName", resourceMap.getOrDefault(resourceId, "未知主机"));
            newRow.put("cpuUsage", row.get("overall_cpu_usage"));
            newRow.put("memoryUtil", row.get("memory_utilization"));
            newRow.put("videoUtil", row.get("video_utilization"));
            newRow.put("timestamp", row.get("ts"));

            newList.add(newRow);
        }

        return newList;
    }

    /**
     * 获取最新一条数据。
     *
     * @return 主表Mapper对象。
     */
    @Override
    public SchNodeBasicMetrics getLatestData(Long resourceId) {
        return schNodeBasicMetricsMapper.getLatestData(resourceId);
    }

    /**
     * 根据主机id查询监控。
     *
     * @return 主表Mapper对象。
     */
    @Override
    public SchNodeBasicMetrics getCpuMonitoringByResourceId(Long resourceId, Date ts) {
        return schNodeBasicMetricsMapper.getCpuMonitoringByResourceId(resourceId,ts);
    }

    /**
     * 获取指定数据
     * @return
     */
    @Override
    public List<SchNodeBasicMetrics> getSchNodeBasicMetricsListByFilter(SchNodeBasicMetrics schNodeBasicMetrics,List<Long> resourceIdList) {
        return schNodeBasicMetricsMapper.statisticalIndicators(schNodeBasicMetrics, resourceIdList);
    }

    /**
     * 获取最近20分钟内数据
     * @return
     */
    @Override
    public List<SchNodeBasicMetrics> getRecentData(List<Long> resourceIdList) {
        return schNodeBasicMetricsMapper.getSchNodeBasicMetricsListByTs(resourceIdList);
    }
}
