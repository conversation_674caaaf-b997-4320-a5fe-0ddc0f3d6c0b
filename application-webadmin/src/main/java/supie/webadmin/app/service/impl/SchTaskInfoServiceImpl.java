package supie.webadmin.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.dockerjava.api.command.InspectContainerResponse;
import com.jcraft.jsch.JSchException;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import supie.common.core.exception.MyRuntimeException;
import supie.common.core.object.TokenData;
import supie.webadmin.app.dto.RemoteTask;
import supie.webadmin.app.dto.ResourceAllocation;
import supie.webadmin.app.dto.SchTaskInfoDto;
import supie.webadmin.app.service.*;
import supie.webadmin.app.dao.*;
import supie.webadmin.app.model.*;
import supie.common.core.base.dao.BaseDaoMapper;
import supie.common.core.constant.GlobalDeletedFlag;
import supie.common.core.object.MyRelationParam;
import supie.common.core.base.service.BaseService;
import supie.common.sequence.wrapper.IdGeneratorWrapper;
import com.github.pagehelper.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import supie.webadmin.app.util.*;
import supie.webadmin.app.vo.RemoteTaskVo;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 任务表数据操作服务类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Slf4j
@Service("schTaskInfoService")
public class SchTaskInfoServiceImpl extends BaseService<SchTaskInfo, Long> implements SchTaskInfoService {

    @Autowired
    private IdGeneratorWrapper idGenerator;
    @Autowired
    private SchTaskInfoMapper schTaskInfoMapper;

    @Resource
    SshConnectionUtil sshConnectionUtil;

    @Resource
    private SchResourceInfoMapper schResourceInfoMapper;

    @Resource
    private ComposeGenerate composeGenerate;

    @Resource
    private SchVirtualComputeCardSituationMapper schVirtualComputeCardSituationMappers;

    @Resource
    private SchTaskApprovalMapper schTaskApprovalMapper;
    @Autowired
    private SchNodeBasicMetricsService schNodeBasicMetricsService;
    @Autowired
    private SchCardMonitorService schCardMonitorService;

    @Resource
    private SchComputeDeviceMapper schComputeDeviceMapper;

    @Resource
    private SchTaskImageMapper schTaskImageMapper;

    @Resource
    private VnpuMonitorUtil vnpuMonitorUtil;

    @Value("${application.composeBasePath}")
    private String mountPath;

    @Resource(name = "poolExecutor")
    private ExecutorService poolExecutor;

    @Resource
    private SchScalePlanMapper schScalePlanMapper;

    @Resource
    private SchTaskMonitoringMapper schTaskMonitoringMapper;

    @Resource
    private SchContainerManagerMapper schContainerManagerMapper;

    @Value("${nginx.mount-path}")
    private String nginxConfig;

    @Resource
    private SchBusinessDictMapper schBusinessDictMapper;

    @Resource
    private DockerJavaUtil dockerJavaUtil;



    /**
     * nginx 链接地址
     */
    @Value("${nginx.gateway-url}")
    private  String linkUrl;


    /**
     * 审核任务
     *
     * @param taskId 任务ID
     * @param status 任务状态
     * @return 返回审核任务结果集
     */
    @Override
    public SchTaskApproval auditTask(String taskId, @NotNull String status) {
        schTaskApprovalMapper.update(
                new LambdaUpdateWrapper<SchTaskApproval>()
                        .eq(SchTaskApproval::getTaskId, Long.valueOf(taskId))
                        .set(SchTaskApproval::getDecisionTime, new Date())
                        .set(SchTaskApproval::getUpdateTime, new Date())
                        .set(SchTaskApproval::getStatus, status)

        );
        //  更新任务表 1930928779136339968
        schTaskInfoMapper.update(new LambdaUpdateWrapper<SchTaskInfo>()
                .eq(SchTaskInfo::getId, Long.valueOf(taskId))
                .set(SchTaskInfo::getUpdateTime, new Date())
                .set(SchTaskInfo::getUpdateUserId, Objects.requireNonNull(TokenData.takeFromRequest()).getUserId())
                .set(SchTaskInfo::getApproveState, status)

        );
        return schTaskApprovalMapper.selectOne(new LambdaQueryWrapper<SchTaskApproval>().eq(SchTaskApproval::getTaskId, Long.valueOf(taskId)));
    }

    /**
     * 构建服务器的 资源池、资源、计算卡、切分ID
     *
     * @return 返回资源信息集合
     */
    @Override
    public List<RootNode> relationResource() {
        List<ResourceTree> deviceSliceList = schResourceInfoMapper.queryDeviceSlice();  // 包含设备、切片信息
        List<ResourceTree> resourcePoolList = schResourceInfoMapper.queryResourcePool();  // 包含资源、资源池信息
        List<SchTaskInfo> taskList = schTaskInfoMapper.selectList(new LambdaQueryWrapper<SchTaskInfo>()
                .isNotNull(SchTaskInfo::getPartitionId));
        Map<Long, List<SchTaskInfo>> taskMap = taskList.stream().collect(Collectors.groupingBy(SchTaskInfo::getPartitionId));

        // 2 合并所有数据，并按 poolId 分组，用于构建 RootNode
        Map<Long, List<ResourceTree>> rootGroup = Stream.concat(
                        deviceSliceList.stream(),
                        resourcePoolList.stream()
                ).filter(tree -> tree.getPoolId() != null)
                .collect(Collectors.groupingBy(ResourceTree::getPoolId));

        List<RootNode> treeResult = new ArrayList<>();

        // 3 构建完整树形结构
        rootGroup.forEach((poolId, rootTrees) -> {
            if (rootTrees.isEmpty()) {
                return;
            }
            // 3.1 创建 RootNode
            RootNode rootNode = new RootNode();
            rootNode.setPoolId(poolId);
            rootNode.setPoolName(rootTrees.get(0).getPoolName());

            // 3.2 按 resourceId 分组，构建 ResourceNode
            Map<Long, List<ResourceTree>> resourceGroup = rootTrees.stream()
                    .filter(tree -> tree.getResourceId() != null)
                    .collect(Collectors.groupingBy(ResourceTree::getResourceId));

            // / 3.2 构建 ResourceNode 结点列表
            List<ResourceNode> resourceNodes = new ArrayList<>();

            resourceGroup.forEach((resourceId, resourceTrees) -> {
                ResourceNode resourceNode = new ResourceNode();
                resourceNode.setResourceId(resourceId);
                if (!resourceTrees.isEmpty()) {
                    resourceNode.setResourceName(resourceTrees.get(0).getResourceName());
                }
                //  获取当前 资源所关的 设备、切片信息
                List<ResourceTree> currentDeviceSlices = deviceSliceList
                        .stream()
                        .filter(t ->
                                Objects.equals(t.getResourceId(), resourceId))
                        .toList();
                // 3.3 按 deviceId 分组，构建 DeviceNode
                Map<Long, List<ResourceTree>> deviceGroup = currentDeviceSlices
                        .stream()
                        .filter(tree -> tree.getDeviceId() != null)
                        .collect(Collectors.groupingBy(ResourceTree::getDeviceId));
                // 构建   ResourceNode 孩子结点列表  列表 DeviceNode
                List<DeviceNode> deviceNodes = new ArrayList<>();
                deviceGroup.forEach((deviceId, deviceTrees) -> {
                    DeviceNode deviceNode = new DeviceNode();
                    deviceNode.setDeviceId(deviceId);
                    if (!deviceTrees.isEmpty()) {
                        deviceNode.setDeviceName(deviceTrees.get(0).getModelNumber());
                    }
                    // 3.4 构建 DeviceNode 孩子节点列表 SliceNode
                    List<SliceNode> sliceNodes = deviceTrees.stream()
                            .filter(t -> t.getSliceId() != null && t.getVnpuId() != null)
                            .map(t -> {
                                SliceNode sliceNode = new SliceNode();
                                sliceNode.setSliceId(t.getSliceId());
                                sliceNode.setVnpuId(t.getVnpuId());
                                sliceNode.setSchTaskInfoList(taskMap.get(t.getVnpuId()));
                                return sliceNode;
                            })
                            .distinct()
                            .collect(Collectors.toList());
                    deviceNode.setChildrenSlice(sliceNodes);
                    deviceNodes.add(deviceNode);
                });

                resourceNode.setChildrenDevice(deviceNodes);
                resourceNodes.add(resourceNode);
            });
            rootNode.setChildrenResource(resourceNodes);
            treeResult.add(rootNode);
        });
        return treeResult;
    }

    /**
     * 停止任务
     *
     * @param taskId 任务id
     */
    @Override
    public SchTaskInfo stopTask(Long taskId) {
        SchTaskInfo schTaskInfo = schTaskInfoMapper.selectById(Objects.requireNonNull(taskId, "任务id不能为空"));
        SchResourceInfo schResourceInfo = schResourceInfoMapper.selectById(schTaskInfo.getResourceId());
        try {
            SshConnection connection = sshConnectionUtil.createConnection(schResourceInfo.getHostIp(), schResourceInfo.getPort(),
                    schResourceInfo.getLoginName(), schResourceInfo.getPassword(), 30000);
            String result = sshConnectionUtil.executeCommand(connection, "docker stop " + Objects.requireNonNull(schTaskInfo.getContainerName(), "当前任务不存在"));
            if (schTaskInfo.getContainerName().equals(result.trim())) {
                schTaskInfoMapper.update(new LambdaUpdateWrapper<SchTaskInfo>()
                        .eq(SchTaskInfo::getId, taskId)
                        .set(SchTaskInfo::getStatus, "stop")
                        .set(SchTaskInfo::getContainerStatus, "paused"));
                return schTaskInfoMapper.selectById(taskId);
            } else {
                throw new MyRuntimeException("任务停止失败");
            }
        } catch (JSchException | IOException e) {
            log.error("任务启动失败");
            throw new MyRuntimeException("任务停止失败");


        }
    }

    /**
     * 定时更新任务数据状态
     */
    @Override
    public void upDateTaskStatus() {
        List<SchTaskInfo> info = schTaskInfoMapper.checkTaskList();
        if (CollUtil.isNotEmpty(info)) {
            info.forEach(item -> {
                try {
                    // 获取容器状态信息
                    InspectContainerResponse exec = dockerJavaUtil.connect(item.getResourceId())
                            .inspectContainerCmd(item.getContainerId())
                            .exec();
                    String status = exec.getState().getStatus();
                    Long exitCodeLong = Optional.ofNullable(exec.getState().getExitCodeLong()).orElse(0L);
                    String exitCode = "exited(" + exitCodeLong.intValue() + ")";
                    log.info("定时更新任务状态 ContainerStatus: {} ContainerExitCode: {}", status, exitCode);
                    // 更新任务和容器状态
                    this.updateTaskAndContainerStatus(item, status, exitCode);
                } catch (Exception e) {
                    log.error("获取容器状态失败 containerId: {}", item.getContainerId(), e);
                }
            });
        }
    }

    /**
     * @param item     数据项
     * @param status   容器状态
     * @param exitCode 状态码
     */
    private void updateTaskAndContainerStatus(SchTaskInfo item, String status, String exitCode) {
        LambdaUpdateWrapper<SchTaskInfo> taskUpdateWrapper = new LambdaUpdateWrapper<SchTaskInfo>()
                .eq(SchTaskInfo::getId, item.getId())
                .set(SchTaskInfo::getExitCode, exitCode)
                .set(SchTaskInfo::getContainerStatus, status)
                .set(SchTaskInfo::getUpdateTime, new Date());

        LambdaUpdateWrapper<SchContainerManager> containerUpdateWrapper = new LambdaUpdateWrapper<SchContainerManager>()
                .eq(SchContainerManager::getId, item.getContainerMangerId())
                .set(SchContainerManager::getContainerStatus, status)
                .set(SchContainerManager::getUpdateTime, new Date());

        if ("running".equals(status) || "created".equals(status) || "restarting".equals(status)) {
            log.info("更新任务状态为  running  当前 containerStatus: {}",status);
            taskUpdateWrapper.set(SchTaskInfo::getStatus, "running");
            containerUpdateWrapper.set(SchContainerManager::getContainerStatus, "running");
        } else if ("paused".equals(status)) {
            log.info("更新任务状态为  stop 当前 containerStatus: {}",status);
            taskUpdateWrapper.set(SchTaskInfo::getStatus, "stop");
            containerUpdateWrapper.set(SchContainerManager::getContainerStatus, "stop");
        } else if ("dead".equals(status)) {
            log.info("更新任务状态为  finished 当前 containerStatus: {}",status);
            taskUpdateWrapper.set(SchTaskInfo::getStatus, "failed");
            containerUpdateWrapper.set(SchContainerManager::getContainerStatus, "failed");
        }
        if("exited(0)".equals(exitCode)){
            log.info("更新任务状态为  finished 当前 containerStatus: {}",status);
            taskUpdateWrapper.set(SchTaskInfo::getStatus, "finished");
            taskUpdateWrapper.set(SchTaskInfo::getExitCode, exitCode);
        }else {
            log.info("更新任务状态为   当前 containerStatus: {}",status);
            taskUpdateWrapper.set(SchTaskInfo::getStatus, "failed");
            taskUpdateWrapper.set(SchTaskInfo::getExitCode, exitCode);
        }
        schTaskInfoMapper.update(taskUpdateWrapper);
        schContainerManagerMapper.update(containerUpdateWrapper);
    }

    /**
     * 扩缩容正在执行的容器任务
     *
     * @param schTaskInfos 正在执行的仁列表
     */
    @Override
    public Map<String, List<SchTaskInfo>> expandDecreaseMonitor(List<SchTaskInfo> schTaskInfos) {
        // 循环 正在执行的容器任务
        // 记录缩容扩容队列信息
        List<SchTaskInfo> expandQueue = new ArrayList<>();
        List<SchTaskInfo> decreaseQueue = new ArrayList<>();
        schTaskInfos.forEach(item -> {
            Long scalePlanId = item.getScalePlanId();
            //  查询CPU   内存信息 使用率 去匹配对应的扩缩容计划
            SchScalePlan plan = schScalePlanMapper.selectById(scalePlanId);
            //  获取当前任务的监控数据
            SchTaskMonitoring monitor = schTaskMonitoringMapper
                    .selectOne(new LambdaQueryWrapper<SchTaskMonitoring>()
                            .eq(SchTaskMonitoring::getTaskId, item.getId())
                            .orderByDesc(SchTaskMonitoring::getCreateTime)
                            .last("limit 1")
                    );
            boolean resultCEx = monitor.getCpuUsage().compareTo(new BigDecimal(plan.getCpuThresholdEx())) > 0;
            boolean resultMEx = monitor.getMemRate().compareTo(new BigDecimal(plan.getMemoryThresholdEx())) > 0;
            if (resultCEx && resultMEx) {
                SchScalePlan schScalePlan = schScalePlanMapper.selectById(item.getScalePlanId());
                item.setScalePlan(schScalePlan);
                expandQueue.add(item);
            }
            // 缩容队列
            boolean resultCDc = monitor.getCpuUsage().compareTo(new BigDecimal(plan.getCpuThresholdDc())) < 0;
            boolean resultMDC = monitor.getMemRate().compareTo(new BigDecimal(plan.getMemoryThresholdDc())) < 0;
            if (resultCDc && resultMDC) {
                SchScalePlan schScalePlan = schScalePlanMapper.selectById(item.getScalePlanId());
                item.setScalePlan(schScalePlan);
                decreaseQueue.add(item);
            }
        });
        Map<String, List<SchTaskInfo>> map = new HashMap<>();
        map.put("expandQueue", expandQueue);
        map.put("decreaseQueue", decreaseQueue);
        return map;
    }


    /**
     * 构建可用资源VNPU 资源信息
     *
     * @param map
     */
    public void buildAvailableResource(List<SchTaskInfo> map) {
        for (SchTaskInfo item : map) {
            SchScalePlan scalePlan = item.getScalePlan();
            JSONObject jsonObject = JSON.parseObject(scalePlan.getResourceConfig());
            String poolId = jsonObject.getString("poolId");
            String resourceInfoId = jsonObject.getString("resourceInfoId");
            String computeDeviceId = jsonObject.getString("computeDeviceId");
            String vccId = jsonObject.getString("vccId");
            // 选卡删除自己在运行的卡
            List<SchTaskInfo> verify = schTaskInfoMapper.usageQuery(item.getId(), computeDeviceId, vccId);

            if (verify.isEmpty()) {
                //构建模板镜像信息执行
                List<SchContainerManager> container = schContainerManagerMapper
                        .selectList(new LambdaQueryWrapper<SchContainerManager>()
                                .eq(SchContainerManager::getTaskInfoId, item.getId()));
                this.stopTask(item.getId());
                SchScalePlan schScalePlan = schScalePlanMapper.selectById(item.getScalePlanId());
                //composeGenerate.buildRemoteCommonTemplate(List.of(container.get(Integer.parseInt(vccId))),item,resource);

            } else {
                List<Integer> resource = this.availableResource(Math.toIntExact(scalePlan.getExpandMemoryNum()));

            }
        }
    }

    /**
     * 构建空闲可用VNPU 信息列表
     *
     * @param graphicSize 需要显存大小
     * @return 返回可用信息列表
     */
    public List<Integer> availableResource(Integer graphicSize) {
        List<SchComputeDevice> devices = schComputeDeviceMapper.selectList(
                new LambdaQueryWrapper<SchComputeDevice>()
                        .ge(SchComputeDevice::getMemorySize, graphicSize
                        ));
        List<Long> computerId = devices.stream().map(SchComputeDevice::getId).filter(Objects::nonNull).toList();
        List<SchTaskInfo> schTaskInfos = schTaskInfoMapper.selectList(
                new LambdaQueryWrapper<SchTaskInfo>()
                        .eq(SchTaskInfo::getStatus, "running")
                        .eq(SchTaskInfo::getContainerStatus, "running")
                        .in(SchTaskInfo::getComputeDeviceId, computerId)
        );
        if (schTaskInfos.isEmpty()) {
            throw new MyRuntimeException("没有可运行的任务");
        }
        List<Long> runCom = schTaskInfos.stream().map(SchTaskInfo::getComputeDeviceId).filter(Objects::nonNull).toList();
        List<Long> runPartition = schTaskInfos.stream().map(SchTaskInfo::getPartitionId).filter(Objects::nonNull).toList();
        return schTaskInfoMapper.queryAvaliable(runCom, runPartition);
    }

    /**
     * 远程任务调度
     *
     * @param remoteTask 任务调度
     */
  /*  @Transactional
    @Override
    public RemoteTaskVo remoteTask(RemoteTask remoteTask) {
        Long taskId = 0L;
        Long imageId = 0L;
        List<String> ports = new ArrayList<>();
        if (!"non".equals(remoteTask.getNeedResource())) {
            log.info("执行需要卡任务.....");
            if (remoteTask.getGraphicSize() == null || !remoteTask.getGraphicSize().matches("\\d+")) {
                throw new IllegalArgumentException("显存大小必须为正整数");
            }
            // 1. 收集可用任务资源
            List<SchComputeDevice> devices = schComputeDeviceMapper.selectList(
                    new LambdaQueryWrapper<SchComputeDevice>()
                            .ge(SchComputeDevice::getMemorySize, Integer.valueOf(remoteTask.getGraphicSize())
                            ));
            if (devices.isEmpty()) {
                throw new MyRuntimeException("没有满足条件的资源");
            }
            //1.1 提取ID
            List<Long> computerId = devices.stream().map(SchComputeDevice::getId).filter(Objects::nonNull).toList();
            // 2 收集正在运行任务   任务态 running  容器态 running restarting paused created
            List<Long> runCard = schTaskInfoMapper.collectRunTask(computerId);
            // 2.2 提取可用vnpu id
            List<Integer> available = schComputeDeviceMapper.queryAvailableVnpu(runCard);
            // 2.1 todo  收集可以释放的资源 exited(0) 运行中的也是零  exited(非零)
            //schTaskInfoMapper.extractResource();
            if (available.isEmpty()) {
                throw new MyRuntimeException("没有可用的算力卡资源");
            }
            Integer item = available.get(new Random().nextInt(available.size()));
            // todo 卡关系有误
            SchResourceInfo info = schResourceInfoMapper.queryAvaliable(item);
            SchComputeDevice cd = schComputeDeviceMapper.queryDevice(item, info.getId());
            // TODO 资源池多对多问题
            List<Long> pool = schResourceInfoMapper.queryPoolId(info.getId());
            Long poolID;
            if (!pool.isEmpty()) {
                poolID = pool.get(new Random().nextInt(pool.size()));
            } else {
                poolID = 0L;
            }
            if (remoteTask.getMultiplePort() == Boolean.TRUE) {
                for (int i = 0; i < remoteTask.getNeedPortNum(); i++) {
                    ports.add(String.valueOf(sshConnectionUtil.getFreePort(info.getHostIp())));
                }
            } else {
                String hostPort = String.valueOf(sshConnectionUtil.getFreePort(info.getHostIp()));
                ports.add(hostPort);
            }
            //  传输端口配置
            remoteTask.setExport(ports);
            if("1".equals(remoteTask.getOpenUrl())){
                // TODO list port 40050
                remoteTask.setVisitLink(linkUrl+ IdUtil.simpleUUID());
            }
            List<SchBusinessDict> dict = schBusinessDictMapper.selectList(null);
            Integer graphicSize = remoteTask.getGraphicSize() != null ? Integer.parseInt(remoteTask.getGraphicSize()) : 0;
            Integer mem = remoteTask.getMemorySize() != null ? Integer.valueOf(remoteTask.getMemorySize()) : null;
            String env = remoteTask.getEnvironment() != null ? JSON.toJSONString(remoteTask.getEnvironment()) : null;
            String command = remoteTask.getCommand() != null ? JSON.toJSONString(remoteTask.getCommand()) : null;
            SchTaskInfo schTaskInfo = new SchTaskInfo();

            List<SchTaskImage> images = schTaskImageMapper.selectList(
                    new LambdaQueryWrapper<SchTaskImage>()
                            .and(wrapper -> wrapper
                                    .eq(SchTaskImage::getImageName, remoteTask.getImageName())
                                    .or(remoteTask.getImageVersion() != null,
                                            i -> i.eq(SchTaskImage::getImageName, remoteTask.getImageName())
                                                    .eq(SchTaskImage::getImageVersion, remoteTask.getImageVersion()))
                            )
            );
            if (images.isEmpty()) {
                //  不存在则创建镜像
                SchTaskImage schTaskImage = new SchTaskImage()
                        .setId(idGenerator.nextLongId())
                        .setImageName(remoteTask.getImageName())
                        .setCreateTime(new Date())
                        .setUpdateTime(new Date())
                        .setIsDelete(GlobalDeletedFlag.NORMAL);
                schTaskImageMapper.insert(schTaskImage);
                imageId = schTaskImage.getId();
            } else {
                imageId = images.get(0).getId();
            }
            // 创建任务数据
            schTaskInfo.setId(idGenerator.nextLongId())
                    .setUpdateTime(new Date())
                    .setCreateTime(new Date())
                    .setGraphicNeededMb(graphicSize)
                    .setMemoryNeededMb(mem)
                    .setRunCommand(command)
                    .setEnvConfig(env)
                    .setApproveState("approved")
                    .setStatus("starting")
                    .setTaskName(remoteTask.getTaskName())
                    .setComputeDeviceId(cd.getId())
                    .setPoolId(poolID)
                    .setIsDelete(GlobalDeletedFlag.NORMAL)
                    .setPartitionId(Long.valueOf(item))
                    .setDictId(1933758313216872448L)
                    //.setDictId(dict.get(new Random().nextInt(dict.size())).getId())
                    .setResourceId(info.getId());
            schTaskInfo.setTaskImageId(imageId);
            schTaskInfoMapper.insert(schTaskInfo);
            taskId = schTaskInfo.getId();
            if (StringUtils.isEmpty(remoteTask.getContainerMapPath())) {
                remoteTask.setContainerMapPath(mountPath + "/" + schTaskInfo.getId());
            }
            CompletableFuture.runAsync(
                    () -> {
                        byte[] bytes = composeGenerate.buildRemoteTaskTemplate(
                                List.of(item),
                                remoteTask
                                , ports
                                , Map.of("taskId", schTaskInfo.getId())
                        );
                        try {
                            composeGenerate.remoteTask(info,  schTaskInfo.getId(), bytes,remoteTask);
                        } catch (IOException e) {
                            log.error("任务执行失败");
                            // 失败更新任务树
                            schTaskInfoMapper.update(new LambdaUpdateWrapper<SchTaskInfo>()
                                    .eq(SchTaskInfo::getId, schTaskInfo.getId())
                                    .set(SchTaskInfo::getUpdateTime, new Date())
                                    .set(SchTaskInfo::getFailReason, e.getMessage())
                                    .set(SchTaskInfo::getStatus, "failed"));

                            throw new MyRuntimeException(e);
                        }

                    }, poolExecutor);
        } else {
            log.info("执行不需要卡任务.....");
            List<SchResourceInfo> infos = schResourceInfoMapper.selectList(null);
            if (infos.isEmpty()) {
                throw new MyRuntimeException("没有可用服务器资源");
            }
            int item = new Random().nextInt(infos.size());
            Long resourceId = infos.get(item).getId();
            SchResourceInfo r = infos.get(item);
            List<Long> pool = schResourceInfoMapper.queryPoolId(resourceId);
            Long poolID;
            if (!pool.isEmpty()) {
                poolID = pool.get(new Random().nextInt(pool.size()));
            } else {
                poolID = 0L;
            }
            if (remoteTask.getMultiplePort() == Boolean.TRUE) {
                for (int i = 0; i < remoteTask.getNeedPortNum(); i++) {
                    ports.add(String.valueOf(sshConnectionUtil.getFreePort(r.getHostIp())));
                }
            } else {
                String hostPort = String.valueOf(sshConnectionUtil.getFreePort(r.getHostIp()));
                ports.add(hostPort);
            }
            remoteTask.setExport(ports);
            if("1".equals(remoteTask.getOpenUrl())){
                // TODO list port 40050
                remoteTask.setVisitLink(linkUrl+ IdUtil.simpleUUID());
            }
            Integer graphicSize = remoteTask.getGraphicSize() != null ? Integer.parseInt(remoteTask.getGraphicSize()) : 0;
            Integer mem = remoteTask.getMemorySize() != null ? Integer.valueOf(remoteTask.getMemorySize()) : null;
            String env = remoteTask.getEnvironment() != null ? JSON.toJSONString(remoteTask.getEnvironment()) : null;
            String command = remoteTask.getCommand() != null ? JSON.toJSONString(remoteTask.getCommand()) : null;
            List<SchTaskImage> images = schTaskImageMapper.selectList(
                    new LambdaQueryWrapper<SchTaskImage>()
                            .and(wrapper -> wrapper
                                    .eq(SchTaskImage::getImageName, remoteTask.getImageName())
                                    .or(remoteTask.getImageVersion() != null,
                                            i -> i.eq(SchTaskImage::getImageName, remoteTask.getImageName())
                                                    .eq(SchTaskImage::getImageVersion, remoteTask.getImageVersion()))
                            )
            );
            if (images.isEmpty()) {
                SchTaskImage schTaskImage = new SchTaskImage()
                        .setId(idGenerator.nextLongId())
                        .setImageName(remoteTask.getImageName())
                        .setCreateTime(new Date())
                        .setUpdateTime(new Date())
                        .setIsDelete(GlobalDeletedFlag.NORMAL);
                schTaskImageMapper.insert(schTaskImage);
                imageId = schTaskImage.getId();
            } else {
                imageId = images.get(0).getId();
            }
            SchTaskInfo schTaskInfo = new SchTaskInfo()
                    .setId(idGenerator.nextLongId())
                    .setUpdateTime(new Date())
                    .setCreateTime(new Date())
                    .setGraphicNeededMb(graphicSize)
                    .setMemoryNeededMb(mem)
                    .setRunCommand(command)
                    .setEnvConfig(env)
                    .setApproveState("approved")
                    .setStatus("starting")
                    .setTaskName(remoteTask.getTaskName())
                    .setComputeDeviceId(null)
                    .setPoolId(poolID)
                    // .setDictId(dict.get(new Random().nextInt(dict.size())).getId())
                    .setDictId(1933758313216872448L)
                    .setIsDelete(GlobalDeletedFlag.NORMAL)
                    .setPartitionId(null)
                    .setResourceId(r.getId());
            schTaskInfo.setTaskImageId(imageId);
            taskId = schTaskInfo.getId();
            if (StringUtils.isEmpty(remoteTask.getContainerMapPath())) {
                remoteTask.setContainerMapPath(mountPath + "/" + schTaskInfo.getId());
            }

            CompletableFuture.runAsync(
                    () -> {

                        schTaskInfoMapper.insert(schTaskInfo);
                        byte[] bytes = composeGenerate.buildRemoteTaskTemplate(
                                List.of(item),
                                remoteTask
                                , ports
                                , Map.of("taskId", schTaskInfo.getId())
                        );
                        try {
                            composeGenerate.remoteTask(r,schTaskInfo.getId(), bytes,remoteTask);
                        } catch (Exception e) {
                            log.error("TaskServiceImpl remote  method  execute fail {}  ", e.getMessage());
                            log.error(String.valueOf(e));
                            // 失败更新任务树
                            schTaskInfoMapper.update(new LambdaUpdateWrapper<SchTaskInfo>()
                                    .eq(SchTaskInfo::getId, schTaskInfo.getId())
                                    .set(SchTaskInfo::getUpdateTime, new Date())
                                    .set(SchTaskInfo::getFailReason, e.getMessage())
                                    .set(SchTaskInfo::getStatus, "failed"));
                        }
                    }, poolExecutor);
        }

        return new RemoteTaskVo()
                .setTaskName(remoteTask.getTaskName())
                .setPorts(ports)
                .setTaskId(taskId.toString())
                .setImageName(remoteTask.getImageName())
                .setStatus("starting")
                .setVisitLink(remoteTask.getVisitLink())
                .setMapPath(remoteTask.getContainerMapPath());

    }

*/


    /**
     *   执行docker-compose up -d
     * @param remoteTask 任务调度
     * @return 任务信息返回结果
     */
    public RemoteTaskVo remoteTask(RemoteTask remoteTask) {
        // 验证基本参数
         this.validateRemoteTask(remoteTask);
        // 准备资源信息
        ResourceAllocation allocation = this.allocateResources(remoteTask);
        // 创建或获取镜像记录
        Long imageId = this.getOrCreateTaskImage(remoteTask);
        // 创建任务记录
        SchTaskInfo taskInfo = this.createTaskInfo(remoteTask, allocation, imageId);
        // 设置挂载路径
        if (StringUtils.isEmpty(remoteTask.getContainerMapPath())) {
            remoteTask.setContainerMapPath(mountPath + "/" + taskInfo.getId());
        }
        // 异步执行任务
         this.executeTaskAsync(remoteTask, taskInfo, allocation);
        // 返回任务信息

        String visitLink = remoteTask.getVisitLink()!=null?
                remoteTask.getVisitLink()+taskInfo.getId()+"/lab?token="+ remoteTask.getToken()
                :"";
        return new RemoteTaskVo()
                .setTaskName(remoteTask.getTaskName())
                .setPorts(allocation.getPorts())
                .setTaskId(taskInfo.getId().toString())
                .setImageName(remoteTask.getImageName())
                .setStatus("starting")
                .setVisitLink(visitLink)
                .setMapPath(remoteTask.getContainerMapPath());
    }

    /**
     *   参数验证
     * @param remoteTask 远程任务信息
     */
    private void validateRemoteTask(RemoteTask remoteTask) {
        if (!"non".equals(remoteTask.getNeedResource()) &&
                (remoteTask.getGraphicSize() == null || !remoteTask.getGraphicSize().matches("\\d+"))) {
            throw new IllegalArgumentException("显存大小必须为正整数");
        }
    }

    /**
     *  构建不同任务算力分配  non 不需要算力资源  physical 物理卡资源 vnpu 代表需要npu 资源
     * @param remoteTask 任务信息列表
     * @return  资源分配结果
     */
    private ResourceAllocation allocateResources(RemoteTask remoteTask) {
        ResourceAllocation allocation = new ResourceAllocation();
        if (!"non".equals(remoteTask.getNeedResource())) {
            // 需要GPU/NPU资源的情况
            this.allocateComputeResources(remoteTask, allocation);
        } else {
            // 不需要特殊资源的情况
            this.allocateBasicResources(remoteTask, allocation);
        }
        // 需要构建 jupyter 访问链接
        if ("1".equals(remoteTask.getOpenUrl())) {
            remoteTask.setVisitLink(linkUrl);
            remoteTask.setToken(IdUtil.fastSimpleUUID());
        }
        // 设置端口信息
        remoteTask.setExport(allocation.getPorts());
        return allocation;
    }


    /**
     *   避免重复创建 镜像
     * @param remoteTask 任务基础信息
     * @return imageId
     */
    private Long getOrCreateTaskImage(RemoteTask remoteTask) {
        List<SchTaskImage> images = schTaskImageMapper.selectList(
                new LambdaQueryWrapper<SchTaskImage>()
                        .and(
                                wrapper -> wrapper
                                .eq(SchTaskImage::getImageName, remoteTask.getImageName())
                                .or(remoteTask.getImageVersion() != null,
                                        i ->
                                                i.eq(SchTaskImage::getImageName, remoteTask.getImageName())
                                                .eq(SchTaskImage::getImageVersion, remoteTask.getImageVersion()))
                        )
        );
        if (images.isEmpty()) {
            SchTaskImage schTaskImage = new SchTaskImage()
                    .setId(idGenerator.nextLongId())
                    .setImageVersion(remoteTask.getImageVersion())
                    .setImageName(remoteTask.getImageName())
                    .setCreateTime(new Date())
                    .setUpdateTime(new Date())
                    .setIsDelete(GlobalDeletedFlag.NORMAL);
            schTaskImageMapper.insert(schTaskImage);
            return schTaskImage.getId();
        } else {
            return images.get(0).getId();
        }
    }

    /**
     * 创建存储任务信息
     * @param remoteTask 构建任务信息传输对象
     * @param allocation 资源分配解果
     * @param imageId 镜像id
     * @return 构建任务信息
     */
    private SchTaskInfo createTaskInfo(RemoteTask remoteTask, ResourceAllocation allocation, Long imageId) {
        Integer graphicSize = remoteTask.getGraphicSize() != null ? Integer.parseInt(remoteTask.getGraphicSize()) : 0;
        Integer mem = remoteTask.getMemorySize() != null ? Integer.valueOf(remoteTask.getMemorySize()) : null;
        String env = remoteTask.getEnvironment() != null ? JSON.toJSONString(remoteTask.getEnvironment()) : null;
        String command = remoteTask.getCommand() != null ? JSON.toJSONString(remoteTask.getCommand()) : null;
        SchTaskInfo taskInfo = new SchTaskInfo()
                .setId(idGenerator.nextLongId())
                .setUpdateTime(new Date())
                .setCreateTime(new Date())
                .setGraphicNeededMb(graphicSize)
                .setMemoryNeededMb(mem)
                .setRunCommand(command)
                .setEnvConfig(env)
                .setApproveState("approved")
                .setStatus("starting")
                .setTaskName(remoteTask.getTaskName())
                .setComputeDeviceId(allocation.getComputeDevice() != null ? allocation.getComputeDevice().getId() : null)
                .setPoolId(allocation.getPoolId())
                .setDictId(1933758313216872448L)
                .setIsDelete(GlobalDeletedFlag.NORMAL)
                .setPartitionId(allocation.getPartitionId() != null ? Long.valueOf(allocation.getPartitionId()) : null)
                .setResourceId(allocation.getResourceInfo().getId())
                .setTaskImageId(imageId);

        schTaskInfoMapper.insert(taskInfo);
        return taskInfo;
    }

    /**
     *   异步执行任务
     * @param remoteTask 远程任务信息
     * @param taskInfo 当前存储的数据库任务信息
     * @param allocation   当前分配资源
     */
    private void executeTaskAsync(RemoteTask remoteTask, SchTaskInfo taskInfo, ResourceAllocation allocation) {
        CompletableFuture.runAsync(() -> {
            try {
                byte[] bytes = composeGenerate.buildRemoteTaskTemplate(
                        allocation.getPartitionId() != null ? List.of(allocation.getPartitionId()) : List.of(0),
                        remoteTask,
                        allocation.getPorts(),
                        Map.of("taskId", taskInfo.getId())
                );
                composeGenerate.remoteTask(allocation.getResourceInfo(), taskInfo.getId(), bytes, remoteTask);
            } catch (Exception e) {
                log.error("任务执行失败: {}", e.getMessage(), e);
                schTaskInfoMapper.update(new LambdaUpdateWrapper<SchTaskInfo>()
                        .eq(SchTaskInfo::getId, taskInfo.getId())
                        .set(SchTaskInfo::getUpdateTime, new Date())
                        .set(SchTaskInfo::getFailReason, e.getMessage())
                        .set(SchTaskInfo::getStatus, "failed"));
            }
        }, poolExecutor);
    }


    /**
     *  执行资源分配 需要VNPU GPU  需要获取vccid 才能执行
     * @param remoteTask  任务信息传输对象
     * @param allocation 分配资源信息对象
     */
    private void allocateComputeResources(RemoteTask remoteTask, ResourceAllocation allocation) {
        log.info("SchTaskInfoService  allocateComputeResources  method  execute start: current task need compute resource ");
        int graphicSizeValue = Integer.parseInt(remoteTask.getGraphicSize());
        // 查找满足显存要求的计算设备
        List<SchComputeDevice> devices = schComputeDeviceMapper.selectList(
                new LambdaQueryWrapper<SchComputeDevice>()
                        .ge(SchComputeDevice::getMemorySize, graphicSizeValue)
        );
        if (devices.isEmpty()) {
            throw new MyRuntimeException("没有满足条件的资源");
        }
        // 提取计算设备ID
        List<Long> computerId = devices.stream().map(SchComputeDevice::getId).filter(Objects::nonNull).toList();
        // 收集正在运行任务的卡  任务态 running  容器态 running restarting paused created
        List<Long> runCard = schTaskInfoMapper.collectRunTask(computerId);
        // 提取可用VNPU ID
        List<Integer> available = schComputeDeviceMapper.queryAvailableVnpu(runCard);
        if (available.isEmpty()) {
            throw new MyRuntimeException("没有可用的算力卡资源");
        }
        // 随机选择一个可用VNPU
        Integer partitionId = available.get(new Random().nextInt(available.size()));
        allocation.setPartitionId(partitionId);
        // 获取资源信息和计算设备
        SchResourceInfo resourceInfo = schResourceInfoMapper.queryAvaliable(partitionId);
        SchComputeDevice computeDevice = schComputeDeviceMapper.queryDevice(partitionId, resourceInfo.getId());
        allocation.setResourceInfo(resourceInfo);
        allocation.setComputeDevice(computeDevice);
        // 获取资源池ID
        List<Long> poolIds = schResourceInfoMapper.queryPoolId(resourceInfo.getId());
        allocation.setPoolId(poolIds.isEmpty() ? 0L : poolIds.get(new Random().nextInt(poolIds.size())));
        // 分配端口
        this.allocatePorts(remoteTask, allocation);
    }


    /**
     * 分配基本资源(不需要GPU/NPU) 只需要服务器空闲即可
     * @param remoteTask 远程调用任务数据对象
     * @param allocation 分配资源信息传输对象
     */
    private void allocateBasicResources(RemoteTask remoteTask, ResourceAllocation allocation) {
        log.info("SchTaskInfoService  allocateBasicResources  method  execute start: current task don't need compute resource ");
        // 获取可用服务器资源
        List<SchResourceInfo> infos = schResourceInfoMapper.selectList(null);
        if (infos.isEmpty()) {
            throw new MyRuntimeException("没有可用服务器资源");
        }
        // 随机选择一个服务器
        int index = new Random().nextInt(infos.size());
        SchResourceInfo resourceInfo = infos.get(index);
        allocation.setResourceInfo(resourceInfo);
        // 获取资源池ID
        List<Long> poolIds = schResourceInfoMapper.queryPoolId(resourceInfo.getId());
        allocation.setPoolId(poolIds.isEmpty() ? 0L : poolIds.get(new Random().nextInt(poolIds.size())));
        // 分配端口
        this.allocatePorts(remoteTask, allocation);
    }

    /**
     * 执行端口分配
     * @param remoteTask 远程任务数据对象
     * @param allocation 资源分配传输对象
     */
    private void allocatePorts(RemoteTask remoteTask, ResourceAllocation allocation) {
        List<String> ports = new ArrayList<>();
        String hostIp = allocation.getResourceInfo().getHostIp();
        if (Boolean.TRUE.equals(remoteTask.getMultiplePort())) {
            // 分配多个端口
            for (int i = 0; i < remoteTask.getNeedPortNum(); i++) {
                ports.add(String.valueOf(sshConnectionUtil.getFreePort(hostIp)));
            }
        } else {
            // 分配单个端口
            ports.add(String.valueOf(sshConnectionUtil.getFreePort(hostIp)));
        }
        allocation.setPorts(ports);
        remoteTask.setExport(ports);//携带传输
    }


    /**
     *  构建nginx config配置文件
     * @param resourceId 资源id
     * @param taskId 信息列表
     * @param export 存储信息
     * @throws JSchException
     */
    @Override
    public void nginxProxy(Long resourceId, Long taskId, List<String> export) throws JSchException, IOException {
        log.info("nginxProxy  build start");
       // SchResourceInfo info = schResourceInfoMapper.selectById(resourceId);
       // SshConnection connection = sshConnectionUtil.createConnection(info.getHostIp(), info.getPort(), info.getLoginName(), info.getPassword(), 5000);
         export.forEach(
                 port -> {
                     try {
                         this.buildMultiNginxProxy("127.78.86",port,taskId);
                     } catch (IOException e) {
                         log.error("nginx 配置追加错误");
                     }
                 }
         );
        //sshConnectionUtil.executeCommand(connection, "docker exec code_proxy_nginx nginx -s reload");

    }

    /**
     *  多端口映射配置
     * @param  ip 服务器ip
     * @param port 暴露端口
     * @param taskId 任务id
     */
    public void buildMultiNginxProxy(String ip, String port, Long taskId) throws IOException {
        log.info("读取nginx配置文件构建....nginx.conf");
        //Path path = Paths.get(nginxConfig);
        String str = "D:\\gy_sch_be\\application-webadmin\\src\\main\\resources\\templates\\remotetask\\nginx.conf";
        Path path = Paths.get(nginxConfig);
        List<String> lines = new ArrayList<>();
        try (BufferedReader reader = Files.newBufferedReader(path)) {
            String line;
            while ((line = reader.readLine()) != null) {
                lines.add(line);
            }
        }
        
        // 构建location块配置
        StringBuilder nginxProxy = new StringBuilder();
        // String codePrefix = "code" + taskId;
        String containerName="test_container";
         String codePrefix = "code" + "124455767645645";
        nginxProxy.append(System.lineSeparator())
                .append("location  ~ ^/").append(codePrefix).append("/ {")
                .append(System.lineSeparator())
                .append(System.lineSeparator())
                .append("proxy_pass http://").append(containerName)// 容器名
                .append(":").append(port).append(";")
                .append(System.lineSeparator())
                .append("rewrite ^/").append(codePrefix).append("(/.*)$ $1 break;") // 修正rewrite规则
                .append(System.lineSeparator())
                .append("access_log /var/log/nginx/codeserver_access.log main;")
                .append(System.lineSeparator())
                .append("error_log /var/log/nginx/codeserver_error.log debug;")
                .append(System.lineSeparator())
                .append("}")
                .append(System.lineSeparator());
        
        if (lines.size() > 1) {
            lines.add(lines.size() - 2, String.valueOf(nginxProxy));
        } else {
            throw new IOException("Nginx配置文件格式不正确");
        }
        
        try (BufferedWriter writer = Files.newBufferedWriter(path, StandardOpenOption.TRUNCATE_EXISTING)) {
            for (String l : lines) {
                writer.write(l);
                writer.newLine();
            }
        }
    }


    /**
     * 运行任务批量停止
     *
     * @param idList 运行型任务列表
     * @return 任务信息列表
     */
    @Override
    public List<SchTaskInfo> batchStop(List<String> idList) {
        List<Long> id = idList.stream().filter(i -> !StringUtils.isEmpty(i)).map(Long::valueOf).toList();
        if (id.isEmpty()) {
            throw new MyRuntimeException("任务不存在");
        }
        List<SchTaskInfo> queryR = schTaskInfoMapper.selectList(
                new LambdaQueryWrapper<SchTaskInfo>()
                        .in(SchTaskInfo::getId, id)
                        .eq(SchTaskInfo::getApproveState, "approved")
                        .eq(SchTaskInfo::getStatus, "running")
        );
        if (queryR.isEmpty()) {
            throw new MyRuntimeException("任务不存在");
        }
        queryR.forEach(item -> {
            schTaskInfoMapper.update(new LambdaUpdateWrapper<SchTaskInfo>()
                    .eq(SchTaskInfo::getId, item.getId())
                    .set(SchTaskInfo::getStatus, "stopping")
                    .set(SchTaskInfo::getUpdateTime, new Date()));
        });
        CompletableFuture.runAsync(() -> {
            queryR.forEach(item -> {
                try {
                    //TODO 任务执行失败状态回退问题
                    this.stopTask(item.getId());
                } catch (Exception e) {
                    log.warn("任务停止失败");
                }
            });
        }, poolExecutor);
        return List.of();
    }

    /**
     * 重启失败任务
     *
     * @param taskId 任务id
     */
    @Override
    public SchTaskInfo restartTask(Long taskId) {
        try {
            SchTaskInfo schTaskInfo = schTaskInfoMapper.selectById(taskId);
            if (StringUtils.isEmpty(schTaskInfo.getContainerName().trim())) {
                this.startTask(schTaskInfo.getId());
            }
            SchResourceInfo schResourceInfo = schResourceInfoMapper.selectById(
                    Objects.requireNonNull(schTaskInfo.getResourceId(), "当前任务已经失效")
            );
            SshConnection connection = sshConnectionUtil.createConnection(schResourceInfo.getHostIp(), schResourceInfo.getPort(),
                    schResourceInfo.getLoginName(), schResourceInfo.getPassword(), 30000);
            String command = sshConnectionUtil.executeCommand(connection, "docker restart " + Objects.requireNonNull(schTaskInfo.getContainerName(), "当前任务不存在"));
            if (!schTaskInfo.getContainerName().equals(command.trim())) {
                //  重启失败 之前状态保留
                throw new MyRuntimeException("任务重启失败");
            }
            String statusCommand = "docker inspect --format='{{.State.Status}}' " + command.trim();
            String result = sshConnectionUtil.executeCommand(connection, statusCommand);
            if (result.trim().equals("running")) {
                schTaskInfoMapper.update(new LambdaUpdateWrapper<SchTaskInfo>()
                        .eq(SchTaskInfo::getId, taskId)
                        .set(SchTaskInfo::getUpdateTime, new Date())
                        .set(SchTaskInfo::getStatus, "running")
                        .set(SchTaskInfo::getContainerStatus, "running"));
                return schTaskInfoMapper.selectById(taskId);
            } else {
                throw new MyRuntimeException("任务重启失败");
            }
        } catch (JSchException | IOException e) {
            log.error("重启失败", e);
            throw new MyRuntimeException("任务重启失败");
        }
    }

    /**
     * 批量启动任务
     *
     * @param idList 任务列表 status
     * @return 启动数据结果
     */
    @Override
    public List<SchTaskInfo> batchStart(List<String> idList) {
        List<Long> id = idList.stream().filter(i -> !StringUtils.isEmpty(i)).map(Long::valueOf).toList();
        if (id.isEmpty()) {
            throw new MyRuntimeException("任务不存在");
        }
        List<SchTaskInfo> queryR = schTaskInfoMapper.selectList(
                new LambdaQueryWrapper<SchTaskInfo>()
                        .in(SchTaskInfo::getId, id)
                        .eq(SchTaskInfo::getApproveState, "approved")
                        .eq(SchTaskInfo::getStatus, "pending")
        );
        if (queryR.isEmpty()) {
            throw new MyRuntimeException("任务不存在");
        }
        CompletableFuture.runAsync(() -> {
            // 批量循环执行任务启动
            queryR.forEach(item -> {
                try {
                    // 保证发生异常任务继续执行
                    this.startBatchTask(item.getId());
                } catch (Exception e) {
                    // 异常跟新当前任务启动状态
                    // 记录具体异常类型和详细信息
                    log.error("任务[{}]启动失败: {}", item.getId(), e.getMessage(), e);
                    schTaskInfoMapper.update(new LambdaUpdateWrapper<SchTaskInfo>()
                            .eq(SchTaskInfo::getId, item.getId())
                            .set(SchTaskInfo::getUpdateTime, new Date())
                            .set(SchTaskInfo::getStatus, "failed"));

                }
            });
        }, poolExecutor);
        queryR.forEach(
                item -> schTaskInfoMapper.update(new LambdaUpdateWrapper<SchTaskInfo>()
                        .eq(SchTaskInfo::getId, item.getId())
                        .set(SchTaskInfo::getUpdateTime, new Date())
                        .set(SchTaskInfo::getStatus, "starting")
                )
        );
        return List.of();
    }

    public SchTaskInfo startBatchTask(Long taskId) {
        // 获取审核通过任务信息
        SchTaskInfo schTaskInfo = schResourceInfoMapper.approval(taskId);
        if (Objects.isNull(schTaskInfo)) {
            throw new MyRuntimeException("当前任务审核没有通过");
        }
        SchResourceInfo schResourceInfo = schResourceInfoMapper.selectById(schTaskInfo.getResourceId());
        Objects.requireNonNull(schResourceInfo, "当前任务对应的资源不存在");
        SchVirtualComputeCardSituation car = schVirtualComputeCardSituationMappers
                .selectById(Objects.requireNonNull(schTaskInfo.getPartitionId(), "切分卡信息不存在"));
        List<Integer> vnpuList = List.of(car.getVccId());
        if (vnpuList.isEmpty()) {
            vnpuList = schTaskInfoMapper.queryInfoVnpu(taskId);
        }
        try {
            // 压缩包类型 线解压缩上传构建 挂载数据
            return composeGenerate.ordinaryStart(schResourceInfo, vnpuList, schTaskInfo.getTaskImageId(), taskId);
        } catch (IOException e) {
            log.error("startTask: {}", e.getMessage());
            throw new MyRuntimeException(e);
        }
    }

    /**
     * 返回当前Service的主表Mapper对象。
     *
     * @return 主表Mapper对象。
     */
    @Override
    protected BaseDaoMapper<SchTaskInfo> mapper() {
        return schTaskInfoMapper;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SchTaskInfo saveNew(SchTaskInfo schTaskInfo) {
        if (StringUtils.isEmpty(schTaskInfo.getApproveState())) {
            schTaskInfo.setApproveState("pending");
        }
        if (StringUtils.isEmpty(schTaskInfo.getApproveState())) {
            schTaskInfo.setStatus("pending");
        }
        // 构建审核关系
        schTaskApprovalMapper.insert(
                new SchTaskApproval()
                        .setId(idGenerator.nextLongId())
                        .setTaskId(schTaskInfo.getId())
                        .setStatus("pending")
                        .setTaskId(schTaskInfo.getId())
                        .setCreateTime(new Date())
                        .setUpdateTime(new Date())
                        .setIsDelete(GlobalDeletedFlag.NORMAL)
                        .setCreateUserId(Objects.requireNonNull(TokenData.takeFromRequest()).getUserId())
                        .setUpdateUserId(Objects.requireNonNull(TokenData.takeFromRequest()).getUserId())
                        .setDataDeptId(Objects.requireNonNull(TokenData.takeFromRequest()).getDeptId())
                        .setDataUserId(Objects.requireNonNull(TokenData.takeFromRequest()).getUserId())
        );

        schTaskInfoMapper.insert(this.buildDefaultValue(schTaskInfo));
        return schTaskInfo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNewBatch(List<SchTaskInfo> schTaskInfoList) {
        if (CollUtil.isNotEmpty(schTaskInfoList)) {
            schTaskInfoList.forEach(this::buildDefaultValue);
            schTaskInfoMapper.insertList(schTaskInfoList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean update(SchTaskInfo schTaskInfo, SchTaskInfo originalSchTaskInfo) {
        // 这里重点提示，在执行主表数据更新之前，如果有哪些字段不支持修改操作，请用原有数据对象字段替换当前数据字段。
        UpdateWrapper<SchTaskInfo> uw = this.createUpdateQueryForNullValue(schTaskInfo, schTaskInfo.getId());
        return schTaskInfoMapper.update(schTaskInfo, uw) == 1;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean remove(Long id) {
        return schTaskInfoMapper.deleteById(id) == 1;
    }

    @Override
    public List<SchTaskInfo> getSchTaskInfoList(SchTaskInfo filter, String orderBy) {
        return schTaskInfoMapper.getSchTaskInfoList(filter, orderBy);
    }

    @Override
    public List<SchTaskInfo> getSchTaskInfoListWithRelation(SchTaskInfo filter, String orderBy) {
        List<SchTaskInfo> resultList = schTaskInfoMapper.getSchTaskInfoList(filter, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }


    /**
     * 启动任务 TODO目前写法针对一个节点
     *
     * @param taskId 任务表主键id
     * @return 启动结果数据
     */
    @Override
    public SchTaskInfo startTask(Long taskId) {
        // 获取审核通过任务信息
        SchTaskInfo schTaskInfo = schResourceInfoMapper.approval(taskId);
        if (Objects.isNull(schTaskInfo)) {
            throw new MyRuntimeException("当前任务审核没有通过");
        }
        SchTaskInfo verify = this.memoryQueue(schTaskInfo.getId(), schTaskInfo.getPartitionId());
        if (!Objects.isNull(verify)) {
            return verify;
        }
        SchResourceInfo schResourceInfo = schResourceInfoMapper.selectById(schTaskInfo.getResourceId());
        Objects.requireNonNull(schResourceInfo, "当前任务对应的资源不存在");
        SchVirtualComputeCardSituation car = schVirtualComputeCardSituationMappers
                .selectById(Objects.requireNonNull(schTaskInfo.getPartitionId(), "切分卡信息不存在"));
        List<Integer> vnpuList = List.of(car.getVccId());
        if (vnpuList.isEmpty()) {
            vnpuList = schTaskInfoMapper.queryInfoVnpu(taskId);
        }
        try {
            return composeGenerate.ordinaryStart(schResourceInfo, vnpuList, schTaskInfo.getTaskImageId(), taskId);
        } catch (IOException e) {
            log.error("startTask: {}", e.getMessage());
            throw new MyRuntimeException(e);
        }
    }

    /**
     * 抢占调度
     *
     * @param
     * @return 返回当前任务数据
     */
    @Override
    public SchTaskInfo priorityDispatch(SchTaskInfoDto schTaskInfoDto) {
        // 4. 获取资源信息
        SchTaskInfo schTaskInfo = schResourceInfoMapper.approval(schTaskInfoDto.getId());
        if (schTaskInfo == null) {
            throw new MyRuntimeException("当前任务审核未通过");
        }

        List<Long> occupyList = schTaskInfoDto.getOccupyList();
        if (occupyList == null || occupyList.isEmpty()) {
            throw new MyRuntimeException("抢占任务列表不能为空");
        }
        // 查询正在运行的任务
        List<SchTaskInfo> runningTasks = schTaskInfoMapper.selectList(
                new LambdaQueryWrapper<SchTaskInfo>()
                        .in(SchTaskInfo::getId, occupyList)
                        .eq(SchTaskInfo::getStatus, "running")
                        .in(SchTaskInfo::getContainerStatus, Arrays.asList("running", "exited"))
        );
        List<Long> taskIdList = runningTasks.stream()
                .map(SchTaskInfo::getId)
                .filter(Objects::nonNull)
                .toList();
        // 创建异步任务并获取Future对象以便后续处理
        CompletableFuture<SchTaskInfo> future = CompletableFuture.supplyAsync(() -> {
                    try {
                        // 1. 并行停止任务，确保一个失败不影响其他执行
                        List<CompletableFuture<Void>> futures = new ArrayList<>();
                        for (Long taskId : taskIdList) {
                            futures.add(CompletableFuture.runAsync(() -> {
                                try {
                                    this.stopTask(taskId);
                                    log.info("成功停止任务: {}", taskId);
                                } catch (Exception e) {
                                    log.error("停止任务失败, taskId: {}, 原因: {}", taskId, e.getMessage(), e);
                                }
                            }, poolExecutor)); // 使用线程池执行任务
                        }

                        // 2. 等待所有停止任务完成
                        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
                        log.info("所有需要抢占的任务已停止, 开始启动新任务: {}", schTaskInfoDto.getId());
                        // 3. 更新当前任务状态为启动中
                        schTaskInfoMapper.update(new LambdaUpdateWrapper<SchTaskInfo>()
                                .eq(SchTaskInfo::getId, schTaskInfoDto.getId())
                                .set(SchTaskInfo::getStatus, "starting")
                                .set(SchTaskInfo::getUpdateTime, new Date())
                                .set(SchTaskInfo::getUpdateUserId, Objects.requireNonNull(TokenData.takeFromRequest()).getUserId()));
                        SchResourceInfo schResourceInfo = schResourceInfoMapper.selectById(schTaskInfoDto.getResourceId());
                        if (schResourceInfo == null) {
                            throw new MyRuntimeException("任务关联的资源不存在");
                        }

                        // 5. 获取相关计算设备和切分信息
                        List<Long> resourceIds = runningTasks.stream()
                                .map(SchTaskInfo::getResourceId)
                                .filter(Objects::nonNull)
                                .distinct()
                                .toList();

                        List<SchComputeDevice> computeDevices = schComputeDeviceMapper.selectList(
                                new LambdaQueryWrapper<SchComputeDevice>()
                                        .in(!resourceIds.isEmpty(), SchComputeDevice::getResourceId, resourceIds));

                        List<Long> deviceIds = computeDevices.stream()
                                .map(SchComputeDevice::getId)
                                .filter(Objects::nonNull)
                                .distinct()
                                .toList();

                        List<SchVirtualComputeCardSituation> virtualSituations = schVirtualComputeCardSituationMappers.selectList(
                                new LambdaQueryWrapper<SchVirtualComputeCardSituation>()
                                        .in(!deviceIds.isEmpty(), SchVirtualComputeCardSituation::getComputeDeviceId, deviceIds));

                        List<Integer> vccIds = virtualSituations.stream()
                                .map(SchVirtualComputeCardSituation::getVccId)
                                .filter(Objects::nonNull)
                                .distinct()
                                .toList();
                        // 6. 生成 Compose 文件并部署
                        try {
                            SchTaskInfo result = composeGenerate.generateCompose(schResourceInfo, vccIds, schTaskInfo.getTaskImageId(), schTaskInfoDto.getId());
                            // 7. 任务启动成功，更新状态为运行中
                            schTaskInfoMapper.update(new LambdaUpdateWrapper<SchTaskInfo>()
                                    .eq(SchTaskInfo::getId, schTaskInfoDto.getId())
                                    .set(SchTaskInfo::getStatus, "running")
                                    .set(SchTaskInfo::getUpdateTime, new Date()));

                            log.info("任务抢占并启动成功: {}", schTaskInfoDto.getId());
                            return result;
                        } catch (IOException e) {
                            log.error("启动任务失败: {}", e.getMessage(), e);
                            throw new MyRuntimeException("启动任务失败: " + e.getMessage());
                        }
                    } catch (Exception e) {
                        log.error("抢占调度过程发生异常: {}", e.getMessage(), e);
                        throw e; // 重新抛出异常，让exceptionally处理
                    }
                }, poolExecutor) // 使用线程池执行主任务
                .exceptionally(ex -> {
                    // 8. 处理异常，更新任务状态为失败
                    log.error("抢占任务失败，taskId: {}, error: {}", schTaskInfoDto.getId(), ex.getMessage(), ex);
                    schTaskInfoMapper.update(new LambdaUpdateWrapper<SchTaskInfo>()
                            .eq(SchTaskInfo::getId, schTaskInfoDto.getId())
                            .set(SchTaskInfo::getStatus, "failed")
                            .set(SchTaskInfo::getUpdateTime, new Date())
                            .set(SchTaskInfo::getUpdateUserId, Objects.requireNonNull(TokenData.takeFromRequest()).getUserId()));
                    return null;
                });

        // 9. 添加任务完成后的回调处理
        future.thenAccept(result -> {
            if (result != null) {
                log.info("抢占任务异步处理完成，任务状态: {}", result.getStatus());
                // 可以在这里添加额外的成功处理逻辑，如发送通知等
                try {
                    // 启动监控数据采集
                    //composeGenerate.containerMonitorCollect(result.getId());
                } catch (Exception e) {
                    log.warn("启动监控数据采集失败: {}", e.getMessage());
                }
            }
        });
        // 10. 立即返回当前任务状态，不等待异步操作完成
        return schTaskInfoMapper.selectById(schTaskInfoDto.getId());
    }

    /**
     * @param taskId      任务ID
     * @param partitionId 切分卡ID
     */
    public SchTaskInfo memoryQueue(Long taskId, Long partitionId) {
        List<SchTaskInfo> running = schTaskInfoMapper.selectList(new LambdaUpdateWrapper<SchTaskInfo>()
                .eq(SchTaskInfo::getStatus, "running")
        );
        List<Long> list = running.stream().map(SchTaskInfo::getPartitionId).toList();
        if (list.contains(partitionId)) {
            log.warn("memoryQueue....切分卡正在运行中");
            schTaskInfoMapper
                    .update(new LambdaUpdateWrapper<SchTaskInfo>()
                            .eq(SchTaskInfo::getId, taskId).set(SchTaskInfo::getStatus, "queued")
                    );
            return schTaskInfoMapper.selectById(taskId);
        }
        return null;
    }


    /**
     * 内存调度 抢占调度
     *
     * @param taskId      任务ID
     * @param partitionId 分卡ID
     */
    public SchTaskInfo memoryDispatch(Long taskId, Long partitionId) {
        SchTaskInfo schTaskInfo = schTaskInfoMapper.selectById(taskId);
        List<SchTaskInfo> running = schTaskInfoMapper.selectList(new LambdaUpdateWrapper<SchTaskInfo>()
                .eq(SchTaskInfo::getStatus, "running")
        );
        for (SchTaskInfo item : running) {
            if (item.getPartition() == schTaskInfo.getPartition() && item.getTaskPriority() > schTaskInfo.getTaskPriority()) {
                schTaskInfoMapper
                        .update(new LambdaUpdateWrapper<SchTaskInfo>()
                                .eq(SchTaskInfo::getId, taskId)
                                .set(SchTaskInfo::getStatus, "queued")
                        );
                return schTaskInfoMapper.selectById(taskId);
            }
        }
        return null;
    }


    @Override
    public List<SchTaskInfo> getGroupedSchTaskInfoListWithRelation(
            SchTaskInfo filter, String groupSelect, String groupBy, String orderBy) {
        List<SchTaskInfo> resultList =
                schTaskInfoMapper.getGroupedSchTaskInfoList(filter, groupSelect, groupBy, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        // NOTE: 这里只是包含了关联数据，聚合计算数据没有包含。
        // 主要原因是，由于聚合字段通常被视为普通字段使用，不会在group by的从句中出现，语义上也不会在此关联。
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    private SchTaskInfo buildDefaultValue(SchTaskInfo schTaskInfo) {
        if (schTaskInfo.getId() == null) {
            schTaskInfo.setId(idGenerator.nextLongId());
        }
        TokenData tokenData = TokenData.takeFromRequest();
        schTaskInfo.setCreateUserId(tokenData.getUserId());
        schTaskInfo.setCreateTime(new Date());
        schTaskInfo.setUpdateTime(new Date());
        schTaskInfo.setIsDelete(GlobalDeletedFlag.NORMAL);
        return schTaskInfo;
    }

    /*
     * 获取容器数量
     * */
    @Override
    public Integer countByContainerName() {
        return schTaskInfoMapper.countByContainerName();
    }


    /**
     * 任务资源占用
     */
    @Override
    public List<SchTaskInfo> taskOccupyResource(Long resourceInfoId, Long resourcePoolId) {
        List<Long> resourceInfoIdList = new ArrayList<>();
        if (resourcePoolId != null) {
            //查询资源池下所有主机
            List<SchResourceInfo> schResourceInfoList = schResourceInfoMapper.queryResourceInfoByPoolId(resourcePoolId);
            resourceInfoIdList = schResourceInfoList.stream().map(SchResourceInfo::getId).toList();
        } else if (resourceInfoId != null) {
            resourceInfoIdList.add(resourceInfoId);
        } else {
            resourceInfoIdList = schResourceInfoMapper.selectList(null).stream().map(SchResourceInfo::getId).toList();
        }
        if (resourceInfoIdList.isEmpty()) {
            return new ArrayList<>();
        }
        //查询运行中的任务
        List<SchTaskInfo> schTaskInfos = schTaskInfoMapper.selectList(new LambdaQueryWrapper<SchTaskInfo>()
                .eq(SchTaskInfo::getStatus, "running")
                .in(SchTaskInfo::getResourceId, resourceInfoIdList));
        //提取资源id
        List<Long> resourceIds = schTaskInfos.stream().map(SchTaskInfo::getResourceId).toList();
        //提取显卡id
        List<Long> computeDeviceIds = schTaskInfos.stream().map(SchTaskInfo::getComputeDeviceId).toList();
        Map<Long, SchCardMonitor> schCardMonitorToComputeDeviceIdMap = new HashMap<>();
        Map<Long, SchNodeBasicMetrics> schNodeBasicMetricsToResourceIdMap = new HashMap<>();
        //存入监控信息
        for (SchTaskInfo schTaskInfo : schTaskInfos) {
            if (schTaskInfo.getResourceId() == null) {
                continue;
            }
            //获取最新时间
            SchNodeBasicMetrics newSchNodeBasicMetrics = schNodeBasicMetricsService.getLatestData(schTaskInfo.getResourceId());
            if (schTaskInfo.getComputeDeviceId() != null) {
                //查询显卡监控
                SchCardMonitor schCardMonitor = schCardMonitorService.getNpuMonitoringByComputeDeviceId(schTaskInfo.getComputeDeviceId(), newSchNodeBasicMetrics.getTs());
                schTaskInfo.setSchCardMonitor(schCardMonitor);
//                //提取显卡id转为map
//                schCardMonitorToComputeDeviceIdMap = schCardMonitorList.stream().collect(Collectors.toMap(SchCardMonitor::getComputeDeviceId, Function.identity()));
            }
            if (schTaskInfo.getResourceId() != null) {
                //查询cpu监控
                SchNodeBasicMetrics schNodeBasicMetrics = schNodeBasicMetricsService.getCpuMonitoringByResourceId(schTaskInfo.getResourceId(), newSchNodeBasicMetrics.getTs());
                schTaskInfo.setSchNodeBasicMetrics(schNodeBasicMetrics);
                //提取主机id转为map
//                schNodeBasicMetricsToResourceIdMap = schNodeBasicMetricsList.stream().collect(Collectors.toMap(SchNodeBasicMetrics::getResourceId, Function.identity()));
            }
//            schTaskInfo.setSchCardMonitor(schCardMonitorToComputeDeviceIdMap.get(schTaskInfo.getComputeDeviceId()));
//            schTaskInfo.setSchNodeBasicMetrics(schNodeBasicMetricsToResourceIdMap.get(schTaskInfo.getResourceId()));
        }

        return schTaskInfos;

    }

    /*
     * 获取执行任务的节点数量
     * */
    @Override
    public Integer getNodeNumber() {
        return schTaskInfoMapper.getNodeNumber();
    }

    /*
     * 获取不同状态的任务数量
     * */
    @Override
    public List<Map<String, Object>> getTaskStatusCount() {
        return schTaskInfoMapper.getTaskStatusCount();
    }

    /**
     * 获取任务的VNPU内存使用情况
     *
     * @param taskId 任务ID
     * @return VNPU内存使用情况
     */
    public Map<String, Object> getTaskVnpuMemoryUsage(Long taskId) {
        // 获取任务信息
        SchTaskInfo taskInfo = this.getById(taskId);
        if (taskInfo == null) {
            throw new MyRuntimeException("任务不存在");
        }

        // 获取容器ID
       /* String containerId = taskInfo.getContainerId();
        if (containerId == null || containerId.isEmpty()) {
            throw new MyRuntimeException("任务容器ID不存在");
        }*/

        // 获取资源ID
        Long resourceId = taskInfo.getResourceId();
        if (resourceId == null) {
            throw new MyRuntimeException("任务资源ID不存在");
        }

        // 获取VNPU内存使用情况
        return vnpuMonitorUtil.getContainerVnpuUsage(resourceId, "IJJ");
    }
}
