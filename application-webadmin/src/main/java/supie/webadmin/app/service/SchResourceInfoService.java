package supie.webadmin.app.service;

import com.alibaba.fastjson.JSONObject;
import com.jcraft.jsch.Session;
import supie.webadmin.app.model.*;
import supie.webadmin.app.dto.SchResourceInfoDto;
import supie.common.core.base.service.IBaseService;
import supie.common.core.object.ResponseResult;
import supie.webadmin.app.util.AgentConfig;
import supie.webadmin.app.util.ServerInfo;
import supie.webadmin.app.vo.SchResourceInfoVo;


import java.util.*;

/**
 * 服务资源信息表数据操作服务接口。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
public interface SchResourceInfoService extends IBaseService<SchResourceInfo, Long> {

    /**
     * 验证服务器连接并获取系统信息
     *
     * @param schResourceInfoDto 服务器连接信息
     * @return 验证结果和系统信息
     */
    ResponseResult<SchResourceInfoVo> verifyServerConnection(SchResourceInfoDto schResourceInfoDto);

    /**
     * 保存新增对象。
     *
     * @param schResourceInfo 新增对象。
     * @return 返回新增对象。
     */
    SchResourceInfo saveNew(SchResourceInfo schResourceInfo);

    /**
     * 利用数据库的insertList语法，批量插入对象列表。
     *
     * @param schResourceInfoList 新增对象列表。
     */
    void saveNewBatch(List<SchResourceInfo> schResourceInfoList);

    /**
     * 更新数据对象。
     *
     * @param schResourceInfo         更新的对象。
     * @param originalSchResourceInfo 原有数据对象。
     * @return 成功返回true，否则false。
     */
    boolean update(SchResourceInfo schResourceInfo, SchResourceInfo originalSchResourceInfo);

    /**
     * 删除指定数据。
     *
     * @param id 主键Id。
     * @return 成功返回true，否则false。
     */
    boolean remove(Long id);

    /**
     * 获取单表查询结果。由于没有关联数据查询，因此在仅仅获取单表数据的场景下，效率更高。
     * 如果需要同时获取关联数据，请移步(getSchResourceInfoListWithRelation)方法。
     *
     * @param filter  过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    List<SchResourceInfo> getSchResourceInfoList(SchResourceInfo filter, String orderBy);

    /**
     * 获取主表的查询结果，以及主表关联的字典数据和一对一从表数据，以及一对一从表的字典数据。
     * 该查询会涉及到一对一从表的关联过滤，或一对多从表的嵌套关联过滤，因此性能不如单表过滤。
     * 如果仅仅需要获取主表数据，请移步(getSchResourceInfoList)，以便获取更好的查询性能。
     *
     * @param filter 主表过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    List<SchResourceInfo> getSchResourceInfoListWithRelation(SchResourceInfo filter, String orderBy);

    /**
     * 获取分组过滤后的数据查询结果，以及关联的字典数据和一对一从表数据，以及一对一从表的字典数据。
     *
     * @param filter      过滤对象。
     * @param groupSelect 分组显示列表参数。位于SQL语句SELECT的后面。
     * @param groupBy     分组参数。位于SQL语句的GROUP BY后面。
     * @param orderBy     排序字符串，ORDER BY从句的参数。
     * @return 分组过滤结果集。
     */
    List<SchResourceInfo> getGroupedSchResourceInfoListWithRelation(
            SchResourceInfo filter, String groupSelect, String groupBy, String orderBy);

    /**
     * 获取服务器各种不同资源的总量
     *
     * @return 统计结果集。
     */
    HashMap<String, Object> getResourceStatistics();

    /*
    * 远程主机连接测试
    * @param resourceInfoId   服务资源对象id
    * @return  session      保持连接的会话
    * */
    Session SshConnector(Long resourceInfoId);

    /*
     * 获取节点历史使用率和节点的下的计算卡使用率
     * @param resourceInfoId        服务资源对象
     * @return  List<String>        节点对象和卡对象列表
     * */
    List<String> getHistoryInfos(SchResourceInfo schResourceInfo);

    List<String> getNowInfos(SchResourceInfoDto schResourceInfoDto);

    /*
     * 获取节点使用率和节点的下的计算卡使用率
     * @param resourceInfoId        服务资源对象
     * @return  List<String>        节点对象和卡对象列表
     * */
    List<Map<String,Object>> getResource(AgentConfig agentConfig);

    /*
      *
     * 节点注册：存储节点和计算卡信息进数据库
     * @param resourceInfoId        服务资源对象
     * @return
     * */
    void registerResourceInfo(SchResourceInfo schResourceInfo);

    /*
     *
     * 存储计算卡信息
     * @param resourceInfo        服务资源信息
     * ServerInfo                   服务信息
     * agentConfig                  连接信息
     * @return
     * */
    void saveComputeDeviceInfo(List<Map<String, Object>> resourceInfo, ServerInfo serverInfo,AgentConfig agentConfig);

    /**
     * 更新资源池和资源池关联的资源信息
     * @param poolId 当前资源池id resource_pool_id
     * @param id 当前资源id resource_info_id
     */
    void updateBindRelation(Long poolId, Long id);
    /*
     *
     * agent检测---修改
     * @param resourceInfo        服务资源信息
     * @return
     * */
    List<Map<String, Object>> getResources(JSONObject agentConfig);

    /*
     *
     * 存储计算卡信息
     * @param resourceInfo        服务资源信息列表
     * agentConfig                  连接信息
     * @return
     * */
    void saveComputeDeviceInfoV2(List<Map<String, Object>> resourceInfo, JSONObject agentConfig);
    /*
     *
     * 添加卡算力,产品类型等信息
     * @return
     * */
    JSONObject addDeviceInfo(String productType);
    /*
     *
     * @param schResourceInfo
     * @return
     * */
    List<Map<String, Object>> getComputeDeviceInfoByNodeId(SchResourceInfo schResourceInfo);

    Map<String, Object> statisticalIndicators(Long resourceId, String tsStart, String tsEnd, String type, Integer interval,Long resourcePoolId);

    SchNodeBasicMetrics calculateCPUAverage(List<SchNodeBasicMetrics> dataList);

    List<SchCardMonitor> calculateNPUAverage(List<SchCardMonitor> dataList);

    Map<String, Object> resourceTotal();

    List<SchResourceInfo> monitoringMetrics(List<SchResourceInfo> schResourceInfoList);

    List<SchResourceInfo> getSchResourceInfoListByPoolId(Long poolId);

    /*
    * 获取资源状态分组
    * */
    List<Map<String, Object>> getResourceStatusGroup();

    /*
    *根据主节点信息,查询节点下的卡信息
     */
    List<SchComputeDevice> getDeviceInfoByResource(SchResourceInfo schResourceInfo);

    /*
    * 根据主节点信息,查询节点下的卡的监控情况
    * */
    List<SchCardMonitor> getSchCardMonitorInfoByResource(SchResourceInfo schResourceInfo);

    SchNodeBasicMetrics getSchNodeBasicMetricsInfoByResource(SchResourceInfo schResourceInfo);

    List<Map<String, Object>> getResourceAggregationStats(List<Long> resourceIds);

    /*
    * 统计节点下的任务情况
    * */
    Map<String, Object> getResourceTaskStats(SchResourceInfo schResourceInfo);

    /*
    * 计算已使用的资源数量：任务占用+虚拟切分
     * */
    Integer getResourceUsedNumber();
}
