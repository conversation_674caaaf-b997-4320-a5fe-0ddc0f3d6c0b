package supie.webadmin.app.util;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jcraft.jsch.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import supie.webadmin.app.dao.SchResourceInfoMapper;
import supie.webadmin.app.model.SchResourceInfo;
import supie.webadmin.app.service.SchResourceInfoService;
import supie.webadmin.app.model.NpuChipNode;
import supie.webadmin.app.model.NpuDeviceNode;

import java.io.*;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * SSH连接工具类
 * 用于连接远程服务器并执行脚本获取系统信息
 *
 * <AUTHOR> -rf .bug
 * @date 2025-01-20
 */
@Slf4j
@Component
public class SshUtil {
   @Resource
   private SshConnectionUtil sshConnectionUtil;

    /**
     * 连接超时时间 10秒连接超时
     */
    private static final int CONNECTION_TIMEOUT = 10000;

    /**
     * 30秒命令执行超时
     */
    private static final int COMMAND_TIMEOUT = 30000; //
    /**
     * script name
     */
    private static final String SCRIPT_NAME = "info_check.sh";

    /**
     * 服务器存储路径
     */
    private static final String REMOTE_SCRIPT_PATH = "/tmp/" + SCRIPT_NAME;
    @Autowired
    private ComposeGenerate composeGenerate;

    @Resource
    private SchResourceInfoMapper schResourceInfoMapper;


    /**
     * 通过上传脚本获取服务器信息
     *
     * @param con SSH连接配置
     * @return 服务器信息
     */
    public  ServerInfo getServerInfoByScript(SshConfig con) {
        ServerInfo serverInfo = new ServerInfo();
        //Session session = null;
        ChannelSftp sftpChannel = null;
        // 创建SSH连接
        Session session=null;
        try {

            JSch jsch = new JSch();
            session  = jsch.getSession(con.getUsername(),con.getHost(), con.getPort());
            session.setPassword(con.getPassword());
            // 配置连接属性
            Properties config = new Properties();
            config.put("StrictHostKeyChecking", "no");
            // 只使用密码认证，不尝试其他方式
            //config.put("PreferredAuthentications", "password");
            session.setConfig("PreferredAuthentications", "publickey,password");
            // 禁用Kerberos认证
            config.put("GSSAPIAuthentication", "no");
            config.put("UserKnownHostsFile", "/dev/null");
            config.put("ServerAliveInterval", "60"); // 每60秒发送一次保活包
            config.put("ServerAliveCountMax", "3");  // 最多允许丢失3个保活包
            config.put("TCPKeepAlive", "yes");       // 启用TCP保活
            session.setConfig(config);
            session.setTimeout(COMMAND_TIMEOUT);
            // 建立连接
            session.connect();
             /*connection = sshConnectionUtil.createConnection(config.getHost(), config.getPort(), config.getUsername(), config.getPassword(), CONNECTION_TIMEOUT);*/
            serverInfo.setConnected(true);
            // 上传脚本文件 ssh root@************* -p 40086     Ascend12!@
            uploadScript(session);
            // 执行脚本
            String result = executeCommand(session, "chmod +x " + REMOTE_SCRIPT_PATH + " && " + REMOTE_SCRIPT_PATH);
            // 解析JSON结果
            parseServerInfo(result, serverInfo);
            // 清理远程脚本文件
            cleanupScript( session);
        } catch (JSchException e) {
            log.error("SSH连接失败: {}", e.getMessage(), e);
            serverInfo.setConnected(false);
            serverInfo.setErrorMessage("连接失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("获取服务器信息失败: {}", e.getMessage(), e);
            serverInfo.setConnected(false);
            serverInfo.setErrorMessage("获取信息失败: " + e.getMessage());
        } finally {
           if(session!=null){
               session.disconnect();
           }
        }
        
        return serverInfo;
    }

    /**
     * 上传脚本文件到远程服务器
     */
    private static void uploadScript(Session session) throws JSchException, SftpException, IOException {
        ChannelSftp sftpChannel = null;
        try {
            sftpChannel = (ChannelSftp) session.openChannel("sftp");
            sftpChannel.connect();
            // 读取本地脚本文件
            ClassPathResource resource = new ClassPathResource("scripts/" + SCRIPT_NAME);
            InputStream inputStream = resource.getInputStream();
            // 上传到远程服务器
            sftpChannel.put(inputStream, REMOTE_SCRIPT_PATH);
            inputStream.close();
            log.info("脚本文件上传成功: {}", REMOTE_SCRIPT_PATH);
        } finally {
            if (sftpChannel != null && sftpChannel.isConnected()) {
                sftpChannel.disconnect();
            }
        }
    }

    /**
     * 清理远程脚本文件
     * 提供多种清理方式，避免使用rm命令
     */
    private static void cleanupScript(Session session) {
        try {
            // 方式1: 使用SFTP删除文件（推荐方式）
            if (cleanupScriptBySftp(session)) {
                log.debug("通过SFTP成功清理远程脚本文件: {}", REMOTE_SCRIPT_PATH);
                return;
            }
            // 方式2: 使用cat /dev/null覆盖文件内容
            if (cleanupScriptByOverwrite(session)) {
                log.debug("通过覆盖内容成功清理远程脚本文件: {}", REMOTE_SCRIPT_PATH);
                return;
            }
            // 方式3: 使用mv命令重命名文件（移动到临时位置）
            if (cleanupScriptByMove(session)) {
                log.debug("通过移动文件成功清理远程脚本文件: {}", REMOTE_SCRIPT_PATH);
                return;
            }
            // 方式4: 使用echo覆盖文件内容
            if (cleanupScriptByEcho(session)) {
                log.debug("通过echo覆盖成功清理远程脚本文件: {}", REMOTE_SCRIPT_PATH);
                return;
            }
            log.warn("所有清理方式都失败，脚本文件可能仍存在: {}", REMOTE_SCRIPT_PATH);
            
        } catch (Exception e) {
            log.warn("清理远程脚本文件失败: {}", e.getMessage());
        }
    }

    /**
     * 通过SFTP删除文件
     */
    private static boolean cleanupScriptBySftp(Session session) {
        ChannelSftp sftpChannel = null;
        try {
            sftpChannel = (ChannelSftp) session.openChannel("sftp");
            sftpChannel.connect();
            sftpChannel.rm(REMOTE_SCRIPT_PATH);
            return true;
        } catch (Exception e) {
            log.debug("SFTP删除文件失败: {}", e.getMessage());
            return false;
        } finally {
            if (sftpChannel != null && sftpChannel.isConnected()) {
                sftpChannel.disconnect();
            }
        }
    }

    /**
     * 通过覆盖文件内容清理
     */
    private static boolean cleanupScriptByOverwrite(Session session) {
        try {
            String result = executeCommand(session, "cat /dev/null > " + REMOTE_SCRIPT_PATH);
            return true;
        } catch (Exception e) {
            log.debug("覆盖文件内容失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 通过移动文件清理
     */
    private static boolean cleanupScriptByMove(Session session) {
        try {
            String tempPath = REMOTE_SCRIPT_PATH + ".bak." + System.currentTimeMillis();
            String result = executeCommand(session, "mv " + REMOTE_SCRIPT_PATH + " " + tempPath);
            return true;
        } catch (Exception e) {
            log.debug("移动文件失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 通过echo覆盖文件内容清理
     */
    private static boolean cleanupScriptByEcho(Session session) {
        try {
            String result = executeCommand(session, "echo '' > " + REMOTE_SCRIPT_PATH);
            return true;
        } catch (Exception e) {
            log.debug("echo覆盖文件失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 解析服务器信息JSON结果
     */
    private static void parseServerInfo(String jsonResult, ServerInfo serverInfo) {
        try {
            JSONObject jsonObject = JSON.parseObject(jsonResult);
            serverInfo.setSystemInfo(jsonObject.getString("systemInfo"));
            serverInfo.setSystemVersion(jsonObject.getString("systemVersion"));
            serverInfo.setCpuCoreCount(jsonObject.getString("cpuCoreCount"));
            serverInfo.setTotalMemory(jsonObject.getString("totalMemory"));
            serverInfo.setUsedMemory(jsonObject.getString("usedMemory"));
            serverInfo.setAvailableMemory(jsonObject.getString("availableMemory"));
            serverInfo.setGpuCount(jsonObject.getString("gpuCount"));
            serverInfo.setGpuMemory(jsonObject.getString("gpuMemory"));
            log.info("脚本执行成功，获取到系统信息: {}", jsonObject.toJSONString());
        } catch (Exception e) {
            log.error("解析脚本结果失败: {}", e.getMessage(), e);
            serverInfo.setErrorMessage("解析结果失败: " + e.getMessage());
            setDefaultServerInfo(serverInfo);
        }
    }
    /**
     * 设置默认服务器信息
     */
    private static void setDefaultServerInfo(ServerInfo serverInfo) {
        if (serverInfo.getSystemInfo() == null) serverInfo.setSystemInfo("Unknown");
        if (serverInfo.getSystemVersion() == null) serverInfo.setSystemVersion("Unknown");
        if (serverInfo.getCpuCoreCount() == null) serverInfo.setCpuCoreCount("Unknown");
        if (serverInfo.getTotalMemory() == null) serverInfo.setTotalMemory("Unknown");
        if (serverInfo.getUsedMemory() == null) serverInfo.setUsedMemory("Unknown");
        if (serverInfo.getAvailableMemory() == null) serverInfo.setAvailableMemory("Unknown");
        if (serverInfo.getGpuCount() == null) serverInfo.setGpuCount("0");
        if (serverInfo.getGpuMemory() == null) serverInfo.setGpuMemory("0GB");
        if (serverInfo.getGpuDetails() == null) serverInfo.setGpuDetails("[]");
    }

    /**
     * 执行SSH命令
     *
     * @param session SSH会话
     * @param command 要执行的命令
     * @return 命令执行结果
     */
    private static String executeCommand(Session session, String command) throws JSchException, IOException {
        ChannelExec channel = null;
        try {
            channel = (ChannelExec) session.openChannel("exec");
            channel.setCommand(command);
            InputStream in = channel.getInputStream();
            InputStream err = channel.getErrStream();
            channel.connect(COMMAND_TIMEOUT);
            StringBuilder result = new StringBuilder();
            StringBuilder errorOutput = new StringBuilder();
            BufferedReader reader = new BufferedReader(new InputStreamReader(in));
            BufferedReader errorReader = new BufferedReader(new InputStreamReader(err));
            String line;
            while ((line = reader.readLine()) != null) {
                result.append(line).append("\n");
            }
            while ((line = errorReader.readLine()) != null) {
                errorOutput.append(line).append("\n");
            }
            // 等待命令执行完成
            while (!channel.isClosed()) {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
            int exitCode = channel.getExitStatus();
            String output = result.toString().trim();
            if (exitCode != 0 && !errorOutput.isEmpty()) {
                log.warn("命令执行警告 [{}], 退出码: {}, 错误输出: {}", command, exitCode, errorOutput.toString());
            }
            log.debug("命令执行结果 [{}]: {}", command, output);
            return output.isEmpty() ? "Unknown" : output;
            
        } finally {
            if (channel != null && channel.isConnected()) {
                channel.disconnect();
            }
        }
    }




    /**
     * 本地执行命令并返回执行结果
     */
    public CommandExecutionResult localExecutionCommand(String command) {
        try {
            // 执行命令
            Process process = Runtime.getRuntime().exec(command);
            // 读取标准输出
            BufferedReader stdInput = new BufferedReader(new InputStreamReader(process.getInputStream()));
            StringBuilder output = new StringBuilder();
            String line;
            while ((line = stdInput.readLine()) != null) {
                output.append(line).append("\n");
            }
            // 读取错误输出
            BufferedReader stdError = new BufferedReader(new InputStreamReader(process.getErrorStream()));
            StringBuilder errorOutput = new StringBuilder();
            while ((line = stdError.readLine()) != null) {
                errorOutput.append(line).append("\n");
            }

            // 等待命令完成
            int exitCode = process.waitFor();
            log.debug("命令执行完成，退出码：" + exitCode);
            log.debug("标准输出：" + output.toString());
            log.debug("错误输出：" + errorOutput.toString());
            CommandExecutionResult commandExecutionResult = new CommandExecutionResult();
            commandExecutionResult.setExitCode(exitCode);
            commandExecutionResult.setStdOutput(output.toString());
            commandExecutionResult.setStdError(errorOutput.toString());
            return commandExecutionResult;
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
            throw new RuntimeException("服务器命令执行失败！"+e.getMessage());
        }
    }
    /**
     * 远程调用服务器执行命令并返回执行结果
     */
    public CommandExecutionResult executionCommand(Long resourceInfoId, String command) {
        SchResourceInfo info = schResourceInfoMapper.selectById(resourceInfoId);
        SshConnection connection = null;
        Session session;
        try {
           connection = sshConnectionUtil.createConnection(info.getHostIp(), info.getPort(), info.getLoginName(), info.getPassword(), 3000);
            session = connection.getSession();
        } catch (JSchException e) {
            throw new RuntimeException(e);
        }
        //Session session= composeGenerate.initConnect(resourceInfoId);

        CommandExecutionResult result = new CommandExecutionResult();
        StringBuilder stdOutput = new StringBuilder();
        StringBuilder stdError = new StringBuilder();

        try {
            Channel channel = session.openChannel("exec");
            ((ChannelExec) channel).setCommand(command);

            // 设置错误流处理器
            ((ChannelExec) channel).setErrStream(new OutputStream() {
                private final StringBuilder lineBuffer = new StringBuilder();

                @Override
                public void write(int b) {
                    char c = (char) b;
                    lineBuffer.append(c);
                    if (c == '\n' || lineBuffer.length() >= 1024) {
                        stdError.append(lineBuffer.toString()); // 将错误输出存储到 stdError
                        lineBuffer.setLength(0);
                    }
                }

                @Override
                public void close() throws IOException {
                    super.close();
                    if (lineBuffer.length() > 0) {
                        stdError.append(lineBuffer.toString()); // 将剩余内容存储到 stdError
                        lineBuffer.setLength(0);
                    }
                }
            });

            // 获取标准输出流
            InputStream in = channel.getInputStream();
            channel.connect();

            // 读取标准输出
            byte[] tmp = new byte[1024];
            while (true) {
                while (in.available() > 0) {
                    int i = in.read(tmp, 0, 1024);
                    if (i < 0) break;
                    stdOutput.append(new String(tmp, 0, i)); // 将标准输出存储到 stdOutput
                }
                if (channel.isClosed()) {
                    if (in.available() > 0) continue;
                    result.setExitCode(channel.getExitStatus()); // 设置退出码
                    break;
                }
                Thread.sleep(1000); // 避免 CPU 占用过高
            }

            // 设置标准输出和错误输出
            result.setStdOutput(stdOutput.toString());
            result.setStdError(stdError.toString());

            // 记录日志
            log.debug("命令执行完成，退出码：" + result.getExitCode());
            log.debug("标准输出：" + result.getStdOutput());
            log.debug("错误输出：" + result.getStdError());

            channel.disconnect();
        } catch (JSchException | IOException | InterruptedException e) {
            log.error("服务器命令执行失败：" + e.getMessage());
            throw new RuntimeException("服务器命令执行失败！", e);
        } finally {
            connection.close();
        }

        return result;
    }
} 