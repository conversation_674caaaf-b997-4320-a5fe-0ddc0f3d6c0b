package supie.webadmin.app.util;

import io.reactivex.rxjava3.core.Single;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.sql.Timestamp;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;


@Component
public class NpuToolUtil {
    private static final Logger log = LoggerFactory.getLogger(NpuToolUtil.class);

    public  List<HardwareAccelerator> getNpuMonitoringInformationTool(String monitoringUrl,Long remoteHostId) {
        List<HardwareAccelerator> hardwareAccelerators = new ArrayList<>();
        try {
            // 创建URL对象指向给定地址，用于获取指标数据
            URL url = new URL(monitoringUrl);

            // 打开与URL的连接
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();

            // 设置HTTP请求的方法为GET
            conn.setRequestMethod("GET");

            // 使用BufferedReader来读取服务器的响应
            BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream()));

            // 创建一个列表，用于存储解析后的监控数据
            List<NpuMonitoring> npuMonitoringList = new ArrayList<>();

            // 逐行读取输入流中的数据
            String inputLine;
            while ((inputLine = in.readLine()) != null) {
                // 过滤掉描述性或元数据行
                if (!inputLine.startsWith("# TYPE") && !inputLine.startsWith("# HELP") && inputLine.contains("{")) {
                    // 创建一个新的监控对象
                    NpuMonitoring npuMonitoring = new NpuMonitoring();

                    // 解析监控项名称
                    String[] parts = inputLine.split("\\{", 2);
                    if (parts.length > 1) {
                        npuMonitoring.setName(parts[0].trim());

                        // 解析值和时间戳
                        String[] middleParts = parts[1].split("\\}", 2);
                        if (middleParts.length > 1) {
                            String[] values = middleParts[1].trim().split(" ", 2);
                            npuMonitoring.setValue(values[0]);
                            extracted(npuMonitoring, middleParts[0].split(","));

                            // 解析时间戳
                            if (values.length > 1) {
                                try {
                                    npuMonitoring.setTime(Long.parseLong(values[1]));
                                } catch (NumberFormatException e) {
                                    npuMonitoring.setFormattedDate("Invalid timestamp");
                                }
                            } else {
                                npuMonitoring.setFormattedDate(null);
                            }
                        }
                    }
                    // 将解析后的监控对象添加到列表中
                    npuMonitoringList.add(npuMonitoring);
                }
            }
            // 按设备ID对监控列表进行分组
            Map<String, List<NpuMonitoring>> collect = npuMonitoringList.stream()
                    .filter(m -> m.getId() != null)
                    .collect(Collectors.groupingBy(NpuMonitoring::getId));
            Timestamp timestamp = new Timestamp(System.currentTimeMillis());

            // 处理每个分组的数据
            for (String key : collect.keySet()) {
                ComputingPowerManagement management = new ComputingPowerManagement();
                HardwareAccelerator hardwareAccelerator = new HardwareAccelerator();
                hardwareAccelerator.setTs(timestamp);

                HardwareAccelerator accelerator = saveEncapsulateEntityClassesRemote(collect.get(key), management, remoteHostId, hardwareAccelerator);
                Long fbTotal = accelerator.getFbTotal();
                Long fbUsed = accelerator.getFbUsed();
                accelerator.setFbUsedUtil(fbTotal == 0 ? 0 :  ((double)fbUsed / fbTotal));
                accelerator.setFbFree(fbTotal-fbUsed);
                Long totalMemMemory = accelerator.getTotalMemMemory() != null ? accelerator.getTotalMemMemory() : 0L;
                Long usedMemMemory = accelerator.getUsedMemMemory() != null ? accelerator.getUsedMemMemory() : 0L;
                accelerator.setMemUtilRate(usedMemMemory == 0 ? 0 :  ((double)usedMemMemory / totalMemMemory));
                management.setUpdateTime(new Date());
                hardwareAccelerators.add(accelerator);
            }

            // 关闭输入流
            in.close();
        }catch (IOException e){
            log.error("出现io错误"+e.getMessage(), e);
            e.getStackTrace();
        }
        return hardwareAccelerators;

    }

    // 将NpuMonitoring对象列表保存到硬件加速器管理对象
    public static HardwareAccelerator saveEncapsulateEntityClassesRemote(List<NpuMonitoring> npuMonitoringList, ComputingPowerManagement management ,Long id,HardwareAccelerator hardwareAccelerator){
        for (NpuMonitoring npuMonitoring : npuMonitoringList) {
            hardwareAccelerator.setSerialNumber(Integer.parseInt(npuMonitoring.getId()));
            hardwareAccelerator.setModelName(npuMonitoring.getModelName());
            hardwareAccelerator.setRemoteHostId(id);

            management.setGpu(Integer.parseInt(npuMonitoring.getId()));
            management.setModelname(npuMonitoring.getModelName());
            management.setRemoteHostId(id);
            //    npu_chip_info_total_memory: NPU芯片的总内存信息。
            //    npu_chip_info_used_memory: NPU芯片的已使用内存信息。
            // 根据监控项名称更新硬件加速器的相关属性
            switch (npuMonitoring.getName()){
                // 温度
                case "npu_chip_info_temperature":
                    hardwareAccelerator.setTemp(Integer.parseInt(npuMonitoring.getValue()));
                    break;
                case "npu_chip_info_utilization":
                    hardwareAccelerator.setUtilRate(Double.parseDouble(npuMonitoring.getValue()));
                    break;
                case "npu_chip_info_total_memory":
                    hardwareAccelerator.setTotalMemMemory(Long.parseLong(npuMonitoring.getValue()));
                    break;
                case "npu_chip_info_used_memory":
                    hardwareAccelerator.setUsedMemMemory(Long.parseLong(npuMonitoring.getValue()));
                    break;
                case "npu_chip_info_hbm_total_memory":
                    hardwareAccelerator.setFbTotal(Long.parseLong(npuMonitoring.getValue()));
                    break;
                case "npu_chip_info_hbm_used_memory":
                    hardwareAccelerator.setFbUsed(Long.parseLong(npuMonitoring.getValue()));
                    break;
            }
        }
        return hardwareAccelerator;
    }

    // 解析监控数据中的标签
    private static void extracted(NpuMonitoring npuMonitoring, String[] pairs) {
        for (String pair : pairs) {
            String[] keyValue = pair.split("=");
            if (keyValue.length == 2) {
                String value = keyValue[1].trim().replaceAll("^\"|\"$", "");
                switch (keyValue[0]) {
                    case "container_name":
                        npuMonitoring.setContainerName(value);
                        break;
                    case "id":
                        npuMonitoring.setId(value);
                        break;
                    case "model_name":
                        npuMonitoring.setModelName(value);
                        break;
                    case "namespace":
                        npuMonitoring.setNamespace(value);
                        break;
                    case "pcie_bus_info":
                        npuMonitoring.setPcieBusInfo(value);
                        break;
                    case "pod_name":
                        npuMonitoring.setPodName(value);
                        break;
                    case "vdie_id":
                        npuMonitoring.setVdieId(value);
                        break;
                    // 可以根据需要添加更多的case
                    default:
                        // 处理未识别的属性
                        break;
                }
            }
        }
    }



//    npu_chip_info_bandwidth_tx: NPU芯片发送数据的带宽信息。
//    npu_chip_info_error_code: NPU芯片的错误代码信息。
//    npu_chip_info_hbm_total_memory: NPU芯片上HBM（高带宽内存）的总内存信息。   ---有
//    npu_chip_info_hbm_used_memory: NPU芯片上HBM的已使用内存信息。             ---有
//    npu_chip_info_health_status: NPU芯片的健康状态信息。                    ----有
//    npu_chip_info_link_status: NPU芯片的连接状态信息。
//    npu_chip_info_name: NPU芯片的名称信息。
//    npu_chip_info_network_status: NPU芯片的网络状态信息。
//    npu_chip_info_power: NPU芯片的功耗信息。
//    npu_chip_info_process_info: NPU芯片的进程信息。                   ----npu-smi info -l | grep -i "Total Count"  --查询npu总核数
//    npu_chip_info_rx_ecn_num: NPU芯片接收到的ECN数目。                 ---npu-smi info -t board -i 0 -c 0 | grep -E "Chip Type|Chip Name"    --芯片版本，芯片型号
//    npu_chip_info_rx_fcs_num: NPU芯片接收到的FCS数目。                                     //	Chip Type                      : Ascend
//    npu_chip_info_temperature: NPU芯片的温度信息。                                         //Chip Name                        ：910B
//    npu_chip_info_total_memory: NPU芯片的总内存信息。              ----有
//    npu_chip_info_used_memory: NPU芯片的已使用内存信息。             ----有
//    npu_chip_info_utilization: NPU芯片的利用率信息。               -----有
//    npu_chip_info_vector_utilization: NPU芯片向量利用率信息。
//    npu_chip_info_voltage: NPU芯片的电压信息。
//    npu_chip_link_speed: NPU芯片连接的速度信息。
//    npu_chip_link_up_num: NPU芯片连接上的数目。
//    npu_chip_mac_rx_bad_oct_num: NPU芯片MAC接收到的错误八位数目。
//    npu_chip_mac_rx_bad_pkt_num: NPU芯片MAC接收到的错误数据包数目。
//    npu_chip_mac_rx_pause_num: NPU芯片MAC接收到的暂停数目。
//    npu_chip_mac_rx_pfc_pkt_num: NPU芯片MAC接收到的PFC数据包数目。
//    npu_chip_mac_tx_bad_oct_num: NPU芯片MAC发送的错误八位数目。
//    npu_chip_mac_tx_bad_pkt_num: NPU芯片MAC发送的错误数据包数目。
//    npu_chip_mac_tx_pause_num: NPU芯片MAC发送的暂停数目。
//    npu_chip_mac_tx_pfc_pkt_num: NPU芯片MAC发送的PFC数据包数目。
//    npu_chip_optical_rx_power_0至npu_chip_optical_rx_power_3: NPU芯片光接收端口的功率信息。
//    npu_chip_optical_state: NPU芯片光状态信息。
//    npu_chip_optical_temp: NPU芯片光端口的温度信息。
//    npu_chip_optical_tx_power_0至npu_chip_optical_tx_power_3: NPU芯片光发送端口的功率信息。
//    npu_chip_optical_vcc: NPU芯片光VCC（电源电压）信息。
//    npu_chip_roce_new_pkt_rty_num: NPU芯片ROCE（RDMA over Converged Ethernet）新数据包重试数目。
//    npu_chip_roce_out_of_order_num: NPU芯片ROCE乱序数据包数目。
//    npu_chip_roce_qp_status_err_num: NPU芯片ROCE QP（Queue Pair）状态错误数目。
//    npu_chip_roce_rx_all_pkt_num: NPU芯片ROCE接收到的所有数据包数目。
//    npu_chip_roce_rx_cnp_pkt_num: NPU芯片ROCE接收到的CNP（Credit Notice Packet）数据包数目。
//    npu_chip_roce_rx_err_pkt_num: NPU芯片ROCE接收到的错误数据包数目。
//    npu_chip_roce_tx_all_pkt_num: NPU芯片ROCE发送的所有数据包数目。
//    npu_chip_roce_tx_cnp_pkt_num: NPU芯片ROCE发送的CNP数据包数目。
//    npu_chip_roce_tx_err_pkt_num: NPU芯片ROCE发送的错误数据包数目。
//    npu_chip_roce_unexpected_ack_num: NPU芯片ROCE意外确认数目。
//    npu_chip_roce_verification_err_num: NPU芯片ROCE验证错误数目。



    public  List<Map<String,Object>> getNpuInformation(String monitoringUrl) {
        List<Map<String,Object>> npuList = new ArrayList<>();
        try {
            // 创建URL对象指向给定地址，用于获取指标数据
            URL url = new URL(monitoringUrl);

            // 打开与URL的连接
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();

            // 设置HTTP请求的方法为GET
            conn.setRequestMethod("GET");

            // 使用BufferedReader来读取服务器的响应
            BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream()));

            // 创建一个列表，用于存储解析后的监控数据
            List<NpuMonitoring> npuMonitoringList = new ArrayList<>();

            // 逐行读取输入流中的数据
            String inputLine;
            while ((inputLine = in.readLine()) != null) {
                // 过滤掉描述性或元数据行
                if (!inputLine.startsWith("# TYPE") && !inputLine.startsWith("# HELP") && inputLine.contains("{")) {
                    // 创建一个新的监控对象
                    NpuMonitoring npuMonitoring = new NpuMonitoring();

                    // 解析监控项名称
                    String[] parts = inputLine.split("\\{", 2);
                    if (parts.length > 1) {
                        npuMonitoring.setName(parts[0].trim());

                        // 解析值和时间戳
                        String[] middleParts = parts[1].split("\\}", 2);
                        if (middleParts.length > 1) {
                            String[] values = middleParts[1].trim().split(" ", 2);
                            npuMonitoring.setValue(values[0]);
                            extracted(npuMonitoring, middleParts[0].split(","));

                            // 解析时间戳
                            if (values.length > 1) {
                                try {
                                    npuMonitoring.setTime(Long.parseLong(values[1]));
                                } catch (NumberFormatException e) {
                                    npuMonitoring.setFormattedDate("Invalid timestamp");
                                }
                            } else {
                                npuMonitoring.setFormattedDate(null);
                            }
                        }
                    }
                    // 将解析后的监控对象添加到列表中
                    npuMonitoringList.add(npuMonitoring);
                }
            }
            Map<String, Object> saveMap = new HashMap<>();
            //显存利用率
            //    npu_chip_info_total_memory: NPU芯片的总内存信息。
            //    npu_chip_info_used_memory: NPU芯片的已使用内存信息。
            List<NpuMonitoring> fbTotalList = npuMonitoringList.stream()
                    .filter(entity -> "npu_chip_info_total_memory".equals(entity.getName()))
                    .collect(Collectors.toList());
            List<NpuMonitoring> fbUsedList = npuMonitoringList.stream()
                    .filter(entity -> "npu_chip_info_used_memory".equals(entity.getName()))
                    .collect(Collectors.toList());
            for (int i = 0; i < fbTotalList.size(); i++){
                Double fbTotal = Double.parseDouble(fbTotalList.get(i).getValue());
                Double fbUsed = Double.parseDouble(fbUsedList.get(i).getValue());
                saveSystemNpuMemory(fbTotal,fbUsed,npuList);
            }
            saveSystemNpuMemoryTotal(fbTotalList,fbUsedList,saveMap);
            npuList.add(saveMap);
            // 关闭输入流
            in.close();
        }catch (IOException e){
            log.error("出现io错误"+e.getMessage(), e);
            e.getStackTrace();
        }
        return npuList;

    }
    /*
     * 计算卡的npu使用率
     * */
    private static void saveSystemNpuMemory(Double fbTotal, Double fbUsed, List<Map<String,Object>> npuLis) {
        Map<String, Object> saveMap = new HashMap<>();

        saveMap.put("fbUsed",fbUsed);
        saveMap.put("npuMemorySum",fbTotal);
        saveMap.put("npuMemoryUtilization",(fbUsed / fbTotal) * 100+"%");
        npuLis.add(saveMap);
    }
    /**
     * 显存利用率（%） = (已使用内存 / (已使用内存 + 空闲内存)) * 100
     * @param fbTotalList
     * @param fbUsedList
     * @param saveMap
     */
    private static void saveSystemNpuMemoryTotal(List<NpuMonitoring> fbTotalList, List<NpuMonitoring> fbUsedList, Map<String, Object> saveMap) {
        double fbUsedSum = 0.0;
        double fbTotalSum = 0.0;

        if(fbUsedList != null && fbUsedList.size() != 0){
            for (NpuMonitoring npuMonitoring : fbUsedList) {
                fbUsedSum += Double.parseDouble(npuMonitoring.getValue());
            }
        }

        if(fbTotalList != null && fbTotalList.size() != 0){
            for (NpuMonitoring npuMonitoring : fbTotalList) {
                fbTotalSum += Double.parseDouble(npuMonitoring.getValue());
            }
        }
        saveMap.put("fbUsedSum",fbUsedSum);
        saveMap.put("npuMemorySum",fbTotalSum);
        saveMap.put("npuMemoryUtilization",(fbUsedSum / fbTotalSum) * 100+"%");
    }

    public  List<Map<String,String>> processNpuMonitoringList(List<NpuMonitoring> npuMonitoringList) {
        List<Map<String,String>> npuList = new ArrayList<>();
            // 按设备ID对监控列表进行分组
            Map<String, List<NpuMonitoring>> collect = npuMonitoringList.stream()
                    .filter(m -> m.getId() != null)
                    .collect(Collectors.groupingBy(NpuMonitoring::getId));

            // 处理每个分组的数据
            for (String key : collect.keySet()) {
                    Map<String, String> map = new HashMap<>();
                Map<String, String> saveEntityMap = saveEntity(collect.get(key), map);
                Long fbTotal = Long.parseLong(saveEntityMap.get("fbTotal"));
                Long fbUsed = Long.parseLong(saveEntityMap.get("fbUsed"));
                double fbUsedUtil = fbTotal == 0 ? 0 : ((double) fbUsed / fbTotal);
                map.put("fbUsedUtil",String.valueOf(fbUsedUtil));
                map.put("fbFree",String.valueOf(fbTotal-fbUsed));
                map.put("fbTotal",String.valueOf(fbTotal));
                map.put("fbUsed",String.valueOf(fbUsed));
                Double totalMemMemory = Optional.ofNullable(saveEntityMap.get("totalMemMemory"))
                        .map(String::trim)
                        .filter(s -> !s.isEmpty())
                        .map(Double::parseDouble)
                        .orElse(0.0);

                Double usedMemMemory = Optional.ofNullable(saveEntityMap.get("usedMemMemory"))
                        .map(String::trim)
                        .filter(s -> !s.isEmpty())
                        .map(Double::parseDouble)
                        .orElse(0.0);map.put("memUtilRate",String.valueOf(usedMemMemory == 0 ? 0 :  ((double)usedMemMemory / totalMemMemory)));
                npuList.add(saveEntityMap);
            }

        return npuList;

    }

    public static Map<String, String> saveEntity(List<NpuMonitoring> npuMonitoringList, Map<String, String> map){
        for (NpuMonitoring npuMonitoring : npuMonitoringList) {
            //hardwareAccelerator.setSerialNumber(Integer.parseInt(npuMonitoring.getId()));
            map.put("serialNumber",npuMonitoring.getId());
            //hardwareAccelerator.setModelName(npuMonitoring.getModelName());
            map.put("modelName",npuMonitoring.getModelName());
            //hardwareAccelerator.setRemoteHostId(id);
            //    npu_chip_info_total_memory: NPU芯片的总内存信息。
            //    npu_chip_info_used_memory: NPU芯片的已使用内存信息。
            // 根据监控项名称更新硬件加速器的相关属性
            switch (npuMonitoring.getName()){
                // 温度
                case "npu_chip_info_temperature":
                    //hardwareAccelerator.setTemp(Integer.parseInt(npuMonitoring.getValue()));
                    map.put("temp",npuMonitoring.getValue());
                    break;
                case "npu_chip_info_utilization":
                    //hardwareAccelerator.setUtilRate(Double.parseDouble(npuMonitoring.getValue()));
                    map.put("utilRate",npuMonitoring.getValue());
                    break;
                    // 芯片健康状态
                case "npu_chip_info_health_status":
                    //hardwareAccelerator.setHealthStatus(Integer.parseInt(npuMonitoring.getValue()));
                    map.put("healthStatus",npuMonitoring.getValue());
                    break;
                case "npu_chip_info_total_memory":
                    //hardwareAccelerator.setTotalMemMemory(Long.parseLong(npuMonitoring.getValue()));
                    map.put("totalMemMemory",npuMonitoring.getValue());
                    break;
                case "npu_chip_info_used_memory":
                    //hardwareAccelerator.setUsedMemMemory(Long.parseLong(npuMonitoring.getValue()));
                    map.put("usedMemMemory",npuMonitoring.getValue());
                    break;
                case "npu_chip_info_hbm_total_memory":
                    //hardwareAccelerator.setFbTotal(Long.parseLong(npuMonitoring.getValue()));
                    map.put("fbTotal",npuMonitoring.getValue());
                    break;
                case "npu_chip_info_hbm_used_memory":
                    //hardwareAccelerator.setFbUsed(Long.parseLong(npuMonitoring.getValue()));
                    map.put("fbUsed",npuMonitoring.getValue());
                    break;
            }
        }
        return map;
    }

public List<NpuMonitoring> NpuMonitoringList(String monitoringUrl) {
    int maxRetries = 3;
    IOException lastException = null;

    for (int attempt = 0; attempt < maxRetries; attempt++) {
        try {
            return fetchMetricsWithHttpClient(monitoringUrl);
        } catch (IOException | InterruptedException e) {
            lastException = new IOException("Attempt " + (attempt + 1) + " failed", e);
            log.warn("请求失败，第 {} 次重试...", attempt + 1, e);

            if (attempt < maxRetries - 1) {
                try {
                    // 指数退避：1s -> 2s -> 4s
                    int backoff = (int) Math.pow(2, attempt) * 1000;
                    int jitter = ThreadLocalRandom.current().nextInt(0, 1000); // 随机抖动
                    Thread.sleep(backoff + jitter);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("重试休眠中断", ie);
                }
            }
        }
    }

    throw new RuntimeException("获取指标数据失败", lastException);
}

private List<NpuMonitoring> fetchMetricsWithHttpClient(String monitoringUrl) throws IOException, InterruptedException {
    HttpClient client = HttpClient.newHttpClient();
    HttpRequest request = HttpRequest.newBuilder()
            .uri(URI.create(monitoringUrl))
            .timeout(Duration.ofSeconds(5)) // 设置最大响应时间
            .GET()
            .build();

    HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());

    if (response.statusCode() != 200) {
        throw new IOException("Server returned HTTP code: " + response.statusCode() + " for URL: " + monitoringUrl);
    }

    return parseMetrics(response.body());
}

private List<NpuMonitoring> parseMetrics(String responseBody) {
    List<NpuMonitoring> npuMonitoringList = new ArrayList<>();
    BufferedReader reader = new BufferedReader(new InputStreamReader(new java.io.ByteArrayInputStream(responseBody.getBytes())));

    String inputLine;
    try {
        while ((inputLine = reader.readLine()) != null) {
            if (!inputLine.startsWith("# TYPE") && !inputLine.startsWith("# HELP") && inputLine.contains("{")) {
                NpuMonitoring npuMonitoring = parseLine(inputLine);
                if (npuMonitoring != null) {
                    npuMonitoringList.add(npuMonitoring);
                }
            }
        }
    } catch (IOException e) {
        log.debug("获取npu监控失败！");
//        throw new RuntimeException("解析指标数据失败", e);
    }

    return npuMonitoringList;
}

private NpuMonitoring parseLine(String inputLine) {
    NpuMonitoring npuMonitoring = new NpuMonitoring();
    String[] parts = inputLine.split("\\{", 2);

    if (parts.length > 1) {
        npuMonitoring.setName(parts[0].trim());

        String[] middleParts = parts[1].split("\\}", 2);
        if (middleParts.length > 1) {
            String[] values = middleParts[1].trim().split(" ", 2);
            npuMonitoring.setValue(values[0]);
            extracted(npuMonitoring, middleParts[0].split(","));

            if (values.length > 1) {
                try {
                    npuMonitoring.setTime(Long.parseLong(values[1]));
                } catch (NumberFormatException e) {
                    npuMonitoring.setFormattedDate("Invalid timestamp");
                }
            } else {
                npuMonitoring.setFormattedDate(null);
            }
        }
    }
    return npuMonitoring;
}


    public  List<Map<String,String>> npuInformationss(String monitoringUrl){
        List<NpuMonitoring> npuMonitorings = NpuMonitoringList(monitoringUrl);
        List<Map<String, String>> maps = processNpuMonitoringList(npuMonitorings);
        return maps;
    }
    }


