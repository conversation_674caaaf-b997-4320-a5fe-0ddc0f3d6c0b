package supie.webadmin.app.util;
import lombok.Data;

import java.text.SimpleDateFormat;
import java.util.Date;


@Data
public class NpuMonitoring {
    private String name;

    private String modelName;


    private String value;
    private String formattedDate;  // 存储日期的字符串表示

    private String containerName;
    private String id;
    private String namespace;
    private String pcieBusInfo;
    private String podName;
    private String vdieId;


//    private Date time;
//
//
//    public void setTime(long timestamp) {
//        this.time = new Date(timestamp);
//    }

    public void setTime(long timestamp) {
        Date date = new Date(timestamp);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        this.formattedDate = formatter.format(date);
    }
}

