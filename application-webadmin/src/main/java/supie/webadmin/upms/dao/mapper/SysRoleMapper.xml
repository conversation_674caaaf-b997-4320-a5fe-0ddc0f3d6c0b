<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="supie.webadmin.upms.dao.SysRoleMapper">
    <resultMap id="BaseResultMap" type="supie.webadmin.upms.model.SysRole">
        <id column="role_id" jdbcType="BIGINT" property="roleId"/>
        <result column="role_name" jdbcType="VARCHAR" property="roleName"/>
        <result column="workbench_id" jdbcType="BIGINT" property="workbenchId"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="filterRef">
        <if test="sysRoleFilter != null">
            <if test="sysRoleFilter.roleName != null and sysRoleFilter.roleName != ''">
                <bind name= "safeRoleName" value= "'%' + sysRoleFilter.roleName + '%'"/>
                AND role_name LIKE #{safeRoleName}
            </if>
        </if>
    </sql>

    <select id="getSysRoleList" resultMap="BaseResultMap" parameterType="supie.webadmin.upms.model.SysRole">
        SELECT * FROM sys_role
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
