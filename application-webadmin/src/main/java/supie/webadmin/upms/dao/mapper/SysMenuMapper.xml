<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="supie.webadmin.upms.dao.SysMenuMapper">
    <resultMap id="BaseResultMap" type="supie.webadmin.upms.model.SysMenu">
        <id column="menu_id" jdbcType="BIGINT" property="menuId"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="menu_name" jdbcType="VARCHAR" property="menuName"/>
        <result column="menu_type" jdbcType="INTEGER" property="menuType"/>
        <result column="form_router_name" jdbcType="VARCHAR" property="formRouterName"/>
        <result column="online_form_id" jdbcType="BIGINT" property="onlineFormId"/>
        <result column="online_menu_perm_type" jdbcType="INTEGER" property="onlineMenuPermType"/>
        <result column="report_page_id" jdbcType="BIGINT" property="reportPageId"/>
        <result column="online_flow_entry_id" jdbcType="BIGINT" property="onlineFlowEntryId"/>
        <result column="show_order" jdbcType="INTEGER" property="showOrder"/>
        <result column="icon" jdbcType="VARCHAR" property="icon"/>
        <result column="extra_data" jdbcType="VARCHAR" property="extraData"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="data_belong_typ"  property="dataBelongType"/>
        <result column="deleted_flag"  property="deletedFlag"/>
        <result column="data_belong_type" jdbcType="VARCHAR" property="dataBelongType"/>
    </resultMap>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="supie.webadmin.upms.dao.SysMenuMapper.inputFilterRef"/>
        AND sys_menu.deleted_flag = ${@supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="sysMenuFilter != null">
            <if test="sysMenuFilter.menuId != null">
                AND sys_menu.menu_id = #{sysMenuFilter.menuId}
            </if>
            <if test="sysMenuFilter.dataBelongType != null">
                AND sys_menu.data_belong_type = #{sysMenuFilter.dataBelongType}
            </if>
        </if>
    </sql>

    <select id="getMenuListByUserId" resultMap="BaseResultMap">
        SELECT
            m.*
        FROM
            sys_user_role ur,
            sys_role_menu rm,
            sys_menu m
        <where>
            AND ur.user_id = #{userId}
            AND ur.role_id = rm.role_id
            AND rm.menu_id = m.menu_id
        </where>
        ORDER BY m.show_order
    </select>

    <select id="getMenuListByRoleIds" resultMap="BaseResultMap">
        SELECT
            m.*
        FROM
            sys_role_menu rm,
            sys_menu m
        <where>
            rm.role_id IN
            <foreach collection="roleIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            AND rm.menu_id = m.menu_id
        </where>
        ORDER BY m.show_order
    </select>

    <select id="countMenuCode" resultType="java.lang.Integer">
        <bind name= "safeMenuCode" value= "'%' + menuCode + '%'"/>
        SELECT COUNT(*) FROM sys_menu WHERE extra_data LIKE #{safeMenuCode}
    </select>

    <select id="getsysMenuicList" resultType="supie.webadmin.upms.model.SysMenu">
        SELECT * FROM sys_menu
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
