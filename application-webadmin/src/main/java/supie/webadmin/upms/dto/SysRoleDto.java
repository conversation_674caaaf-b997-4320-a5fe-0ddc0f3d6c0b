package supie.webadmin.upms.dto;

import supie.common.core.validator.UpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;

/**
 * 角色Dto。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Schema(description = "角色Dto")
@Data
public class SysRoleDto {

    /**
     * 角色Id。
     */
    @Schema(description = "角色Id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "角色Id不能为空！", groups = {UpdateGroup.class})
    private Long roleId;

    /**
     * 角色名称。
     */
    @Schema(description = "角色名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "角色名称不能为空！")
    private String roleName;

    /**
     * 与当前角色Id绑定的在线统计表单Id，主要用于用户登录后的工作台显示。
     */
    @Schema(description = "与当前角色Id绑定的在线统计表单Id，主要用于用户登录后的工作台显示")
    private Long workbenchId;
}
