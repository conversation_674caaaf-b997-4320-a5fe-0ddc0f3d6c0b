package supie.webadmin.upms.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.models.security.SecurityScheme;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 * @date 2025/6/6 11:02
 */
@Data
public class TaskCount {



    /**
     *  总任务数
     */
    @Schema(description = "总任务数")
    private  Integer totalTask;
    /**
     * 待提交
     */
    @Schema(description = "待提交")
    private Integer noCommit;
    /**
     * 排队中
     */
    @Schema(description = "排队中")
    private Integer queue;
    /**
     * 运行中
     */
    @Schema(description = "运行中")
    private Integer run;
    /**
     * 完成
     */
    @Schema(description = "完成")
    private Integer finish;
    /**
     * 停止
     */
    @Schema(description = "停止")
    private Integer stop;

    /*
    * 失败
    * */
    @Schema(description = "失败")
    private Integer failed;

    /*
    * 初始化
    * */
    @Schema(description = "初始化")
    private Integer pending;

    /*
    * 启动中
    * */
    @Schema(description = "启动中")
    private Integer start;

    /**
     * 异常
     */
    @Schema(description = "异常")
    private  Integer exception;
}
