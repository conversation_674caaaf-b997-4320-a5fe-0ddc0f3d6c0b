package supie.webadmin.upms.vo;

import supie.common.core.base.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * 角色VO。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Schema(description = "角色VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SysRoleVo extends BaseVo {

    /**
     * 角色Id。
     */
    @Schema(description = "角色Id")
    private Long roleId;

    /**
     * 角色名称。
     */
    @Schema(description = "角色名称")
    private String roleName;

    /**
     * 与当前角色Id绑定的在线统计表单Id，主要用于用户登录后的工作台显示。
     */
    @Schema(description = "与当前角色Id绑定的在线统计表单Id，主要用于用户登录后的工作台显示")
    private Long workbenchId;

    /**
     * 角色与菜单关联对象列表。
     */
    @Schema(description = "角色与菜单关联对象列表")
    private List<Map<String, Object>> sysRoleMenuList;
}
