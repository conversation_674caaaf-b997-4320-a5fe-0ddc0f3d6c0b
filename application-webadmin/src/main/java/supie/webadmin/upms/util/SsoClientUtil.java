package supie.webadmin.upms.util;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import supie.webadmin.config.SsoConfig;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @author: alexWang
 */
@Slf4j
public class SsoClientUtil {

    /**
     * 构建SSO登录URL
     */
//    public static String buildLoginUrl(String redirectUri) {
//        return StrUtil.format("{}?appCode={}&redirectUri={}", ssoServerUrl, appCode, redirectUri);
//    }

    /**
     * 验证SSO服务器返回的参数并获取用户信息
     */
    public static JSONObject validateAndGetUser(String requestAppCode, String token, String nonce, long timestamp, String signature, SsoConfig ssoConfig) {
        try {
            String curAppCode = requestAppCode;
            String curAppSecret = ssoConfig.getAppSecret();
            long curtimeoutSeconds = ssoConfig.getTimeoutSeconds();
            if (!requestAppCode.equals(ssoConfig.getAppCode())) {
                SsoConfig.SsoAppProperties app = ssoConfig.getAppClientByCode(requestAppCode);
                if (app == null) {
                    log.error("SSO验证失败: appCode不存在");
                    return null;
                }
                curAppCode = app.getAppCode();
                curAppSecret = app.getSecret();
                curtimeoutSeconds = app.getTimeoutSeconds();
            }

            // 本地验证时间戳
            long currentTime = System.currentTimeMillis() / 1000;
            if (currentTime - timestamp > curtimeoutSeconds) {
                log.error("SSO请求已过期");
                return null;
            }

            // 本地验证签名
            String expectedSignature = DigestUtil.md5Hex(curAppCode + token + nonce + timestamp + curAppSecret);
            if (!expectedSignature.equals(signature)) {
                log.error("SSO签名验证失败");
                return null;
            }
            // 生成随机数和时间戳
            nonce = RandomUtil.randomString(16);
            timestamp = System.currentTimeMillis() / 1000;
            signature = DigestUtil.md5Hex(curAppCode + token + nonce + timestamp + curAppSecret);
            // 请求SSO服务器验证token并获取用户信息
            Map<String, Object> params = new HashMap<>();
            params.put("appCode", curAppCode);
            params.put("token", token);
            params.put("nonce", nonce);
            params.put("timestamp", timestamp);
            params.put("signature", signature);
            String result = HttpUtil.createPost(ssoConfig.getServerUserUrl())
                    .header("Authorization", token)
                    .form(params)
                    .timeout((int) TimeUnit.SECONDS.toMillis(curtimeoutSeconds))
                    .execute().body();
            JSONObject jsonResult = JSONObject.parseObject(result);

            if (jsonResult.getBooleanValue("success")) {
                return jsonResult.getJSONObject("data");
            } else {
                log.error("SSO服务器验证失败: {}", jsonResult.getString("errorMessage"));
                return null;
            }
        } catch (Exception e) {
            log.error("SSO验证异常", e);
            return null;
        }
    }
}