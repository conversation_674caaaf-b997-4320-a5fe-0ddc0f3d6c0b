package supie.webadmin.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@Configuration
@ConfigurationProperties(prefix = "sso")
public class SsoConfig {

    private String ssoServerUrl;

    private String serverUserUrl;

    private String serverLogoutUrl;

    private String appCode;

    private String appSecret;

    private Long timeoutSeconds;

    private List<SsoAppProperties> apps;

    private List<SsoAppProperties> clients;
    private transient Map<String, SsoAppProperties> appMap;
    private transient Map<String, SsoAppProperties> appClientMap;

    @Data
    public static class SsoAppProperties {
        private String appCode;
        private String appName;
        private String appUrl;
        private String secret;
        private Integer timeoutSeconds = 300;
    }

    public SsoAppProperties getAppByCode(String appCode) {
        if (appMap == null) {
            appMap = apps.stream()
                    .collect(Collectors.toMap(SsoAppProperties::getAppCode, app -> app));
        }
        return appMap.get(appCode);
    }

    public SsoAppProperties getAppClientByCode(String appCode) {
        if (appClientMap == null) {
            appClientMap = clients.stream()
                    .collect(Collectors.toMap(SsoAppProperties::getAppCode, app -> app));
        }
        return appClientMap.get(appCode);
    }
}