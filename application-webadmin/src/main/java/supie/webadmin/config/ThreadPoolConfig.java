package supie.webadmin.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import jakarta.annotation.PreDestroy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 线程池配置类
 */
@Configuration
public class ThreadPoolConfig {
    
    private static final Logger log = LoggerFactory.getLogger(ThreadPoolConfig.class);
    // 添加成员变量来保存线程池引用
    private ExecutorService taskPoolExecutor;

    /**
     * 任务处理线程池
     */
    @Bean("poolExecutor")
    public ExecutorService poolExecutor() {
        // 获取可用处理器数量，用于动态设置线程池大小
        int processors = Runtime.getRuntime().availableProcessors();
        // 核心线程数设置为处理器数量
        int corePoolSize = Math.max(5, processors);
        // 最大线程数设置为处理器数量的2倍
        int maxPoolSize = corePoolSize * 2;
        log.info("创建任务线程池: 核心线程数={}, 最大线程数={}", corePoolSize, maxPoolSize);
        return  this.taskPoolExecutor=new ThreadPoolExecutor(
                corePoolSize,                  // 核心线程数
                maxPoolSize,                   // 最大线程数
                60L,                           // 空闲线程存活时间
                TimeUnit.SECONDS,              // 时间单位
                new LinkedBlockingQueue<>(200), // 任务队列
                new ThreadFactoryBuilder()
                        .setNameFormat("task-processor-%d")  // 线程名称格式
                        .setDaemon(false)                    // 非守护线程
                        .setUncaughtExceptionHandler((thread, throwable) -> {
                            // 记录未捕获的异常
                            log.error("线程执行异常: {}", throwable.getMessage(), throwable);
                        })
                        .setPriority(Thread.NORM_PRIORITY)   // 设置线程优先级
                        .build(),
                new ThreadPoolExecutor.CallerRunsPolicy()    // 拒绝策略：由调用线程处理
        );
    }

    /**
     * 应用关闭时关闭线程池 - 修改为无参方法
     */
    @PreDestroy
    public void shutdown() {
        shutdownThreadPool(this.taskPoolExecutor, "taskPoolExecutor");
    }

    /**
     * 优雅关闭线程池
     */
    private void shutdownThreadPool(ExecutorService pool, String poolName) {
        if (pool == null) return;

        try {
            // 拒绝新任务
            pool.shutdown();
            log.info("正在关闭线程池: {}", poolName);

            // 等待现有任务完成
            if (!pool.awaitTermination(60, TimeUnit.SECONDS)) {
                // 强制关闭
                pool.shutdownNow();
                log.warn("线程池未能在60秒内关闭，强制关闭: {}", poolName);

                // 再次等待
                if (!pool.awaitTermination(60, TimeUnit.SECONDS)) {
                    log.error("线程池未能完全关闭: {}", poolName);
                }
            }

            log.info("线程池已关闭: {}", poolName);
        } catch (InterruptedException e) {
            // 重新设置中断标志
            Thread.currentThread().interrupt();
            // 强制关闭
            pool.shutdownNow();
            log.error("关闭线程池时被中断: {}", poolName, e);
        }
    }
}