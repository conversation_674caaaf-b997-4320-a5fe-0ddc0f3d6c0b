user nginx;
worker_processes auto;

error_log /var/log/nginx/error.log notice;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    sendfile on;
    keepalive_timeout 65;

    # 启用 gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    server {
        listen 80;
        server_name ************ localhost;

        # 访问日志
        access_log /var/log/nginx/access.log main;
        error_log /var/log/nginx/error.log;

        # 根路径 - 测试用
        location / {
            return 200 "Nginx is working! Ready to proxy to lab.\n";
            add_header Content-Type text/plain;
        }

        # 代理 /lab 到后端 JupyterLab 服务
        location /lab {
            proxy_pass http://127.0.0.1:8810;
            proxy_http_version 1.1;
            
            # 基础 HTTP 头设置
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;
            
            # WebSocket 支持
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            
            # 缓冲设置
            proxy_buffering off;
            proxy_cache_bypass $http_upgrade;
            
            # 超时设置
            proxy_connect_timeout 60s;
            proxy_send_timeout 120s;
            proxy_read_timeout 120s;
        }

        # 代理 /login 到后端（处理登录重定向）
        location /login {
            proxy_pass http://127.0.0.1:8810;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 代理 /api 到后端（API 请求）
        location /api {
            proxy_pass http://127.0.0.1:8810;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 代理静态资源
        location /static {
            proxy_pass http://127.0.0.1:8810;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
        }

        # 错误页面
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            return 500 "Internal Server Error\n";
            add_header Content-Type text/plain;
        }
    }
}
