<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>supie</groupId>
    <artifactId>gy_sch_be</artifactId>
    <version>1.0.0</version>
    <name>gy_sch_be</name>
    <packaging>pom</packaging>

    <properties>
        <spring-boot.version>3.1.6</spring-boot.version>
        <spring-boot-admin.version>3.1.6</spring-boot-admin.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <java.version>17</java.version>
        <application.name>gy_sch_be</application.name>
        <!-- 工具库版本 -->
        <qlexpress.version>3.3.4</qlexpress.version>
        <joda-time.version>2.10.13</joda-time.version>
        <guava.version>20.0</guava.version>
        <commons-io.version>2.11.0</commons-io.version>
        <commons-collections4.version>4.4</commons-collections4.version>
        <common-csv.version>1.8</common-csv.version>
        <httpclient5.version>5.2.2</httpclient5.version>
        <poi-ooxml.version>5.2.0</poi-ooxml.version>
        <hutool.version>5.8.23</hutool.version>
        <jjwt.version>0.12.3</jjwt.version>
        <fastjson.version>1.2.83</fastjson.version>
        <bean.query.version>1.1.5</bean.query.version>
        <caffeine.version>2.9.3</caffeine.version>
        <lombok.version>1.18.20</lombok.version>
        <hibernate-validator.version>8.0.1.Final</hibernate-validator.version>
        <flowable.version>7.0.1</flowable.version>
        <rocketmq.version>2.1.1</rocketmq.version>
        <redisson.version>3.15.4</redisson.version>
        <minio.version>8.4.5</minio.version>
        <knife4j.version>4.5.0</knife4j.version>
        <!-- 数据库工具版本 -->
        <druid.version>1.2.16</druid.version>
        <mybatisplus.version>3.5.4.1</mybatisplus.version>
        <pagehelper.version>1.4.7</pagehelper.version>
        <groovy.version>3.0.19</groovy.version>
        <!-- ssh远程连接 -->
        <jsch.version>0.1.55</jsch.version>
        <docker-java.version>3.2.13</docker-java.version>
    </properties>

    <modules>
        <module>application-webadmin</module>
        <module>common</module>
    </modules>

    <dependencies>
        <!--web 模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!-- 日志模块 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-logging</artifactId>
        </dependency>
        <!-- aop模块 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <!-- 缓存模块 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>
        <!--配置文件处理器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <!--加密-->
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-crypto</artifactId>
        </dependency>
        <!--监控-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <!--监控客户端-->
        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-client</artifactId>
            <version>${spring-boot-admin.version}</version>
        </dependency>
        <!-- api参数验证 -->
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>${hibernate-validator.version}</version>
        </dependency>
        <!--Lombok-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <!--测试依赖-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- Docker操作相关依赖 -->
        <dependency>
            <groupId>com.github.docker-java</groupId>
            <artifactId>docker-java</artifactId>
            <version>${docker-java.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.docker-java</groupId>
            <artifactId>docker-java-api</artifactId>
            <version>${docker-java.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.docker-java</groupId>
            <artifactId>docker-java-core</artifactId>
            <version>${docker-java.version}</version>
        </dependency>
        <!-- docker java httpclient -->
        <dependency>
            <groupId>com.github.docker-java</groupId>
            <artifactId>docker-java-transport-httpclient5</artifactId>
            <version>3.2.13</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>8.0.22</version>
            </dependency>

            <dependency>
                <groupId>com.github.mwiede</groupId>
                <artifactId>jsch</artifactId>
                <version>0.2.27</version> <!-- 使用更新的JSch版本以确保SSH连接稳定性 -->
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
                <filtering>false</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.0</version>
                <configuration>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                    <target>${maven.compiler.target}</target>
                    <source>${maven.compiler.source}</source>
                    <encoding>UTF-8</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
